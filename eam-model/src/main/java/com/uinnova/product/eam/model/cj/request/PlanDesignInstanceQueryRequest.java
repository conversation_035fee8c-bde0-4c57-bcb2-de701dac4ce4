package com.uinnova.product.eam.model.cj.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 方案设计分页查询请求参数
 * <AUTHOR>
 */
@Data
public class PlanDesignInstanceQueryRequest {

    /**
     * 每页个数
     */
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum;

    /**
     * 方案类型id
     */
    private Long typeId;

    /**
     * 方案设计模板businessId
     */
    private Long templateId;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 搜索词
     */
    private String word;

    private Long systemId;
}
