package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.AssetType;
import lombok.Data;

@Comment("引用ci关系的资产实体")
@Data
public class CIRltQuotedAssert {

    public CIRltQuotedAssert(String id, AssetType assetType, String name, Object detail) {
        this.id = id;
        this.assetType = assetType;
        this.name = name;
        this.detail = detail;
    }

    @Comment("资产id")
    private String id;
    @Comment("资产类型")
    private AssetType assetType;
    @Comment("资产名称")
    private String name;
    @Comment("资产信息")
    private Object detail;
    @Comment("作者")
    private String creator;
    @Comment("作者名称")
    private String creatorName;
}
