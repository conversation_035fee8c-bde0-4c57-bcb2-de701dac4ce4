package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.rule.CcCiTagRuleItem;

/**
 * 构造不需要保存的标签规则格式的条件查询CI需要的条件
 * <AUTHOR>
 *
 */
public class TagRuleItemCdt implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	@Comment("分类ID")
	private Long classId;
	
	@Comment("当前分下的规则集合")
	private List<CcCiTagRuleItem> rules;

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public List<CcCiTagRuleItem> getRules() {
		return rules;
	}

	public void setRules(List<CcCiTagRuleItem> rules) {
		this.rules = rules;
	}

	

}
