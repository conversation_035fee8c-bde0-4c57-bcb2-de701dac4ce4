package com.uinnova.product.eam.model.dmv;

import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel("属性值")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttrInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("键名称")
    private String key;

    @Comment("属性值")
    private String value;

}
