package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.AssetType;
import lombok.Data;

@Comment("关系删除消息通知")
@Data
public class CIRltDelMsgVo {

    @Comment("消息id")
    private Long noticeId;
    @Comment("资产类型")
    private AssetType assetType;
    @Comment("资产id")
    private String sourceId;
    @Comment("资产名称")
    private String sourceName;
    @Comment("资产版本")
    private Integer version;
    @Comment("接收者")
    private String userLoginCode;
    @Comment("删除操作用户")
    private Long delUserId;
    @Comment("删除操作用户姓名")
    private String delUserName;
}
