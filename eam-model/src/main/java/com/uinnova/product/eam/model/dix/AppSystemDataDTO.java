package com.uinnova.product.eam.model.dix;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 应用系统实体及实体属性返回体
 * <AUTHOR>
 */
@Data
public class AppSystemDataDTO implements Serializable {
    @Comment("子系统id")
    private String systemId;
    @Comment("版本")
    private Integer version;
    @Comment("实体集合")
    private List<AppSystemEntityVO> entityList = new ArrayList<>();
    @Comment("删除的实体id集合")
    private List<String> delEntityIds = new ArrayList<>();
    @Comment("实体间关系集合")
    private List<AppSystemRltVO> rltList = new ArrayList<>();
    @Comment("删除的实体间关系集合")
    private List<String> delRltIds = new ArrayList<>();

    public AppSystemDataDTO() {
    }

    public AppSystemDataDTO(String systemId) {
        this.systemId = systemId;
    }
}
