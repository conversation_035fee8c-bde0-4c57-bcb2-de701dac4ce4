package com.uinnova.product.eam.model.enums;

public enum ApproveCategory {

    // todo 补充 processDefinitionKey 流程定义
    IT_VIEW_ARTIFACT(1,"IT视图制品审批", "xw_it_diagram_approve"),
    BUSINESS_VIEW_ARTIFACT(2,"业务视图制品审批",""),
    DATA_VIEW_ARTIFACT(3,"数据视图制品审批", "");

    public int processClassId;
    public String processClassName;
    public String processDefinitionKey;

    ApproveCategory(int processClassId, String processClassName, String processDefinitionKey) {
        this.processClassId = processClassId;
        this.processClassName = processClassName;
        this.processDefinitionKey = processDefinitionKey;
    }
}
