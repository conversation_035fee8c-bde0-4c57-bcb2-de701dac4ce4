package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("交付物模板类型表[UINO_CJ_Template_type]")
public class TemplateType {

    private Long id;

    private String parentId;

    private String typeName;

    private String creator;

    private Integer sort;

    //交付物模板类型是否删除 0表示没有删除 1表示删除
    private Integer status;

}
