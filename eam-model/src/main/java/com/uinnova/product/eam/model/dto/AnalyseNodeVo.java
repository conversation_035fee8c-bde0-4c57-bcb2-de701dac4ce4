package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.AnalyseLeaf;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 关联分析路径图-节点
 * <AUTHOR>
 * @date 2022/6/6
 */
@Accessors(chain = true)
@Data
public class AnalyseNodeVo extends CcCiClass {
    @Comment("源端节点or目标端节点")
    private AnalyseLeaf leaf;
    @Comment("根节点")
    private Boolean root = false;
    @Comment("坐标")
    private String loc;
    @Comment("宽")
    private Double width;
    @Comment("高")
    private Double height;

}
