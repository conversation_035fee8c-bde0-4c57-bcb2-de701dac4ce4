package com.uinnova.product.eam.model.cj.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 方案历史版本
 * @author: Lc
 * @create: 2022-11-25 09:54
 */
@Data
public class PlanHistoryVersionVo implements Serializable {

    private Long id;

    private String name;

    private String creator;

    private Long publishTime;

    private String version;

    /** 是否存在最新的历史版本和当前版本不一致，true：存在 */
    private Boolean status;

    private Integer dirType;

    public PlanHistoryVersionVo() {}

    public PlanHistoryVersionVo(Boolean status) {
        this.status = status;
    }
}
