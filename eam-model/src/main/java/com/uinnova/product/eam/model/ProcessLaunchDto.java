package com.uinnova.product.eam.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/11/05 10:27
 */
@Data
public class ProcessLaunchDto {

    @ApiModelProperty(value = "流程定义key",required = true)
    private String processDefinitionKey;

    @ApiModelProperty(value = "流程实例名称",required = true)
    private String processInstanceName;

    /**
     * 当前用户code
     */
    @JSONField(serialize = false)
    private String userId;

    @ApiModelProperty(value = "当前流程的ciCode",required = true)
    private String businessKey;

    /**
     * 发起人code
     */
    @JSONField(serialize = false)
    private String owner;

    /**
     * 下一个审批人
     */
    @ApiModelProperty(value = "下一个审批人id(可多个   xxx,sss)",required = true)
    private String nextUserIds;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;


}
