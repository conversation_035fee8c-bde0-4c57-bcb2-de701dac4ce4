package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * @description:
 * @author: Lc
 * @create: 2022-01-06 17:57
 */
@Data
public class PlanTemplateChapterTreeVo implements Serializable {

    @Comment("主键id")
    private Long id;
    @Comment("章节名称")
    private String chapterName;
    @Comment("层级")
    private Integer level;
    @Comment("排序号")
    private Double sortNum;
    @Comment("层级是否展开")
    private boolean expand = true;
    @Comment("父id")
    private Long parentId;
    @Comment("序号")
    private String serialNum;
    @Comment("全名称")
    private String fullName;

    @Comment("所属方案模板id")
    private Long planTemplateId;

    private List<PlanTemplateChapterTreeVo> child;

    @Comment("引入章节信息")
    private List<PlanTemplateIntroduceChapterVo> planTemplateIntroduceChapterVoList;

    private String chapterIdPath;

    private String planAndChapterName;

    @Comment("模板是否必填  false:否 true:是")
    private Boolean required;

}
