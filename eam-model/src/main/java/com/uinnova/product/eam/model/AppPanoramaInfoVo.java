package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.List;

/**
 * 应用全景信息VO
 * <AUTHOR>
 */
@Data
public class AppPanoramaInfoVo {
    @Comment("主键")
    private Long id;
    @Comment("资产信息")
    private CcCiInfo ciInfo;
    @Comment("状态，1：草稿，2：发布")
    private Integer state;
    @Comment("关联视图")
    private List<String> diagramIds;
    @Comment("关联方案")
    private List<Long> planIds;
    @Comment("架构决策")
    private List<Long> decisionIds;

    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;
}
