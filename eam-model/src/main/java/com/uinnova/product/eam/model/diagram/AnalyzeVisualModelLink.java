package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 关联分析元模型link节点实体
 * <AUTHOR>
 * @date 2022/5/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalyzeVisualModelLink extends VisualModelLink {

    @Comment("完整路径：源端classId-关系classId-目标端classId")
    private VisualModelNode toNode;

    @Comment("显示名称")
    private VisualModelNode fromNode;

}
