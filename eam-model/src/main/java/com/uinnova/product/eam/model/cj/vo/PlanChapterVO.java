package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 方案章节VO
 *
 * <AUTHOR>
 */
@Data
public class PlanChapterVO {

    public PlanChapterVO() {
        // 默认为true
        this.expand = true;
    }

    /**
     * 章节 id
     */
    private Long id;

    /**
     * 章节是否来自模板
     */
    private Boolean isFromTemplate;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 带附加信息的章节名称
     */
    private String fullName;

    /**
     * 章节层级
     */
    private Integer level;

    /**
     * 章节序号，前端显示
     */
    private String serialNum;

    /**
     * 是否展开
     */
    private Boolean expand;

    /**
     * 章节是否必填
     */
    private Boolean required;

    /**
     * 章节说明
     */
    private String chapterDesc;

    /**
     * 父章节id
     */
    private Long parentId;

    /**
     * 所属方案的id
     */
    private Long planId;

    /**
     * 章节模板的id
     */
    private Long templateId;

    /**
     * 方案章节详情
     */
    private ChapterContextVO context;

    /**
     * 子章节
     */
    private List<PlanChapterVO> childChapterList;

    /**
     * 是否已加锁，1：加锁， 0：未加锁
     */
    private Integer lock;
    @Comment("引入章节信息")
    private List<PlanTemplateIntroduceChapterVo> planTemplateIntroduceChapterVoList;

    /**
     * 模板是否已被删除 1: 已删除
     */
    private Integer templateDelete;

    /**
     * 是否存在章节协作 0：不存在  1：存在
     */
    private Integer existCollaborate;

    /**
     * 是否已完成协作 0：未完成 1：已完成
     */
    private Integer completeCollaborate;

    /** 按钮标识 0：不可见 1：可见 */
    private Integer buttonSign;
}
