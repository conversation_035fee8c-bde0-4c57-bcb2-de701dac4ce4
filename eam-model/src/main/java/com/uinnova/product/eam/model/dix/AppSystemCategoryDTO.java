package com.uinnova.product.eam.model.dix;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 应用系统对接目录响应实体
 * <AUTHOR>
 */
@Data
@Accessors
public class AppSystemCategoryDTO implements Serializable {
    @Comment("目录id")
    private Long id;
    @Comment("目录名称")
    private String name;
    @Comment("父目录id")
    private Long parentId;
    @Comment("目录层级")
    private Integer level;
    @Comment("是否在启用：1是0否")
    private Integer enable;

    public AppSystemCategoryDTO() {
    }

    public AppSystemCategoryDTO(Long id, String name, Long parentId, Integer level, Integer enable) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.level = level;
        this.enable = enable;
    }
}
