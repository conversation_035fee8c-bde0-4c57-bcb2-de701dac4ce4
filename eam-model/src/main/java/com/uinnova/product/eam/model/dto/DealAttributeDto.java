package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据建模实体保存、删除属性
 * <AUTHOR>
 */
@Data
public class DealAttributeDto {

    @Comment("库类型")
    private LibType libType = LibType.PRIVATE;

    @Comment("视图所属用户code")
    private String ownerCode;

    @Comment("视图id")
    private String diagramId;

    @Comment("视图sheet页id")
    private String sheetId;

    @Comment("是否复制")
    private Boolean copyFlag = false;

    @Comment("删除继承实体属性标识")
    private Boolean delFlag = false;

    @Comment("实体ciCode")
    private String ciCode;

    @Comment("实体属性ciCode")
    private List<String> attrCode = new ArrayList<>();

    @Comment("ci")
    private CcCiInfo ciInfo;

    @Comment("ciCode集合")
    private List<String> ciCodeList = new ArrayList<>();

    @Comment("更新的属性信息")
    private List<CcCiInfo> ciList = new ArrayList<>();

}
