package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class HtAppSystemRltDto {
    @Comment("应用名称")
    private String appSystemName;

    @Comment("关系类型")
    private String rltClassName;

    @Comment("关系描述")
    private String rltClassDesc;

    @Comment("源端对象")
    private String sourceCiName;

    @Comment("源端分类")
    private String sourceClassName;

    @Comment("目标端对象")
    private String targetCiName;

    @Comment("目标端分类")
    private String targetClassName;

    @Comment("修改类型")
    private CHANGETYPE type;

    @Comment("修改前")
    private String attrBefore;

    @Comment("修改后")
    private String attrAfter;

}
