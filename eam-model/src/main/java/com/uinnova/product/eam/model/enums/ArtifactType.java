package com.uinnova.product.eam.model.enums;

/**
 * 制品分栏类型
 * <AUTHOR>
 * @date 2022/2/24
 */
public enum ArtifactType {
    /**
     * 图例配置(分类/图例)
     */
    CI_TYPE(1),
    /**
     * 资产数据配置项
     */
    ASSET_TYPE(2),
    /**
     * 关系配置项
     */
    RLT_TYPE(3),
    /**
     * 模板配置项
     */
    TEMPLATE_TYPE(4),

    /**
     * 区分分栏（type=1和type=2）
     */
    DISTINGUISH_TYPE(-1);

    int val;

    ArtifactType(int val) {
        this.val = val;
    }

    public int val() {
        return this.val;
    }
}
