package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 矩阵分析表格实体
 * <AUTHOR>
 */
@Data
public class EamMatrixAnalysisVo implements Serializable {

    @Comment("矩阵类型:1=关系矩阵,2=属性矩阵")
    private Integer matrixType;

    @Comment("横向分类ID")
    private Long rowId;

    @Comment("横向分类名")
    private String rowName;

    @Comment("纵向分类ID")
    private Long colId;

    @Comment("纵向分类名")
    private String colName;

    @Comment("横向表头数据集")
    private List<CiSimpleInfoVo> rowTitleList = new ArrayList<>();

    @Comment("纵向表头数据集")
    private List<CiSimpleInfoVo> colTitleList = new ArrayList<>();

    @Comment("关系矩阵")
    private List<List<RltSimpleInfoVo>> rltMatrix;

    @Comment("属性矩阵")
    private List<List<List<CiSimpleInfoVo>>> attrMatrix;
}
