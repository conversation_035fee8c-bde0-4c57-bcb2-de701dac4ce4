package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.binary.jdbc.Page;
import lombok.Data;

@Comment("关系删除对比")
@Data
public class CIRltDropCompare {

    public CIRltDropCompare(Boolean rltDel) {
        this.rltDel = rltDel;
    }

    @Comment("是否存在关系被删除")
    private Boolean rltDel;
    @Comment("关系被删除明细")
    private Page<CIRltDropDetail> rltDropDetails;
}
