package com.uinnova.product.eam.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: LC
 * @date: 2024/06/05
 **/
@Data
public class KcpInfoVo implements Serializable {

    /** 关键控制点编号 */
    private String keyContPointNum;

    /** 关键控制点 */
    private String keyContPoint;

    /** 控制目标 */
    private String controlTarget;

    /** 控制方法 */
    private String controlMethod;

    /** 关键子流程 */
    private String keySubProcesses;

    /** 关联活动 */
    private String relatedActivities;

    /** 关联风险 */
    private String relatedRisk;
}
