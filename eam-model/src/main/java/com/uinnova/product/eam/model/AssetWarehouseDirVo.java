package com.uinnova.product.eam.model;

import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AssetWarehouseDirVo extends AssetWarehouseDir {
    @ApiModelProperty(value="孩子节点")
    private List<AssetWarehouseDirVo> children;;

}
