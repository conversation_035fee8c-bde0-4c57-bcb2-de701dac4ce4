package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class AppSystemQueryVo {

    private Integer pageNum =1;

    private Integer pageSize =20;

    @Comment("分类classCode")
    private String classCode;

    @Comment("时间参数")
    private List<TimeAndLikeQueryParam> timeParams;

    @Comment("字符串，文本")
    private String like;
    /**
     * 模糊搜索字段
     */
    private List<String> words;

    @Comment("字符串，文本模糊查询")
    private List<TimeAndLikeQueryParam> likeParams;

    @Comment("属性过滤条件")
    private List<AppSystemFilterVo> filters;

    @Comment("私有库CiCode 特殊处理架构决策草稿新建，查询私有库")
    private List<String> decisionCiCodes;

    @Comment("查询关联资产数量")
    private Boolean queryAssetCount = Boolean.FALSE;

    @Comment("卡片ID")
    private Long appSquareConfId;

}
