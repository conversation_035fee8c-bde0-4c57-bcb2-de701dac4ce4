package com.uinnova.product.eam.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
public class FlowOperationStatisticsDto {

    @ApiModelProperty(value = "流程ci code", required = true)
    private String ciCode;

    @ApiModelProperty(value = "统计开始时间")
    private String startDate = "2000-01-01";

    @ApiModelProperty(value = "统计结束时间")
    private String endDate = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);

    @ApiModelProperty(value = "统计频次")
    private String frequencyStatistics = "year";

    @ApiModelProperty(value = "系统来源")
    private String sourceId;
}


