package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据建模批量属性处理返回实体
 * <AUTHOR>
 */
@Data
public class DataModelBatchResp {
    @Comment("实体属性所属分类")
    private CcCiClass attributeCiClass;
    @Comment("实体属性的属性定义")
    private List<CcCiAttrDef> attributeDefs;
    @Comment("继承节点集合：实体ciCode-继承属性list")
    private Map<String, List<CcCiInfo>> inheritMap = new HashMap<>();
    @Comment("删除节点集合：实体ciCode-删除属性ciCode集合")
    private Map<String, List<String>> delMap = new HashMap<>();
}
