package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/24 16:45
 */
@Data
public class FlowProcessSystemTreeDto implements Comparable<FlowProcessSystemTreeDto>{

    private long ciId;

    private String ciCode;

    private String flowSystemName;

    private String flowCode;

    private String flowSystemType;

    private Boolean canSign = Boolean.FALSE;

    /**
     * 卡片id，这里是复用资产管理的接口
     */
    private Long appSquareConfId;

    private Integer publishStatus;


    /**
     * 流程审批状态:未发布:0  审批中:1  待发布:2  已发布:3  升版中:4
     */
    private int processApprovalStatus = 0;

    /**
     * 流程能否编辑的状态:可编辑:0  不可编辑:1
     */
    private int updateStatus = 0;



    private CcCiInfo ciInfo;

    private Map<String, Object> listMap;

    /**
     * CI属性
     */
    private Map<String, String> attrs;

    private Integer currentChildrenFlowCount;

    private Collection<FlowProcessSystemTreeDto> child;


    public void addListMap(String key, Object value) {
        if (listMap == null) {
            listMap = new HashMap<>();
        }
        if (Objects.isNull(value)) {
            value = "";
        }
        listMap.put(key, value);
    }

    @Override
    public int compareTo(FlowProcessSystemTreeDto o) {

        try {
            String[] parts1 = flowCode.split("\\.");
            String[] parts2 = o.getFlowCode().split("\\.");
            int maxLength = Math.max(parts1.length, parts2.length);
            for (int i = 0; i < maxLength; i++) {
                int part1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
                int part2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
                if (part1 > part2) {
                    return 1;
                } else if (part1 < part2) {
                    return -1;
                }
            }
            return -1;
        }catch (NumberFormatException numberFormatException){
            if(this.flowCode.compareTo(o.getFlowCode())!=0){
                return this.flowCode.compareTo(o.getFlowCode());
            }else {
                return -1;
            }
        }
    }

}
