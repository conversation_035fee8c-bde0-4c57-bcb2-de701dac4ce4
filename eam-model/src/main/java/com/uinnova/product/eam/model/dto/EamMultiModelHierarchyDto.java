package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamMultiModelType;
import lombok.Data;

/**
 * 模型层级配置VO
 * <AUTHOR>
 */
@Data
public class EamMultiModelHierarchyDto {
    @Comment("主键id")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("模型树名称")
    private String name;

    @Comment("模型树类型")
    @Deprecated
    private EamMultiModelType type;

    @Comment("模型树类型")
    private Integer modelType;

    @Comment("模型树描述")
    private String describe;

    @Comment("是否发布   0=未发布（默认），1=已发布")
    private Integer releaseState;

    @Comment("是否删除：0删除,1正常")
    private Integer dataStatus;

    @Comment("创建时间")
    private Long createTime;

    @Comment("更改时间")
    private Long modifyTime;
}
