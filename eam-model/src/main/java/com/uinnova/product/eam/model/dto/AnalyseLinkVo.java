package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.AnalyseLeaf;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 关联分析路径图-路径
 * <AUTHOR>
 * @date 2022/6/6
 */
@Accessors(chain = true)
@Data
public class AnalyseLinkVo extends CcCiClass {
    @Comment("关系线标识：源端classId-关系classId-目标端classId")
    private String code;
    @Comment("起始节点key")
    private Long from;
    @Comment("结束节点key")
    private Long to;
    @Comment("层级")
    private Integer level;
    @Comment("点集合")
    private List<Double> points;
    @Comment("方向")
    private AnalyseLeaf leaf;
}
