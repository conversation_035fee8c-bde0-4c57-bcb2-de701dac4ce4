package com.uinnova.product.eam.model.vo;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.enums.CIRltDelType;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class CIRltDelMsgParam {

    private List<ESDiagram> diagrams;
    private Map<String, List<CIRltDropDetail>> diagramRltMap;
    private List<EamMatrixInstance> matrixInstances;
    private Map<Long, List<CIRltDropDetail>> matrixRltMap;
    private String opUser;
    private CIRltDelType delType;
    private String relateAssertId;
    private String relateAssertName;
    private String relateAssertType;
}
