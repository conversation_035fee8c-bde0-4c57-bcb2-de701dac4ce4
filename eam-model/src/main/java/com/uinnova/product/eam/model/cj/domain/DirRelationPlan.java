package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;

/**
 * @description: 资产文件夹关联方案
 * @author: Lc
 * @create: 2022-03-17 11:07
 */
@Data
public class DirRelationPlan extends BaseEntity implements Condition {

    private Long dirId;

    private Long planId;

    private String ownerCode;
}
