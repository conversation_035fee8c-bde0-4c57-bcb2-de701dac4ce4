package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class CheckResultInfo {
    @Comment("分类名称")
    private String className;
    @Comment("制品数量校验-限制信息")
    private String productNumInfo;
    @Comment("ci主键")
    private String ciPrimaryKey;
    @Comment("ci必填项校验-未填写的字段信息")
    private String requiredFieldInfo;
    @Comment("设计库主键冲突校验-私有库业务主键")
    private String privateCiPrimaryKey;
    @Comment("设计库主键冲突校验-设计库业务主键")
    private String designCiPrimaryKey;
    @Comment("私有库对象icon")
    private String privateIcon;
    @Comment("设计库对象icon")
    private String designIcon;
    @Comment("视图版本校验-视图当前版本号")
    private String currentVersion;
    @Comment("视图版本校验-仓库视图最新版本号")
    private String latestVersion;
    @Comment("模型父级目录存在校验-校验信息")
    private String modelPlvlExistInfo;


}
