package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import com.uinnova.product.eam.model.cj.vo.QuestionAnswerVO;
import lombok.Data;

import java.util.List;

/**
 * @description: 问题
 * @author: Lc
 * @create: 2022-03-01 18:46
 */
@Data
@Comment("方案章节问题")
public class PlanChapterQuestion extends BaseEntity implements Condition {

    @Comment("方案id")
    private Long planId;

    @Comment("方案章节id")
    private Long planChapterId;

    @Comment("方案章节内容id")
    private Long planChapterContextId;

    @Comment("问题")
    private String question;

    @Comment("回复列表")
    private List<QuestionAnswerVO> answerList;

    @Comment("状态 0:待整改 1:已整改 2:无需整改 3:例外问题")
    private Integer questionState;

    @Comment("验证结果 0:待验证 1:通过 2:不通过")
    private Integer checkResult;

    @Comment("批注id")
    private Long annotationId;

    @Comment("流程实例id")
    private String processInstanceId;

    @Comment("任务定义主键")
    private String taskDefinitionKey;

    @Comment("用户Code")
    private String creator;
}
