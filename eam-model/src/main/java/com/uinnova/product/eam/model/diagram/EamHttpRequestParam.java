package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.Map;

@Comment("请求相关信息")
public class EamHttpRequestParam implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("请求的URL")
	private String url;
	
	@Comment("请求方式")
	private String requestMethod;
	
	@Comment("请求头")
	private Map<String, String> headers;
	
	@Comment("url后面追加的请求参数")
	private String urlparams;
	
	@Comment("bean请求参数")
	private Map<String,Object> formbean;
	@Comment("post传递的form文件")
	private MultipartFile Files;

	public MultipartFile getFiles() {
		return Files;
	}

	public void setFiles(MultipartFile files) {
		Files = files;
	}

	public String getUrlparams() {
		return urlparams;
	}

	public void setUrlparams(String urlparams) {
		this.urlparams = urlparams;
	}

	public Map<String, Object> getFormbean() {
		return formbean;
	}

	public void setFormbean(Map<String, Object> formbean) {
		this.formbean = formbean;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getRequestMethod() {
		return requestMethod;
	}

	public void setRequestMethod(String requestMethod) {
		this.requestMethod = requestMethod;
	}

	public Map<String, String> getHeaders() {
		return headers;
	}

	public void setHeaders(Map<String, String> headers) {
		this.headers = headers;
	}
	
	
}
