package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.AutoGraphVo;
import com.uinnova.product.eam.model.enums.GraphType;
import lombok.Data;

import java.util.List;

@Data
@Comment("dify智能绘图-上下文关系图")
public class DFAutoContextGraphParam {

    @Comment("中心元素")
    private String centerElement;
    @Comment("关联关系")
    private List<String> relations;
    @Comment("制图分类")
    private GraphType graphType;
    @Comment("布局")
    private DFAutoContextGraphLayout layout;
    @Comment("样式")
    private AutoGraphVo.Style style;
    @Comment("失败信息")
    private String failureMsg;
}
