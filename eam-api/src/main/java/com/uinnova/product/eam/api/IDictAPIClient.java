package com.uinnova.product.eam.api;

import com.uinnova.product.eam.base.model.DictInfo;

import java.util.List;
import java.util.Map;

public interface IDictAPIClient {


    List<DictInfo> selectListByType(String codeType, String parentCode, String className);

    Map<String, List<DictInfo>> selectGroupList(List<String> codeTypes, String className);

    Map<String, String> getAllInfo(List<String> codeTypes,String className);
}
