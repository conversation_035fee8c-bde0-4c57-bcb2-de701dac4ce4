package com.uinnova.product.eam.local.asset;

import com.binary.core.exception.BinaryException;
import com.uinnova.product.eam.api.IArchitecturalApiClient;
import com.uinnova.product.eam.base.model.AttrConfInfo;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.model.asset.ArchitecturalDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalNameCheckDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalResolutionDTO;
import com.uinnova.product.eam.model.asset.SearchArchitecturalDTO;
import com.uinnova.product.eam.service.asset.ArchitecturalSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ArchitecturalSvcLocal implements IArchitecturalApiClient {

    @Resource
    private ArchitecturalSvc architecturalSvc;

    @Override
    public Long createArchitecturalResolution(ArchitecturalDTO architecturalDTO) {
        return architecturalSvc.createArchitecturalResolution(architecturalDTO);
    }

    @Override
    public List<Map<String, Object>> getInfoBySubsystemCode(ArchitecturalResolutionDTO architecturalResolutionDTO) {
        return architecturalSvc.getInfoBySubsystemCode(architecturalResolutionDTO);
    }

    @Override
    public ArchitecturalResolutionDTO getInfoByCiId(Long ciId) {
        return architecturalSvc.getInfoByCiId(ciId);
    }

    @Override
    public AttrDefInfo selectAttrConf(AttrConfInfo attrConfInfo) {
        return architecturalSvc.selectAttrConf(attrConfInfo);
    }

    @Override
    public boolean checkArchitecturalName(ArchitecturalNameCheckDTO architecturalNameCheckDTO) {
        return architecturalSvc.checkArchitecturalName(architecturalNameCheckDTO);
    }

    @Override
    public ESCIInfo searchArchitecturalDTO(SearchArchitecturalDTO searchArchitecturalDTO) {
        return architecturalSvc.searchArchitecturalDTO(searchArchitecturalDTO);
    }

    @Override
    public List<Long> getResIdsById(Long architecturalId) {
        return architecturalSvc.getResIdsById(architecturalId);
    }

    @Override
    public List<FileResourceMeta> download(List<Long> resIds) {
        return architecturalSvc.download(resIds);
    }

    @Override
    public List<Long> changeSubsystemCode(String sourceCode, String targetCode, Long ciClassIdByName) throws BinaryException {
        return architecturalSvc.changeSubsystemCode(sourceCode,targetCode,ciClassIdByName);
    }


}
