package com.uinnova.product.eam.api.diagram;

public interface ESDiagramVersionApiClient {
    /**
     * <AUTHOR>
     * @Description
     * 恢复历史视图，覆盖当前视图
     *     1.更改当前视图的历史版本标识为0
     *     2.将当前视图的分享记录过渡给历史版本
     *     3.为
     * @Date 17:25 2021/7/5
     * @Param [diagramId-当前视图id，historyDiagramId-历史版本id ]
     * @return java.lang.Long
     **/
    Long restoreDiagramByVersionId(Long currDiagramId, Long historyVersionId);


    /**
     * <AUTHOR>
     * @Description 根据历史视图新建视图
     * @Date 11:44 2021/7/6
     * @Param [diagramId]
     * @return java.lang.Long
     **/
    Long createDiagramByCurrVersion(Long diagramId, boolean versionFlag);
}
