package com.uinnova.product.eam.api;

import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uino.bean.permission.base.SysUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IResourceAPIClient {
    List<ResourceInfo> upload(MultipartFile[] files, SysUser sysUser);

    List<FileResourceMeta> download(List<Long> ids);

    /**
     * 上传文件区分业务类型
     * @param files 文件
     * @param type 业务类型
     * @return 资源信息
     */
    List<ResourceInfo> uploadFileByType(MultipartFile[] files, Integer type);

    /**
     * 删除资源
     * @param id 资源id
     * @return 删除结果
     */
    Integer deleteResource(Long id);

    /**
     * 根据类型获取图片
     * @param type 业务类型：1制品，2品牌logo
     * @return 图片集合
     */
    List<DefaultFileVo> getImages(Integer type);
}
