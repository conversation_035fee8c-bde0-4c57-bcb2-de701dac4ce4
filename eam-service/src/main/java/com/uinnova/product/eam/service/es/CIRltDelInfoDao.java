package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CIRltDelInfo;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

@Repository
public class CIRltDelInfoDao extends AbstractESBaseDao<CIRltDelInfo, CIRltDelInfo> {

    @Override
    public String getIndex() {
        return "uino_eam_ci_rlt_del_info";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
