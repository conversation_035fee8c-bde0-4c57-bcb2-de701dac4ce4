package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.*;
import com.uinnova.product.eam.comm.bean.SceneDiagram;
import com.uinnova.product.eam.model.*;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 * 
 */
@Deprecated
public interface VcDiagramSvc {

	/**
	 * 查询目录列表
	 * 
	 * @param domainId
	 * @param cdt
	 * @param orders
	 * @return
	 */
	public List<VcDiagramDir> queryDiagramDirList(Long domainId, CVcDiagramDir cdt, String orders);

	/**
	 * 查询视图树结构
	 * 
	 * @param domainId
	 * 
	 * @param dirType
	 *            默认=1:我的
	 * @param orders
	 * @return
	 */
	public DiagramDirTree queryDiagramTree(Long domainId, Long userId, Integer dirType, CVcDiagram diagramCdt,
			String orders, Boolean flag);

	/**
	 * 通过视图目录中间表查询视图树结构
	 * 
	 */
	public DiagramDirTree queryDiagramTreeByDirRelation(Long domainId, Long userId, Integer dirType,
			CVcDiagram diagramCdt, String orders, Boolean flag);

	/**
	 * 查询目录列表下视图数量
	 * 
	 * @param domainId
	 * @param dirIds
	 * @param userId
	 * @return
	 */
	public List<VcDiagramDirInfo> queryDirDiagramCountList(Long domainId, Long[] dirIds, Long userId);

	/**
	 * 不分页查询视图元素信息
	 * 
	 * @param domainId
	 *            数据域
	 * @param cdt
	 *            查询条件
	 * @param orders
	 *            排序字段
	 * @return
	 */
	public List<VcDiagramEle> queryDiagramEleList(Long domainId, CVcDiagramEle cdt, String orders);

	/**
	 * 根据ID查询目录数据
	 * 
	 * @param domainId
	 *            数据域
	 * @param dirId
	 *            主键值
	 * @return
	 */
	public VcDiagramDir queryDiagramDirById(Long domainId, Long dirId);

	/**
	 * 保存或更新，判断主键ID[id]是否存在, 存在则更新, 不存在则插入
	 * 
	 * @param domainId
	 *            数据域
	 * @param record
	 *            数据记录
	 * @return 当前记录主键[id]值
	 */
	public Long saveOrUpdateDiagramDir(Long domainId, VcDiagramDir record);

	/**
	 * 查询视图
	 * 
	 * @param domainId
	 *            数据域
	 * @param cdt
	 *            查询条件
	 * @param orders
	 *            排序
	 * @return
	 */
	public List<VcDiagram> queryDiagramList(Long domainId, CVcDiagram cdt, String orders);

	/**
	 * 可以使用notIn 的分页查询视图
	 * 
	 * @param domainId
	 *            数据域
	 * @param pageNum
	 * @param pageSize
	 * @param cdt
	 *            查询条件
	 * @param notInIds
	 * @param orders
	 * @param diagramQs
	 *            查询深度
	 * @return
	 */
	public Page<VcDiagramInfo> queryDiagramPageByCdtAndNotInIds(Long domainId, Integer pageNum, Integer pageSize,
																CVcDiagram cdt, Long[] notInIds, String orders, DiagramQ[] diagramQs);

	/**
	 * 分页查询视图
	 * 
	 * @param domainId
	 *            数据域
	 * @param pageNum
	 * @param pageSize
	 * @param cdt
	 *            查询条件
	 * @param orders
	 * @return
	 */
	public Page<VcDiagram> queryDiagramPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cdt,
			String orders);

	/**
	 * 通过ID查询视图信息
	 * 
	 * @param domainId
	 * @param id
	 * @param diagramQs
	 * @return
	 */
	public VcDiagramInfo queryDiagramInfoById(Long domainId, Long id, DiagramQ[] diagramQs);

	/**
	 * 通过
	 * 
	 * @param domainId
	 *            数据域 数据域
	 * @param cdt
	 *            查询条件
	 * @return 视图数据
	 */
	public List<VcDiagramInfo> queryDiagramInfoList(Long domainId, CVcDiagram cdt, String orders, DiagramQ[] diagramQs);

	/**
	 * 分页查询视图详细信息
	 * 
	 * @param domainId
	 *            数据域
	 * @param pageNum
	 *            页码
	 * @param pageSize
	 *            页码大小
	 * @param cdt
	 *            查询条件
	 * @param orders
	 *            排序
	 * @param diagramQs
	 * @return 视图数据
	 */
	public Page<VcDiagramInfo> queryDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cdt,
			String orders, DiagramQ[] diagramQs);


	/**
	 * 生成一个视图名字
	 * 
	 * @param domainId
	 * @param userId
	 *            用户ID
	 * @param type
	 *            1视图2组合视图
	 * @return 视图名字
	 */
	public String generateDiagramName(Long domainId, Long userId, Integer type);

	/**
	 * 更新视图的名字和目录信息
	 * 
	 * @param domainId
	 *            数据域
	 * @param newName
	 *            新的视图名字
	 * @param newDirId
	 *            新的目录ID
	 * @param diagramId
	 *            视图ID
	 * @return 1成功0失败
	 */
	public Integer updateDiagramNameAndDirIdById(Long domainId, String newName, Long newDirId, Long diagramId);

	/**
	 * 保存或更新视图(ID存在更新)
	 * 
	 * @param domainId
	 *            数据域
	 * @param diagramInfo
	 * @return
	 */
	public Long saveOrUpdateDiagram(Long domainId, VcDiagramInfo diagramInfo);


	/**
	 * 更新视图的基本信息
	 * 
	 * @param record
	 *            视图信息
	 * @return 保存后的结果
	 */
	public VcDiagram updateDiagramBaseInfo(VcDiagram record);

	/**
	 * 查询目录和视图之间的关系
	 * 
	 * @param domainId
	 * @param cdt
	 * @return
	 */
	public Map<Long, Set<Long>> queryDiagramDirRelation(Long domainId, CVcDiagramDirRelation cdt);

	/**
	 * 保存视图和目录的关联信息
	 * 
	 * @param dirId
	 * @param dirType
	 * @param diagramId
	 * @return
	 */
	public Long saveDiagramDirRelation(Long domainId, Long dirId, Integer dirType, Long diagramId);

	Long saveOrUpdateSysDiagramDir(long domainId, VcDiagramDir record, LibType libType);

}
