package com.uinnova.product.eam.service.diagram;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.DiagramShareRecord;
import com.uinnova.product.eam.base.diagram.model.DiagramShareRecordResult;
import com.uinnova.product.eam.base.diagram.model.ShareRecordQueryBean;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IEamShareDiagramSvc {

    /**
     * 分页查询分享记录
     * @param shareRecordQueryBean
     * @return
     */
    Page<DiagramShareRecordResult> queryShareRecordPage(ShareRecordQueryBean shareRecordQueryBean);

    /**
     * 删除视图分享记录
     * @param id
     * @return
     */
    Integer removeShareRecord(Long id);

    /**
     * 查询当前视图分享的用户id
     * @param diagramIds
     * @return
     */
    Map<Long, Set<Long>> queryDiagramSharedUserIds(Long[] diagramIds);

    /**
     * 根据userId查询用户名下的分享记录
     * @param userId 用户id
     * @return 视图分享记录
     */
    List<DiagramShareRecord> queryByUserId(Long userId);
}
