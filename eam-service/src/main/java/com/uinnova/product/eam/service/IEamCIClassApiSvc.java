package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.dto.CiCodeDto;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface IEamCIClassApiSvc {
    /**
     * 保存或更新CI分类
     *
     * @param record
     * @return
     */
    Long saveOrUpdateESCIClass(ESCIClassInfo record);

    /**
     * 根据id删除CI分类
     * 
     * @param id
     * @return
     */
    Integer removeCIClassById(Long id);

    /**
     * 根据classId查询ci分类
     * @param classIds
     * @return
     */
    List<ESCIClassInfo> selectCiClassByIds(List<Long> classIds);

    /**
     * @param :
     * <AUTHOR> wcl
     * @Date : 2021/12/24 17:28
     * @description : 查询分类信息属性的编码
     * @Return :
     **/
    String getEnCodeNum(CiCodeDto ciCodeDto);

    /**
     * @param :
     * <AUTHOR> wcl
     * @Date : 2021/12/24 17:28
     * @description : 查询分类信息属性的编码
     * @Return :
     **/
    Long getIntegerEnCodeNum(CiCodeDto ciCodeDto);

    /**
     * 清空编码存储记录(ES+Redis)
     * @param classIds 分类id
     */
    void clearCacheEncode(List<Long> classIds);

    /**
     * copy CI分类
     * @param ids
     * @return
     */
    Map<Long, ESCIClassInfo> copyCIClassByIds(List<Long> ids);

    /**
     * 根据classCode批量查询CI分类
     *
     * @param classCodes
     * @return
     */
    CcCiClassInfo getCIClassByCodes(String classCodes);

    /**
     * 根据分类标识查询CI分类(属性正确)
     *
     * @param classCodes 分类标识
     * @return ci分类
     */
    ESCIClassInfo getCIClassByCode(String classCodes);

    /**
     * 根据classCode批量查询CI分类
     * @param classCodes 分类标识
     */
    List<CcCiClassInfo> getByClassCodes(Collection<String> classCodes, Long domainId);

    /**
     * 根据分类名称批量查询CI分类
     * @param names 分类名称
     * @return ci分类
     */
    List<CcCiClassInfo> getByClassNames(Collection<String> names, Long domainId);

    /**
     * 根据分类id查询分类信息
     */
    ESCIClassInfo queryClassById(Long id);

    /**
     * 获取分类属性map
     * @param id 分类id
     * @return 属性map<属性id，属性>
     */
    Map<Long, CcCiAttrDef> getAttrDefMap(Long id);

    /**
     * 根据分类标识查询分类信息
     * @param classCode
     * @return
     */
    CcCiClassInfo queryClassAndAttrMappingByCode(String classCode);

    /**
     * 获取分类列表
     * @param word
     * @return
     */
    List<ESCIClassInfo> getCIClassList(String word);

    /**
     * 根据元模型过滤分类
     * @param id 对象分类id
     * @return 分类信息
     */
    List<ESCIClassInfo> filterByVisualModel(Long id, Long orgId);

    /**
     * 校验分类是否能编辑
     * @param id 分类id
     * @return 是否可编辑
     */
    Boolean checkEdit(Long id);

    /**
     *  根据分类ID校验分类下是否存在数据（private/design）
     * @param id
     * @return
     */
    Boolean checkHasData(Long id);
}
