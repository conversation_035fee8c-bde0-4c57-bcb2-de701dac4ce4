package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.comm.bean.CcCiClassInfoConfVO;
import com.uinnova.product.eam.comm.model.es.AssetListAttrConf;

/**
 *
 * <AUTHOR>
 */
public interface AssetListAttrConfSvc {
    /**
     * 保存列表配置
     * @param attrConf
     * @return
     */
    Long saveOrUpdate(AssetListAttrConf attrConf);

    /**
     *  根据配置卡牌Id及分类获取列表配置信息
     * @param appSquareConfId
     * @param type
     * @return
     */
    AssetListAttrConf getListAttr(Long appSquareConfId, Integer type);

    /**
     * 根据ID删除列表配置
     * @param id
     * @return
     */
    Integer deleteListAttrById(Long id);

    /**
     * 根据ID删除列表配置
     * @param appSquareConfId
     * @return
     */
    Integer delListAttrByAppSquareConfId(Long appSquareConfId);

    /**
     * 获取列表显示配置
     * @param appSquareConfId
     * @param type
     * @param oldAttr 是否用原attrdef名称：是true否false
     * @return
     */
    CcCiClassInfoConfVO getListShowAttrList(Long appSquareConfId, Integer type, boolean oldAttr);
}
