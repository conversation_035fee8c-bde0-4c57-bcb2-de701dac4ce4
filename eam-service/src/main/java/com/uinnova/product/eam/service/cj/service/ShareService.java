package com.uinnova.product.eam.service.cj.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.cj.domain.PlanDesignShareRecord;
import com.uinnova.product.eam.model.cj.vo.SharePlanDiagramVO;
import com.uinnova.product.eam.model.cj.vo.ShareUserParamVO;
import com.uinnova.product.eam.model.cj.vo.SharedUserVO;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 分享接口
 *
 * <AUTHOR>
 */
public interface ShareService {

    /**
     * 查找呗分享列表
     *
     * @param paramMap 参数
     * @return 方案 + 视图集
     */
    List<SharePlanDiagramVO> getSharedList(Map<String, Object> paramMap);

    /**
     * 查询方案被分享人列表
     */
    List<SharedUserVO> findPlanSharedList(Long planDesignId, boolean flag);

    /**
     * 查询方案被分享人列表
     */
    List<PlanDesignShareRecord> getByPlanId(Long planId);

    /**
     * 批量查询方案被分享人列表
     * @param planIds
     * @return
     */
    Map<Long, List<PlanDesignShareRecord>> selectByPlanIds(List<Long> planIds);

    /**
     * 删除分享的方案
     * @param planIds
     */
    void deleteOrRecoverySharePlan(List<Long> planIds, Integer status);

    /**
     * 查询方案有编辑和有分享权限的人员列表
     * @param planDesignId
     */
    List<SharedUserVO> findPlanSharedMemberList(Long planDesignId);

    /**
     * 获取分享的用户列表
     * @return
     */
    Page<SysUser> findSharedUserList(ShareUserParamVO shareUserParamVO);

    /**
     * 获取方案分享记录列表
     * @param planDesignShareRecord
     * @return
     */
    List<PlanDesignShareRecord> findPlanShareRecordList(PlanDesignShareRecord planDesignShareRecord);
}
