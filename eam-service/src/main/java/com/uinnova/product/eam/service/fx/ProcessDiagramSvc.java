package com.uinnova.product.eam.service.fx;

import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 *  伏羲 视图数据处理相关
 */
public interface ProcessDiagramSvc {

    /**
     * 根据目录id校验目录是否存在
     * @param dirId 设计库目录ID
     * @param libType 三库标识
     * @return
     */
    Boolean checkDirExist(Long dirId, LibType libType);

    /**
     * 根据视图ids获取DTO信息
     * @return
     */
    List<ESDiagramDTO> queryDiagramInfoByIds(List<String> diagramIds);

    /**
     *  根据视图所属目录id获取DTO信息
     * @param dirIds
     * @return
     */
    List<ESDiagramDTO> queryDiagramInfoByDirIds(List<Long> dirIds);

    /**
     * 处理视图本身数据发布
     * @param esDiagramDTOS 视图信息列表
     * @param releaseDesc 单图发布时发布信息描述
     * @param diagramMap 视图ID与发布位置（目录ID）map
     * @return 返回map 为 架构设计的ID 和 设计仓库的ID
     */
    Map<String, String> dealReleaseDiagram(List<ESDiagramDTO> esDiagramDTOS, String releaseDesc,
                                           Map<String, Long> diagramMap);

    /**
     *  处理视图本身数据检出
     * @param publicDiagram
     * @param dirId
     * @param actionType
     * @param diagramName
     * @return
     */
    String dealCheckOutDiagram(ESDiagramDTO publicDiagram, Long dirId, Integer actionType, String diagramName, String loginCode);

    /**
     *  批量校验视图本身版本冲突
     * @param esDiagramDTOS
     * @return 返回的boolean中 false表示无冲突
     */
    Map<String, Boolean> checkDiagramVersionByIds(List<ESDiagramDTO> esDiagramDTOS);

    /**
     *  刷新视图数据节点
     * @param freshCodeMap 冲突数据 私有库的code -》 设计库的信息
     * @param loginCode 当前用户code
     * @return
     */
    Boolean freshDiagramNode(Map<String, ESCIInfo> freshCodeMap, String loginCode);

}
