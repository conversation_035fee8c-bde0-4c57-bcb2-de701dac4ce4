package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.model.dto.ElementConditionDto;
import com.uinnova.product.eam.model.vo.CjArtifactVo;

import java.util.List;
import java.util.Map;

/**
 * @description: 仓颉制品分类
 * @author: Lc
 * @create: 2022-08-31 15:59
 */
public interface CjArtifactSvc {

    /**
     * 获取制品列表
     * @param dto
     */
    List<CjArtifactVo> findArtifactList(ElementConditionDto dto);

    /**
     * 通知制品id获取制品里列表
     */
    List<EamArtifact> findAllArtifactList();

    /**
     * 通过制品id获取制品列表
     * @param ids
     */
    Map<Long, String> findArtifactListByIds(List<Long> ids);
}
