package com.uinnova.product.eam.service.dm;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.model.dm.CheckResponse;
import com.uinnova.product.eam.model.dm.bean.AttrParamDto;
import com.uinnova.product.eam.model.dm.bean.DataTypeDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DataModelCatalogSvc {

    /***
     * 将概念实体转换成逻辑实体
     * @param conceptDiagrams 需要转化的概念图
     * @param convertType 6-概念模型转逻辑模型；7-逻辑模型转系统逻辑模型；11-系统模型转物理模型
     * @return 转换成功与否的标识
     */
    Boolean convertDiagrams(List<ESDiagram> conceptDiagrams, Integer convertType, Integer operation);

    /***
     * 将概念实体转换成逻辑实体
     * @param dirId 目录id
     * @param diagramId 视图id
     * @param convertType 6-概念模型转逻辑模型；7-逻辑模型转系统逻辑模型；11-系统模型转物理模型
     * @return 转换成功与否的标识
     */
    Integer convert(Long dirId, String diagramId, Integer convertType,Integer operation);

    /***
     * 获取数据类型
     * @param libType 私有库/设计库标识
     * @return 类型名称集合
     */
    Map<String,List<DataTypeDto>> queryDataType(LibType libType);

    /***
     * 根据属性ciCode查询属性信息
     * @param paramDto 参数信息
     * @return 属性信息集合
     */
    List<CcCiInfo> getAttrInfoBiCiCode(AttrParamDto paramDto);

    /**
     * 根据制品类型过滤视图
     * @param dirId 目录id
     * @param dEnergy 视图加密id
     * @param type 制品类型分类
     * @return 过滤后的视图
     */
    List<ESDiagram> filterDiagramByType(Long dirId, String dEnergy, Integer type);
    /**
     * 校验私有库实体和属性是否发生了变化
     * @param dirId 文件夹id
     * @param dEnergy 视图加密id
     * @param convertType 转化类型
     * @return 是否需要更新
     */
    CheckResponse checkVariation(Long dirId, String dEnergy, Integer convertType);
}
