package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.dto.DataSetSheetInfo;
import com.uinnova.product.eam.comm.model.es.DataSetSnapshot;
import com.uinnova.product.eam.model.DataSetSnapshotCdt;
import com.uinnova.product.eam.model.bm.DataSetEnum;
import com.uinnova.product.eam.service.DataSetSnapshotSvc;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.diagram.ESDiagramExtendSvc;
import com.uinnova.product.eam.service.es.DataSetSnapshotDao;
import com.uino.bean.cmdb.base.ESTaskLock;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.dao.cmdb.ESDataSetTaskLockSvc;
import com.uino.dao.cmdb.dataset.ESDataSetExeResultSheetSvc;
import com.uino.dao.cmdb.dataset.ESDataSetSvc;
import com.uino.service.cmdb.microservice.ITaskLockSvc;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataSetSnapshotSvcImpl implements DataSetSnapshotSvc {

    @Autowired
    private ESDataSetSvc esDataSetSvc;

    @Autowired
    private ESDiagramExtendSvc esDiagramExtendSvc;

    @Autowired
    private ESDataSetExeResultSheetSvc esDataSetExeResultSheetSvc;

    @Autowired
    private DataSetSnapshotDao dataSetSnapshotDao;

    @Autowired
    private PlanDesignInstanceService planDesignInstanceService;

    @Autowired
    private ITaskLockSvc taskLockSvc;

    @Autowired
    private ESDataSetTaskLockSvc dataSetTaskLockSvc;

    @Override
    public Long saveorupdateDataSetSnapshot(String assetKey, Integer assetType, List<Long> dataSetIds) {
        List<DataSetSnapshotCdt> realTimeDataSetInfo = this.getRealTimeDataSetInfo(dataSetIds);
        List<DataSetSnapshotCdt> snapshotDataSetInfo = this.getSnapshotDataSetInfo(assetKey, dataSetIds);
        Map<Long, DataSetSnapshotCdt> snapshotDataSetMap = snapshotDataSetInfo.stream().collect(Collectors.toMap(DataSetSnapshotCdt::getDataSetId, each -> each, (k1, k2) -> k2));
        if (!CollectionUtils.isEmpty(snapshotDataSetInfo) &&
                snapshotDataSetInfo.get(0).getFrom() == DataSetEnum.SNAPSHOT_DATA) {
            // 实时数据覆盖做更新
            log.info("更新临时数据集");
            realTimeDataSetInfo.forEach(each -> {
                Long dataSetId = each.getDataSetId();
                DataSetSnapshotCdt cdt = snapshotDataSetMap.get(dataSetId);
                if (cdt != null) {
                    each.setId(cdt.getId());
                }
            });
        } else {
            // 临时数据为空的场景 直接根据实时数据集创建
            log.info("临时数据为空 直接根据实时数据集创建并绑定资产");
        }
        List<DataSetSnapshot> copy = EamUtil.copy(realTimeDataSetInfo, DataSetSnapshot.class);
        copy.forEach(each -> {
                each.setAssetKey(assetKey);
                each.setAssetType(assetType);
            });
        dataSetSnapshotDao.saveOrUpdateBatch(copy);
        return 1L;
    }

    @Override
    public DataSetSnapshotCdt getDataSetInfoByAssetKey(String assetKey, Integer assetType, Long dataSetId) {
        DataSetEnum dataSetEnum = this.getDataSetFrom(assetKey, assetType);
        switch (dataSetEnum) {
            case SNAPSHOT_DATA:
                return getSnapshotDataSetInfo(assetKey, Collections.singletonList(dataSetId)).get(0);
            case REALTIME_DATA:
                return getRealTimeDataSetInfo(Collections.singletonList(dataSetId)).get(0);
            default:
                throw new BinaryException("不支持的数据集来源" + JSONObject.toJSONString(dataSetEnum));
        }
    }

    @Override
    public Long deleteDataSetSnapshotByAssetKey(String assetKey) {
        // 删除关联资产相关的临时数据
        if (BinaryUtils.isEmpty(assetKey)) {
            return 0L;
        }
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("assetKey.keyword", assetKey));
        dataSetSnapshotDao.deleteByQuery(bool, Boolean.TRUE);
        return 1L;
    }

    /**
     *  根据数据集id获取
     * @param dataSetIds
     * @return
     */
    private List<DataSetSnapshotCdt> getRealTimeDataSetInfo(List<Long> dataSetIds) {
        // 如果有任务锁，说明批处理任务还未结束
        if (CollectionUtils.isEmpty(dataSetIds)) {
            return new ArrayList<>();
        }
        List<String> lockName = new ArrayList<>();
        dataSetIds.forEach(dataSetId -> {
            String taskName = "DataSetTask_" + dataSetId;
            lockName.add(taskName);
        });
        BoolQueryBuilder lockQuery = QueryBuilders.boolQuery();
        lockQuery.must(QueryBuilders.termsQuery("taskName.keyword", lockName));
        List<ESTaskLock> lockList = dataSetTaskLockSvc.getListByQuery(lockQuery);
        if (!CollectionUtils.isEmpty(lockList)) {
            log.error("数据集【{}】正在构建中，请稍候", JSONObject.toJSONString(lockList));
            throw new RuntimeException("数据正在构建中，请稍候");
        }
        List<JSONObject> dataSetBaseInfos = esDataSetSvc.getListByQuery(QueryBuilders.termsQuery("id", dataSetIds));
        List<DataSetExeResultSheet> dataSetExeResultList = esDataSetExeResultSheetSvc.getListByQuery(QueryBuilders.termsQuery("dataSetId", dataSetIds));
        Map<Long, List<DataSetExeResultSheet>> dataSetExeResultGroup = dataSetExeResultList.stream().collect(Collectors.groupingBy(DataSetExeResultSheet::getDataSetId));

        List<DataSetSnapshotCdt> dataSetSnapshotList = new ArrayList<>();
        for (JSONObject dataSetBaseInfo : dataSetBaseInfos) {
            Long dataSetId = dataSetBaseInfo.getLong("id");
            List<DataSetExeResultSheet> sheet = dataSetExeResultGroup.get(dataSetId);
            DataSetSnapshotCdt dataSetSnapshot = new DataSetSnapshotCdt();
            this.fillBaseInfo(dataSetSnapshot, dataSetBaseInfo);
            this.fillTableInfo(dataSetSnapshot, sheet);
            dataSetSnapshot.setFrom(DataSetEnum.REALTIME_DATA);
            dataSetSnapshotList.add(dataSetSnapshot);
        }
        return dataSetSnapshotList;
    }

    /**
     *  根据资产主键和数据集id获取临时数据
     * @param assetKey
     * @param dataSetIds
     * @return
     */
    private List<DataSetSnapshotCdt> getSnapshotDataSetInfo(String assetKey, List<Long> dataSetIds) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("assetKey.keyword", assetKey));
        bool.must(QueryBuilders.termsQuery("dataSetId", dataSetIds));
        List<DataSetSnapshot> dataSetSnapshotList = dataSetSnapshotDao.getListByQuery(bool);

        // todo

        if (CollectionUtils.isEmpty(dataSetSnapshotList)) {
            // 这里查询临时数据集为空可能是存量数据没有关联的场景
            log.error("资产【{}】关联的临时数据集【{}】不存在，补充查询实时数据集", assetKey, JSONObject.toJSONString(dataSetSnapshotList));
            List<DataSetSnapshotCdt> realTimeDataSetInfo = this.getRealTimeDataSetInfo(dataSetIds);
            realTimeDataSetInfo.forEach(realTimeData -> {
                realTimeData.setFrom(DataSetEnum.WITHOUT_SNAPSHOT_QUERY_REALTIME);
            });
            return realTimeDataSetInfo;
        }
        List<DataSetSnapshotCdt> cdt = EamUtil.copy(dataSetSnapshotList, DataSetSnapshotCdt.class);
        cdt.forEach(dataSetSnapshot -> {
            dataSetSnapshot.setFrom(DataSetEnum.SNAPSHOT_DATA);
        });
        return cdt;
    }

    /**
     *  根据资产状态获取数据集来源
     * @return
     */
    private DataSetEnum getDataSetFrom(String assetKey, Integer assetType) {
        switch (assetType) {
            case 3:
                return planDesignInstanceService.getDataSetFromByPlanId(Long.valueOf(assetKey));
            default:
                throw new BinaryException("不支持当前类型资产查询：" + assetType);
        }
    }

    /**
     *  填充数据集临时数据表信息
     * @param dataSetSnapshot
     */
    private void fillTableInfo(DataSetSnapshotCdt dataSetSnapshot, List<DataSetExeResultSheet> dataSetExeResultList) {
        if (CollectionUtils.isEmpty(dataSetExeResultList)) {
            log.info("数据集{},{}没有执行结果", dataSetSnapshot.getDataSetName(), dataSetSnapshot.getDataSetId());
            return;
        }

        Map<String, List<DataSetExeResultSheet>> exeResultByPathName = dataSetExeResultList.stream().
                collect(Collectors.groupingBy(DataSetExeResultSheet::getSheetId));

        List<DataSetSheetInfo> snapshotSheetList = new ArrayList<>();
        for (Map.Entry<String, List<DataSetExeResultSheet>> entry : exeResultByPathName.entrySet()) {
            String sheetId = entry.getKey();
            List<DataSetExeResultSheet> dataSetExeResultSheets = entry.getValue();

            DataSetExeResultSheet dataSetExeResultSheet = dataSetExeResultSheets.get(0);

            DataSetSheetInfo sheetInfo = new DataSetSheetInfo();
            sheetInfo.setSheetId(sheetId);
            sheetInfo.setIdPath(dataSetExeResultSheet.getClassIds());
            sheetInfo.setNamePath(dataSetExeResultSheet.getClassNamePath());

            sheetInfo.setHeaders(dataSetExeResultSheet.getHeaders());
            sheetInfo.setData(dataSetExeResultSheets.stream().map(DataSetExeResultSheet::getAttrs).collect(Collectors.toList()));
            snapshotSheetList.add(sheetInfo);
        }
        dataSetSnapshot.setSheetList(snapshotSheetList);
    }

    /**
     *  填充数据集临时数据基本信息
     * @param dataSetSnapshot
     * @param dataSet
     */
    private void fillBaseInfo(DataSetSnapshotCdt dataSetSnapshot, JSONObject dataSet) {
        dataSetSnapshot.setDataSetName(dataSet.getString("name"));
        dataSetSnapshot.setDataSetId(dataSet.getLong("id"));
        dataSetSnapshot.setDatasetCreator(dataSet.getString("creater"));
        dataSetSnapshot.setDatasetCreateTime(dataSet.getLong("createTime"));
        dataSetSnapshot.setDescription(dataSet.getString("description"));
        dataSetSnapshot.setRootClassId(dataSet.getLong("classId"));
        // 重新生成临时数据缩略图
        if (!BinaryUtils.isEmpty(dataSet.getString("thumbnail"))) {
            StringBuilder thumbnailBuilder = new StringBuilder();
            thumbnailBuilder.append("/").append(LocalDate.now()).append("/").append(UUID.randomUUID()).append(".png");
            esDiagramExtendSvc.updateIcon(thumbnailBuilder.toString(), dataSet.getString("thumbnail"));
            dataSetSnapshot.setThumbnail(thumbnailBuilder.toString());
        } else {
            log.info("数据集{}没有缩略图", dataSet.getString("name"));
        }
    }
}
