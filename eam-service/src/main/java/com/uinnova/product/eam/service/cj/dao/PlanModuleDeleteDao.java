package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanModuleDelete;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @description: 记录方案删掉的模块id
 * @author: Lc
 * @create: 2023-03-08 13:47
 */
@Component
public class PlanModuleDeleteDao extends AbstractESBaseDao<PlanModuleDelete, PlanModuleDelete> {

    @Override
    public String getIndex() {
        return "uino_cj_plan_module_delete";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }

}
