package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.model.DiagramBo;
import com.uinnova.product.eam.model.PlanDiagramRequest;
import com.uinnova.product.eam.model.vo.RelateDiagram;
import com.uinnova.product.eam.model.vo.RelateionDiagramVo;

/**
 * <AUTHOR>
 */
@Deprecated
public interface ESDiagramSvc {

    void writeReleaseDiagramId(String releaseDiagramId, Long id);

    int increaseReleaseVersionByDEnergyId(String dEnergyId);

    void writeHistoryVersion(Long diagramId);

    int setLocalVersionByDEnergyIdToZero(Long diagramId);

    /**
     * 名称模糊分页查询已发布的视图
     *
     * @param request {@link PlanDiagramRequest}
     * @return 数据对象 {@link ESDiagram}
     */
    Page<ESDiagram> findDiagramLikeName(PlanDiagramRequest request);

    Page<DiagramBo> findDiagramList(PlanDiagramRequest request);

    /**
     * 查询引用视图（方案中引用视图、视图中关联视图）
     * @param params 查询参数
     * @return
     */
    Page<RelateDiagram> queryRelateDiagramList(RelateionDiagramVo params);
}
