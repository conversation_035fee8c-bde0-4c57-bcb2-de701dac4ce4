package com.uinnova.product.eam.service.fx;

import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.db.bean.DiagramChangeData;
import com.uinnova.product.eam.model.bm.DiagramPrivateAndDesginData;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;

/**
 *  伏羲 视图内 CI / RLT 数据处理相关
 */
public interface ProcessCiRltSvc {

    /**
     * 根据ownerCode查询用户信息
     *
     * @param ownerCode
     * @return
     */
    SysUser getUserInfoByOwnerCode(String ownerCode);

    /**
     * 根据 视图IDs 和 视图IDs的ownerCode 获取私有库设计库的CI/RLT数据
     *
     * @param esDiagramDTOS
     * @param currentUserInfo
     * @return
     */
    DiagramPrivateAndDesginData getPrivateAndDesginDataByDEnergyId(List<ESDiagramDTO> esDiagramDTOS, SysUser currentUserInfo);

    DiagramPrivateAndDesginData getPrivateAndDesginDataByDEnergyId(List<ESDiagramDTO> esDiagramDTOS, SysUser currentUserInfo, Boolean isCustom);

    /**
     * 根据私有库设计库信息生成数据变更集（仅包含CI变更集）
     *
     * @param privateAndDesginDataByDEnergyId
     * @return
     */
    Map<String, List<DiagramChangeData>> getChangeCIDataByDEnergyIds(DiagramPrivateAndDesginData privateAndDesginDataByDEnergyId);

    /**
     * 根据查询的变更数据发布视图中的CI要素 -- 强制行为（将私有库数据覆盖到设计库）
     *
     * @param loginCode               用户code
     * @param changeCIDataByDEnergyId 变更类型 -》 CI数据
     * @return 返回值为发布视图后设计库的CI数据
     */
    List<CcCiInfo> dealPublishDiagramCI(Map<String, List<DiagramChangeData>> changeCIDataByDEnergyId, String loginCode);

    /**
     * 根据查询的变更数据发布视图中的CI要素 -- 强制行为（将私有库数据覆盖到设计库）
     *
     * @param loginCode               用户code
     * @param changeCIDataByDEnergyId 变更类型 -》 CI数据
     * @return 返回值为发布视图后设计库的CI数据
     */
    List<CcCiInfo> dealHTPublishDiagramCI(Map<String, List<DiagramChangeData>> changeCIDataByDEnergyId, String loginCode, Boolean isCustom);

    /**
     * 根据查询的变更数据发布视图中的RLT关系  ******当前关系发布规则 仅支持覆盖 无法同CI逻辑一样生成变更数据集 需要在私有库设计库 *******
     *
     * @param privateAndDesginDataByDEnergyId 私有库 / 设计库 RLT 数据
     * @param ccCiInfos                       关系源端目标端存在设计库的 CI 数据
     * @return
     */
    List<ESCIRltInfo> dealPublishDiagramRlt(DiagramPrivateAndDesginData privateAndDesginDataByDEnergyId, List<CcCiInfo> ccCiInfos);

    /**
     * 检出处理 CI 数据
     *
     * @param privateAndDesginDataByDEnergyId
     * @param loginCode
     */
    List<CcCiInfo> dealCheckOutDiagramCI(DiagramPrivateAndDesginData privateAndDesginDataByDEnergyId, String loginCode);

    /**
     * 检出处理 RLT 数据
     *
     * @param privateAndDesginDataByDEnergyId
     * @param privateCiInfos
     */
    Boolean dealCheckOutDiagramRLT(DiagramPrivateAndDesginData privateAndDesginDataByDEnergyId,
                                   List<CcCiInfo> privateCiInfos, String loginCode);

    /**
     * 多用户校验CI主键冲突
     *
     * @param userCodeAndDiagramInfoMap      用户code -》 对应的用户视图信息
     * @param privateAndDesginDataByUserCode 用户code -》 对应的用户名下 私有库 / 设计库 数据
     * @param isPush                         是否为发布时校验
     */
    Map<String, List<DiagramChangeData>> checkPrimaryKeyConflict(Map<String, List<ESDiagramDTO>> userCodeAndDiagramInfoMap,
                                                                 Map<String, DiagramPrivateAndDesginData> privateAndDesginDataByUserCode,
                                                                 Boolean isPush);

    /**
     * 批量校验制品约束条件
     *
     * @param productNumData    制品ID -》 关联当前制品的视图数据
     * @param dEnergyAndNodeMap 视图ID -》 当前视图下的node信息
     * @return
     */
    Map<String, List<String>> checkCategoryEleNum(Map<Long, List<ESDiagramDTO>> productNumData,
                                                  Map<String, List<ESDiagramNode>> dEnergyAndNodeMap);

    /**
     * 批量校验视图必填项数据 支持多用户
     *
     * @param userData                       用户ID -》 用户维度的视图
     * @param privateAndDesginDataByUserCode 用户ID -》 用户维度的CI数据
     * @param idAndNodeInfo                  视图ID -》 视图上的node信息
     * @return
     */
    Map<String, Map<String, String>> checkRequiredFieldByIds(Map<String, List<ESDiagramDTO>> userData,
                                                             Map<String, DiagramPrivateAndDesginData> privateAndDesginDataByUserCode,
                                                             Map<String, List<ESDiagramNode>> idAndNodeInfo);

    /**
     * 批量校验CI版本 支持多用户
     *
     * @param userData                       用户ID -》 用户视图
     * @param privateAndDesginDataByUserCode 用户ID -》 用户CI
     */
    Map<String, List<DiagramChangeData>> checkCIVersionByIds(Map<String, List<ESDiagramDTO>> userData,
                                                             Map<String, DiagramPrivateAndDesginData> privateAndDesginDataByUserCode);

    /**
     * 重新绑定视图需要检出的要素 刷新数据接口仅支持当前用户操作 无法跨用户刷新
     *
     * @param primaryKeys 要素主键集合
     * @return
     */
    Boolean freshBindingEleByDEnergyId(List<String> primaryKeys, Integer actionType);
}
