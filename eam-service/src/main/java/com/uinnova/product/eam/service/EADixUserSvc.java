package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.dto.UserInfoDto;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.business.UserInfo;

import java.util.List;

public interface EADixUserSvc {
    
    public Integer saveBatchOrg(List<SysOrg> orgList);

    ImportSheetMessage saveBatchUser(List<UserInfoDto> userInfos);

    Integer saveBatchOrgUserRlt(List<SysUserOrgRlt> rltList);

    Integer saveBatchDepartment();
}
