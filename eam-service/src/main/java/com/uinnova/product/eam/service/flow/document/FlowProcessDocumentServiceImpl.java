package com.uinnova.product.eam.service.flow.document;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.feign.workable.entity.HistoryTaskResponse;
import com.uinnova.product.eam.model.FlowProcessSystemPublishHistoryDto;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.dto.FlowSystemFileDto;
import com.uinnova.product.eam.model.enums.FlowSystemType;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.flow.base.AbstractFlowProcessSystemService;
import com.uinnova.product.eam.service.utils.Base64Utils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.util.ESUtil;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import javax.imageio.ImageIO;
import jakarta.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLConnection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.uinnova.product.eam.service.asset.AssetContent.CANCELLED;
import static com.uinnova.product.eam.service.asset.AssetContent.RELEASE_STATE;

/**
 * 流程文档管理服务
 * 负责文档管理、导出、版本历史等功能
 */
@Service
@Slf4j
public class FlowProcessDocumentServiceImpl extends AbstractFlowProcessSystemService {

    @Value("${local.resource.space}")
    private String localPath;

    @Value("${http.resource.space}")
    private String httpPath;

    @Autowired
    private RsmUtils rsmUtils;

    @Resource
    protected ESDiagramNodeDao esDiagramNodeDao;

    private static void mergeCellsHorizontally(XWPFTable table, int rowIndex, int fromColIndex, int toColIndex) {
        for (int colIndex = fromColIndex; colIndex <= toColIndex; colIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(colIndex);
            CTTc ctTc = cell.getCTTc();
            CTTcPr tcPr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();

            if (colIndex == fromColIndex) {
                // 第一个单元格，设置跨列属性为RESTART
                CTHMerge hMerge = tcPr.isSetHMerge() ? tcPr.getHMerge() : tcPr.addNewHMerge();
                hMerge.setVal(STMerge.RESTART);

                // 保留或设置第一个单元格的内容（可选）
            } else {
                // 其他单元格，设置跨列属性为CONTINUE
                CTHMerge hMerge = tcPr.isSetHMerge() ? tcPr.getHMerge() : tcPr.addNewHMerge();
                hMerge.setVal(STMerge.CONTINUE);

                // 清空单元格文本内容（可选，但推荐这样做以避免显示旧内容）
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        run.setText("", 0); // 将文本设置为空字符串，并从索引0开始替换
                    }
                }
            }
        }
    }

    /**
     * 插入文本内容
     *
     * @param doc
     * @param dataMap
     * @throws Exception
     */
    private static void replaceTextInDoc(XWPFDocument doc, Map<String, Object> dataMap) throws Exception {
        // 遍历文档中的所有书签
        List<XWPFParagraph> paragraphs = doc.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            List<XWPFRun> runs = paragraph.getRuns();
            for (XWPFRun run : runs) {
                String text = run.getText(0);
                if (text != null && containsPlaceholder(text, dataMap.keySet())) {
                    // 替换占位符
                    for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                        String placeholder = entry.getKey();
                        if (text.contains(placeholder)) {
                            text = text.replace(placeholder, BinaryUtils.isEmpty(entry.getValue()) ? "空" : entry.getValue().toString());
                            run.setText(text, 0);
                        }
                    }
                }
            }
        }
    }

    private static boolean containsPlaceholder(String text, Set<String> placeholders) {
        for (String placeholder : placeholders) {
            if (text.contains(placeholder)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 保存或更新流程文件
     */
    public Long saveOrUpdateFlowSystemFile(FlowSystemFileDto flowSystemFileDto) {
        if (flowSystemFileDto.getId() == null) {
            flowSystemFileDto.setStatus(0);
        }
        return flowSystemFilePrivateDao.saveOrUpdate(flowSystemFileDto);
    }

    /**
     * 获取流程文件
     */
    public FlowSystemFileDto getFlowSystemFile(String ciCode, LibType libType) {
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setPageSize(1);
        esciSearchBean.setPageNum(1);
        esciSearchBean.setCiCodes(Collections.singletonList(ciCode));
        Page<ESCIInfo> esciInfoPage = ciSwitchSvc.getCiSvc(LibType.DESIGN).searchESCIByBean(esciSearchBean);
        if (esciInfoPage == null || CollectionUtils.isEmpty(esciInfoPage.getData())) {
            return null;
        }
        ESCIInfo esCi = esciInfoPage.getData().get(0);
        if (esCi == null) {
            return null;
        }
        FlowSystemFileDto flowSystemFileDto = new FlowSystemFileDto();
        flowSystemFileDto.setAttrs(esCi.getAttrs());
        Map<String, Object> processMap = esCi.getAttrs();
        Object objective = processMap.get("流程目的");
        if (objective != null) {
            flowSystemFileDto.setObjective(String.valueOf(objective));
        }
        Object overview = processMap.get("流程定义");
        if (overview != null) {
            flowSystemFileDto.setOverview(String.valueOf(overview));
        }
        JSONObject flowSystemRangeJson = new JSONObject();
        Object upstream = processMap.get("上游流程");
        Object downstream = processMap.get("下游流程");
        Object input = processMap.get("流程输入");
        Object output = processMap.get("流程输出");
        if (upstream != null) {
            flowSystemRangeJson.put("上游流程", String.valueOf(upstream));
        } else {
            flowSystemRangeJson.put("上游流程", "");
        }
        if (downstream != null) {
            flowSystemRangeJson.put("下游流程", String.valueOf(downstream));
        } else {
            flowSystemRangeJson.put("下游流程", "");
        }
        if (input != null) {
            flowSystemRangeJson.put("流程输入", String.valueOf(input));
        } else {
            flowSystemRangeJson.put("流程输入", "");
        }
        if (output != null) {
            flowSystemRangeJson.put("流程输出", String.valueOf(output));
        } else {
            flowSystemRangeJson.put("流程输出", "");
        }
        flowSystemFileDto.setFlowSystemRange(flowSystemRangeJson.toJSONString());

        // 封装KCP,获取视图信息
        Set<String> ciCodes = Collections.singleton(ciCode);
        Map<String, String> diagramRelaCiMap = this.diagramRelaCiMap(ciCodes, libType);
        if (CollectionUtils.isEmpty(diagramRelaCiMap)) {
            return flowSystemFileDto;
        }
        List<ESDiagramDTO> esDiagramDTOList = this.esDiagramDTOList(diagramRelaCiMap);
        if (CollectionUtils.isEmpty(esDiagramDTOList)) {
            return flowSystemFileDto;
        }
        Map<String, String> kcpInfoVoMap = new HashMap<>();
        Set<String> uniqueCodeSet = new HashSet<>();
        for (ESDiagramDTO eSDiagramDto : esDiagramDTOList) {
            ESDiagramInfoDTO diagram = eSDiagramDto.getDiagram();
            List<ESDiagramModel> modelList = diagram.getModelList();
            if (!CollectionUtils.isEmpty(modelList)) {
                for (ESDiagramModel model : modelList) {
                    List<ESDiagramLink> linkDataArray = model.getLinkDataArray();
                    if (CollectionUtils.isEmpty(linkDataArray)) {
                        continue;
                    }
                    List<ESDiagramNode> nodeDataArray = model.getNodeDataArray();
                    if (CollectionUtils.isEmpty(nodeDataArray)) {
                        continue;
                    }
                    // 节点是否关联对象
                    Map<String, String> collect = nodeDataArray.stream().filter(item -> !StringUtils.isEmpty(item.getCiCode()))
                            .collect(Collectors.toMap(item -> item.getKey(), item -> item.getCiCode()));
                    for (ESDiagramLink link : linkDataArray) {
                        String linkJson = link.getLinkJson();
                        JSONObject jsonObject = JSONObject.parseObject(linkJson);
                        String from = jsonObject.getString("from");
                        String to = jsonObject.getString("to");
                        String className = jsonObject.getString("className");
                        // 查询kcp与或活动的关系
                        if (collect.containsKey(from) && collect.containsKey(to) && !StringUtils.isEmpty(className)
                                && Objects.equals(className, "KCP-包含")) {
                            String fromCiCode = collect.get(from);
                            CcCiInfo fromCiInfo = ciSwitchSvc.getCiByCode(fromCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
                            if (fromCiInfo != null && fromCiInfo.getCiClass() != null && Objects.equals(fromCiInfo.getCiClass().getClassCode(), "KCP")) {
                                String toCiCode = collect.get(to);
                                CcCiInfo toCiInfo = ciSwitchSvc.getCiByCode(toCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
                                CcCi ci = toCiInfo.getCi();

                                Map<String, String> fromAttrs = fromCiInfo.getAttrs();
                                if (!kcpInfoVoMap.containsKey(ci.getCiCode())) {
                                    kcpInfoVoMap.put(ci.getCiCode(), fromAttrs.get("控制点名称"));
                                } else {
                                    String name = kcpInfoVoMap.get(ci.getCiCode());
                                    if (StringUtils.isEmpty(name)) {
                                        kcpInfoVoMap.put(ci.getCiCode(), fromAttrs.get("控制点名称"));
                                    } else {
                                        name = name + "，" + fromAttrs.get("控制点名称");
                                        kcpInfoVoMap.put(ci.getCiCode(), name);
                                    }
                                }
                            }
                        }

                        // 查询岗位与角色的关系
                        if (!StringUtils.isEmpty(className) && Objects.equals(className, "由...执行")) {
                            uniqueCodeSet.add(link.getUniqueCode());
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(kcpInfoVoMap)) {
            List<ESCIInfo> esciInfoList = ciSwitchSvc.getCiByCodes(new ArrayList<>(kcpInfoVoMap.keySet()), null, LibType.DESIGN);
            if (!CollectionUtils.isEmpty(esciInfoList)) {
                JSONArray jsonArray = new JSONArray();
                for (ESCIInfo ciInfo : esciInfoList) {
                    Map<String, Object> attrMap = ciInfo.getAttrs();
                    String activeName = "";
                    String activeNum = "";
                    String activeDetail = "";
                    if (attrMap.get("活动名称") != null) {
                        activeName = String.valueOf(attrMap.get("活动名称"));
                    }
                    if (attrMap.get("活动编号") != null) {
                        activeNum = String.valueOf(attrMap.get("活动编号"));
                    }
                    if (attrMap.get("活动说明") != null) {
                        activeDetail = String.valueOf(attrMap.get("活动说明"));
                    }
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("活动编号", activeNum);
                    jsonObject.put("活动名称", activeName);
                    jsonObject.put("活动说明", activeDetail);
                    jsonObject.put("控制点名称", kcpInfoVoMap.get(ciInfo.getCiCode()));
                    jsonArray.add(jsonObject);
                }
                flowSystemFileDto.setKcp(jsonArray.toJSONString());
            }
        }
        if (!CollectionUtils.isEmpty(uniqueCodeSet)) {
            Map<String, Set<String>> ciCodeMap = new HashMap<>();
            List<ESCIRltInfo> rltByUniqueCodes = iciRltSwitchSvc.getRltByUniqueCodes(uniqueCodeSet, null, LibType.DESIGN);
            if (!CollectionUtils.isEmpty(rltByUniqueCodes)) {
                for (ESCIRltInfo rltInfo : rltByUniqueCodes) {
                    ciCodeMap.computeIfAbsent(rltInfo.getSourceCiCode(), key -> new HashSet<>()).add(rltInfo.getTargetCiCode());
                }
            }
            if (!CollectionUtils.isEmpty(ciCodeMap)) {
                List<ESCIInfo> esciInfoList = ciSwitchSvc.getCiByCodes(new ArrayList<>(ciCodeMap.keySet()), null, LibType.DESIGN);
                if (!CollectionUtils.isEmpty(esciInfoList)) {
                    JSONArray array = new JSONArray();
                    for (ESCIInfo ciInfo : esciInfoList) {
                        Set<String> postCiCodeSet = ciCodeMap.get(ciInfo.getCiCode());
                        if (CollectionUtils.isEmpty(postCiCodeSet)) {
                            continue;
                        }
                        List<ESCIInfo> postList = ciSwitchSvc.getCiByCodes(new ArrayList<>(postCiCodeSet), null, LibType.DESIGN);
                        if (CollectionUtils.isEmpty(postList)) {
                            continue;
                        }
                        Map<String, Object> attrMap = ciInfo.getAttrs();
                        String activeNum = "";
                        String activeName = "";
                        String activeDetail = "";
                        String inputInfo = "";
                        String outputInfo = "";
                        String inputForm = "";
                        String outputForm = "";
                        if (attrMap.get("活动编号") != null) {
                            activeNum = String.valueOf(attrMap.get("活动编号"));
                        }
                        if (attrMap.get("活动名称") != null) {
                            activeName = String.valueOf(attrMap.get("活动名称"));
                        }
                        if (attrMap.get("活动说明") != null) {
                            activeDetail = String.valueOf(attrMap.get("活动说明"));
                        }
                        if (attrMap.get("输入") != null) {
                            inputInfo = String.valueOf(attrMap.get("输入"));
                        }
                        if (attrMap.get("输出") != null) {
                            outputInfo = String.valueOf(attrMap.get("输出"));
                        }
                        if (attrMap.get("输入表单") != null) {
                            String inputFormJson = String.valueOf(attrMap.get("输入表单"));
                            if (!StringUtils.isEmpty(inputFormJson)) {
                                JSONArray inputJson = JSONObject.parseArray(inputFormJson);
                                if (!CollectionUtils.isEmpty(inputJson)) {
                                    StringBuilder sb = new StringBuilder();
                                    for (int i = 0; i < inputJson.size(); i++) {
                                        JSONObject jsonObject = inputJson.getJSONObject(i);
                                        String name = jsonObject.getString("primary");
                                        if (Objects.equals(inputJson.size() - 1, i)) {
                                            sb.append(name);
                                        } else {
                                            sb.append(name).append("，");
                                        }
                                    }
                                    inputForm = sb.toString();
                                }
                            }
                        }
                        if (attrMap.get("输出表单") != null) {
                            String outputFormJson = String.valueOf(attrMap.get("输出表单"));
                            if (!StringUtils.isEmpty(outputFormJson)) {
                                JSONArray outputJson = JSONObject.parseArray(outputFormJson);
                                if (!CollectionUtils.isEmpty(outputJson)) {
                                    StringBuilder sb = new StringBuilder();
                                    for (int i = 0; i < outputJson.size(); i++) {
                                        JSONObject jsonObject = outputJson.getJSONObject(i);
                                        String name = jsonObject.getString("primary");
                                        if (Objects.equals(outputJson.size() - 1, i)) {
                                            sb.append(name);
                                        } else {
                                            sb.append(name).append("，");
                                        }
                                    }
                                    outputForm = sb.toString();
                                }
                            }
                        }
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < postList.size(); i++) {
                            Map<String, Object> postMap = postList.get(i).getAttrs();
                            String postName = String.valueOf(postMap.get("角色名称"));
                            if (Objects.equals(postList.size() - 1, i)) {
                                sb.append(postName);
                            } else {
                                sb.append(postName).append(",");
                            }
                        }
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("活动编号", activeNum);
                        jsonObject.put("活动名称", activeName);
                        jsonObject.put("活动说明", activeDetail);
                        jsonObject.put("输入", inputInfo);
                        jsonObject.put("输出", outputInfo);
                        jsonObject.put("输入表单", inputForm);
                        jsonObject.put("输出表单", outputForm);
                        jsonObject.put("执行角色", sb.toString());
                        array.add(jsonObject);
                    }
                    flowSystemFileDto.setActiveIllustrate(array.toJSONString());
                }
            }
        }

        Object remark = processMap.get("备注");
        if (remark != null) {
            flowSystemFileDto.setRemark(String.valueOf(remark));
        }
        return flowSystemFileDto;
    }

    /**
     * 导出流程文件
     */
    public String exportProcessFile(HttpServletResponse response, String ciCode, String diagramIds, String id, LibType libType) {
        Map<String, Map> flowFileNew = new HashMap<>();
        if (!id.isEmpty() && !id.equals("null")) {
            FlowSystemApproveData byId = flowSystemApproveDataDao.getById(Long.valueOf(id));
            flowFileNew = (Map<String, Map>) byId.getLinkCiMap().get("流程文件");
            diagramIds = byId.getDiagramEnergy();
        } else {
            flowFileNew = getFlowFileNew(diagramIds, ciCode, libType);
        }
        Map map = (Map) flowFileNew.get("流程").get("dataList");
        XWPFDocument document = this.getWord(flowFileNew, diagramIds);
        OutputStream out = null;
        try {
            Long dateTimeFolder = ESUtil.getNumberDate();
            File destFolder = new File(localPath + "/" + dateTimeFolder);
            if (!destFolder.exists()) {
                destFolder.mkdirs();
            }
            String destFileName = map.get("流程名称") + "流程文件" + map.get("流程编码") + ".docx";
            File destFile = new File(destFolder, destFileName);
            out = new FileOutputStream(new File(destFile.getCanonicalPath()));
            document.write(out);
            rsmUtils.uploadRsmFromFile(destFile);
            return httpPath + "/" + dateTimeFolder + "/" + destFileName;
        } catch (Exception e) {
            log.info("导出数据失败!" + e);
            throw new BusinessException("导出数据失败!");
        } finally {
            try {
                if (document != null) {
                    document.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                throw new BusinessException("关闭流错误!");
            }
        }
    }

    /**
     * 获取流程发布历史
     */
    public List<FlowProcessSystemPublishHistoryDto> getFlowSystemPublishHistory(
            String ciCode, String publishType) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        if (publishType != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("publishType.keyword", publishType));
        }
        List<FlowProcessSystemPublishHistory> flowSystemPublishHistory = flowProcessSystemPublishHistoryDao
                .getSortListByQueryScroll(boolQueryBuilder, "createTime", false);
        List<FlowProcessSystemPublishHistoryDto> flowProcessSystemPublishHistoryDtos = new ArrayList<>();
        for (int i = 0; i < flowSystemPublishHistory.size(); i++) {
            FlowProcessSystemPublishHistoryDto flowProcessSystemPublishHistoryDto = new FlowProcessSystemPublishHistoryDto();
            FlowProcessSystemPublishHistory flowProcessSystemPublishHistory = flowSystemPublishHistory.get(i);
            BeanUtils.copyProperties(flowProcessSystemPublishHistory, flowProcessSystemPublishHistoryDto);
            String creator = flowProcessSystemPublishHistory.getCreator();
            UserInfo creatorUser = iUserApiSvc.getUserInfoByLoginCode(creator);
            flowProcessSystemPublishHistoryDto.setCreator(creatorUser.getUserName());
            String checkUser = flowProcessSystemPublishHistory.getCheckUser();
            UserInfo checkUserObj = iUserApiSvc.getUserInfoByLoginCode(checkUser);
            flowProcessSystemPublishHistoryDto.setCheckUser(checkUserObj.getUserName());

            String publishType1 = flowProcessSystemPublishHistoryDto.getPublishType();
            if (publishType1.equalsIgnoreCase("submit")) {
                flowProcessSystemPublishHistoryDto.setPublishType("提交");
                flowProcessSystemPublishHistoryDto.setVersionName("SV" + (flowSystemPublishHistory.size() - i));
            } else {
                flowProcessSystemPublishHistoryDto.setPublishType("审批");
                flowProcessSystemPublishHistoryDto.setVersionName("V" + (flowSystemPublishHistory.size() - i));
            }
            flowProcessSystemPublishHistoryDtos.add(flowProcessSystemPublishHistoryDto);
        }
        return flowProcessSystemPublishHistoryDtos;

    }

    /**
     * 生成Word文档
     */
    private XWPFDocument getWord(Map<String, Map> flowFileNew, String diagramIds) {
        XWPFDocument document = new XWPFDocument();
        try {
            Map map = (Map) flowFileNew.get("流程").get("dataList");
            if (map == null || map.isEmpty()) {
                throw new IllegalArgumentException("流程列表为空或不存在");
            }
            // 密级等级 - 左对齐
            createTextParagraph(document, ParagraphAlignment.LEFT, BinaryUtils.isEmpty(map.get("密级等级")) ? "空" : map.get("密级等级").toString(), "000000", 14, true, "宋体");
            // 标题 - 居中对齐
            createTextParagraph(document, ParagraphAlignment.CENTER, BinaryUtils.isEmpty(map.get("标题")) ? "空" : map.get("标题").toString(), "0F3363", 18, true, "宋体");
            // 流程文件 - 居中对齐
            createTextParagraph(document, ParagraphAlignment.CENTER, "流程文件", "F92419", 18, true, "宋体");
            // 流程编码和签发 - 同一行显示并居中
            XWPFParagraph combinedPara = document.createParagraph();
            combinedPara.setAlignment(ParagraphAlignment.CENTER);
            // 流程编码
            createTextRun(combinedPara, "流程编码:\u00A0" + (BinaryUtils.isEmpty(map.get("流程编码")) ? "空" : map.get("流程编码").toString()), "000000", 10);
            combinedPara.createRun().addTab();
            // 添加多个制表符作为分隔，根据需要调整数量以获得期望的间距
            for (int i = 0; i < 5; i++) { // 例如添加5个制表符
                combinedPara.createRun().addTab();
            }
            // 签发
            createTextRun(combinedPara, "签发:\u00A0", "000000", 10);
            XWPFRun valueRun = combinedPara.createRun();
            valueRun.setText(BinaryUtils.isEmpty(map.get("签发")) ? "空" : map.get("签发").toString());
            valueRun.setFontSize(10);
            valueRun.setColor("000000");
            valueRun.setFontFamily("楷体");
            // 添加横线
            XWPFParagraph paragraph = document.createParagraph();
            CTP ctp = paragraph.getCTP();
            CTPBdr pbdr = ctp.addNewPPr().addNewPBdr();
            CTBorder border = CTBorder.Factory.newInstance();
            border.setColor("D9001B");
            border.setSz(BigInteger.valueOf(27));
            border.setVal(STBorder.SINGLE);
            pbdr.setBottom(border);
            // 流程名称 - 居中对齐
            createTextParagraph(document, ParagraphAlignment.CENTER, BinaryUtils.isEmpty(map.get("流程名称")) ? "空" : map.get("流程名称").toString() + "流程说明文件", "000000", 18, true, "微软雅黑");
            createTextParagraph(document, ParagraphAlignment.LEFT, "1 前言", "000000", 10, true, "微软雅黑");
            createTextParagraph(document, ParagraphAlignment.LEFT, "1.1 目的", "000000", 10, true, "微软雅黑");
            String s = map.get("目的").toString();
            if (s != null) {
                s = "\u00A0\u00A0\u00A0\u00A0" + s;
            }
            String s1 = BinaryUtils.isEmpty(map.get("适用范围")) ? "空" : map.get("适用范围").toString();
            if (s1 != null) {
                s1 = "\u00A0\u00A0\u00A0\u00A0" + s1;
            }
            createTextParagraph(document, ParagraphAlignment.LEFT, s, "000000", 8, false, "微软雅黑");
            createTextParagraph(document, ParagraphAlignment.LEFT, "1.2 适用范围", "000000", 10, true, "微软雅黑");
            createTextParagraph(document, ParagraphAlignment.LEFT, s1, "000000", 8, false, "微软雅黑");
            createTable(document, "2 术语", flowFileNew.get("术语"));
            createTable(document, "3 角色、职责与对应岗位", flowFileNew.get("角色、职责与对应岗位"));
            createTextParagraph(document, ParagraphAlignment.LEFT, "4 管理内容与方法", "000000", 10, true, "微软雅黑");
            createTextParagraph(document, ParagraphAlignment.LEFT, "4.1 流程图", "000000", 10, true, "微软雅黑");
            if (!StringUtils.isEmpty(diagramIds)) {
                createTextParagraph(document, ParagraphAlignment.CENTER, BinaryUtils.isEmpty(map.get("流程图标题")) ? "空" : map.get("流程图标题").toString(), "000000", 10, true, "微软雅黑");
                creatFlowChart(document, diagramIds);
            }
            createTable(document, "4.2 流程说明", flowFileNew.get("流程说明"));
            createTable(document, "4.3 裁剪指南", flowFileNew.get("裁剪指南"));
            createTable1(document, "4.4 流程范围", flowFileNew.get("流程范围"));
            createTable1(document, "4.5 流程绩效指标", flowFileNew.get("流程绩效指标"));
            createTextParagraph(document, ParagraphAlignment.LEFT, "4.6 流程接口描述", "000000", 10, true, "微软雅黑");
            createTable(document, "4.6.1 前置流程", flowFileNew.get("前置流程"));
            createTable(document, "4.6.2 后置流程", flowFileNew.get("后置流程"));
            createTable(document, "4.6.3 调用流程", flowFileNew.get("调用流程"));
            createTable(document, "5 相关文件", flowFileNew.get("相关文件"));
            createTable(document, "6 风险", flowFileNew.get("风险"));
            createTable(document, "7 记录保存", flowFileNew.get("记录保存"));
            createTable(document, "版本记录", flowFileNew.get("版本记录"));
            createApprovalInfo(document, flowFileNew);
            return document;
        } catch (IllegalArgumentException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        } catch (BusinessException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            log.error("导出文件信息错误!", e);
            throw new BusinessException("导出文件信息错误!");
        }
    }

    /**
     * 创建文本段落
     */
    private void createTextParagraph(XWPFDocument document, ParagraphAlignment alignment, String text, String color, int fontSize, boolean bold, String fontFamily) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(alignment);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setColor(color);
        run.setFontSize(fontSize);
        run.setFontFamily(fontFamily);
        run.setBold(bold);
        CTP ctp = paragraph.getCTP();
        CTPPr ctppr = ctp.getPPr() == null ? ctp.addNewPPr() : ctp.getPPr();
        CTSpacing spacing = ctppr.addNewSpacing();
        spacing.setAfter(BigInteger.valueOf(10));
        spacing.setBefore(BigInteger.valueOf(10));
    }

    /**
     * 创建表格
     */
    private void createTable(XWPFDocument document, String tableName, Map<String, Object> maps) {
        // 表格标题
        XWPFParagraph tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun tableContext = tableParagraph.createRun();
        tableContext.setText(tableName);
        tableContext.setFontFamily("微软雅黑");
        if (tableName.equals("4.6.1 前置流程") || tableName.equals("4.6.2 后置流程") || tableName.equals("4.6.3 调用流程")) {
            tableContext.setBold(false);
        } else {
            tableContext.setBold(true);
        }
        tableContext.setFontSize(10);
        CTP ctp = tableParagraph.getCTP();
        CTPPr ctppr = ctp.getPPr() == null ? ctp.addNewPPr() : ctp.getPPr();
        CTSpacing spacing = ctppr.addNewSpacing();
        spacing.setAfter(BigInteger.valueOf(10));
        spacing.setBefore(BigInteger.valueOf(10));

        // 创建表格
        XWPFTable table = document.createTable();
        CTTblWidth tableWidth = table.getCTTbl().addNewTblPr().addNewTblW();
        tableWidth.setType(STTblWidth.PCT);
        tableWidth.setW(BigInteger.valueOf(5000));
        //
        //CTTblWidth tblInd = table.getCTTbl().getTblPr().getTblInd();
        //if (tblInd == null) {
        //    tblInd = table.getCTTbl().getTblPr().addNewTblInd();
        //}
        //tblInd.setW(BigInteger.valueOf(0));
        //table.getCTTbl().getTblPr().setTblInd(tblInd);

        // 获取表头和数据
        Object attrsObj = maps.get("attrs");
        String[] attrs = attrsObj instanceof JSONArray ?
                ((JSONArray) attrsObj).toArray(new String[0]) :
                (String[]) attrsObj;
        List<Map> dataList = (List<Map>) maps.get("dataList");
        boolean hasData = dataList != null && !dataList.isEmpty();

        // 表头行
        XWPFTableRow headerRow = table.getRow(0);
        headerRow.setHeight(200);

        // 设置列宽
        CTTblGrid tblGrid = table.getCTTbl().addNewTblGrid();
        int columnCount = attrs.length;
        BigInteger columnWidth = BigInteger.valueOf(5000).divide(BigInteger.valueOf(Math.max(columnCount, 1)));

        // 创建表头
        for (int i = 0; i < attrs.length; i++) {
            XWPFTableCell cell = headerRow.getCell(i);
            if (cell == null) {
                cell = headerRow.addNewTableCell();
            }
            cell.setText(attrs[i]);
            CTTc cttc = cell.getCTTc();
            CTTcPr ctPr = cttc.addNewTcPr();
            ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
            cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
            cell.setColor("D9D9D9");
            cell.setWidth(columnWidth.toString());
            tblGrid.addNewGridCol().setW(columnWidth);
        }

        // 填充数据
        if (hasData) {
            for (int i = 0; i < dataList.size(); i++) {
                XWPFTableRow dataRow = table.createRow();
                dataRow.setHeight(200);
                Map<String, String> rowData = dataList.get(i);

                for (int j = 0; j < attrs.length; j++) {
                    XWPFTableCell cell = dataRow.getCell(j);
                    if (cell == null) {
                        cell = dataRow.addNewTableCell();
                    }
                    String value = rowData.get(attrs[j]);
                    cell.setText(value != null ? value : "");
                    CTTc cttc = cell.getCTTc();
                    CTTcPr ctPr = cttc.addNewTcPr();
                    ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                    cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                    cell.setWidth(columnWidth.toString());
                }
            }
        } else {
            // 添加空行
            XWPFTableRow emptyRow = table.createRow();
            emptyRow.setHeight(400);
            for (int i = 0; i < attrs.length; i++) {
                XWPFTableCell cell = emptyRow.getCell(i);
                if (cell == null) {
                    cell = emptyRow.addNewTableCell();
                }
                cell.setText("无");
                CTTc cttc = cell.getCTTc();
                CTTcPr ctPr = cttc.addNewTcPr();
                ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                cell.setWidth(columnWidth.toString());
            }
        }
    }

    /**
     * 设置单元格属性
     */
    private void setCellProperties(XWPFTableCell cell, BigInteger width) {
        CTTc ctTc = cell.getCTTc();
        CTTcPr tcPr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();
        // 设置单元格宽度
        CTTblWidth cellWidth = tcPr.isSetTcW() ? tcPr.getTcW() : tcPr.addNewTcW();
        cellWidth.setType(STTblWidth.PCT);
        cellWidth.setW(width);
        // 设置垂直居中
        tcPr.addNewVAlign().setVal(STVerticalJc.CENTER);
        // 设置水平居中
        ctTc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
    }

    /**
     * 添加表格后的空白行
     */
    private void addSpacingAfterTable(XWPFDocument document) {
        XWPFParagraph spacingParagraph = document.createParagraph();
        CTP ctpSpacing = spacingParagraph.getCTP();
        CTPPr ctpprSpacing = ctpSpacing.getPPr() == null ? ctpSpacing.addNewPPr() : ctpSpacing.getPPr();
        CTSpacing spacingAfterTable = ctpprSpacing.addNewSpacing();
        spacingAfterTable.setAfter(BigInteger.valueOf(150));
    }

    /**
     * 创建流程图
     */
    private void creatFlowChart(XWPFDocument document, String diagramIds) throws IOException, InvalidFormatException {
        List<ESDiagramDTO> diagrams = diagramSvcV2.queryDiagramByIds(Arrays.asList(diagramIds));
        if (diagrams == null) {
            return;
        }
        ESDiagramDTO esDiagramDTO = diagrams.get(0);
        if (esDiagramDTO != null) {
            // 缩略图
            String icon1 = esDiagramDTO.getDiagram().getIcon1();
            if (icon1 == null) {
                throw new BusinessException("流程图存在问题");
            }
            if (icon1 != null && !icon1.startsWith(httpPath)) {
                icon1 = httpPath + icon1;
                esDiagramDTO.getCreator().setIcon(icon1);
            }
            // 读取图片
            URL url = new URL(icon1);
            URLConnection urlConnection = url.openConnection();
            InputStream inputStream = urlConnection.getInputStream();
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                return;
            }
            String suffix = icon1.substring(icon1.lastIndexOf(".") + 1, icon1.length());
            int picType = Document.PICTURE_TYPE_PNG;
            if (Objects.equals(suffix, "png")) {
                picType = Document.PICTURE_TYPE_PNG;
            } else if (Objects.equals(suffix, "jpg") || Objects.equals(suffix, "jpeg")) {
                picType = Document.PICTURE_TYPE_JPEG;
            } else if (Objects.equals(suffix, "gif")) {
                picType = Document.PICTURE_TYPE_GIF;
            }
            // 获取图片的原始宽高
            int originalWidth = image.getWidth();
            int originalHeight = image.getHeight();
            // 计算缩放后的宽高，保持比例
            double scale = Math.min((double) 330 / originalWidth, (double) 300 / originalHeight);
            int scaledWidth = (int) (originalWidth * scale);
            int scaledHeight = (int) (originalHeight * scale);
            URL url1 = new URL(icon1);
            URLConnection urlConnection1 = url1.openConnection();
            InputStream inputStream1 = urlConnection1.getInputStream();
            XWPFParagraph productP = document.createParagraph();
            productP.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun productInfo = productP.createRun();
            productInfo.addPicture(inputStream1, picType, null, Units.toEMU(scaledWidth), Units.toEMU(scaledHeight));
        }
    }

    /**
     * 创建审批信息
     */
    private void createApprovalInfo(XWPFDocument document, Map<String, Map> flowFileNew) {
        String[] approvalList = {"编写", "部门审查", "业务审查", "集成验证"};

        for (String approvalType : approvalList) {
            List<Map> approvalRecords = (List<Map>) flowFileNew.get(approvalType).get("dataList");
            boolean isFirst = true;

            if (approvalRecords != null && !approvalRecords.isEmpty()) {
                for (Map approvalRecord : approvalRecords) {
                    Map<String, String> approvalInfo = (Map<String, String>) approvalRecord;
                    for (Map.Entry<String, String> entry : approvalInfo.entrySet()) {
                        XWPFParagraph paragraph = document.createParagraph();

                        // 设置段落属性
                        CTP ctp = paragraph.getCTP();
                        CTPPr ppr = ctp.getPPr() != null ? ctp.getPPr() : ctp.addNewPPr();

                        // 设置行高和间距
                        CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();
                        spacing.setLine(BigInteger.valueOf(620));
                        spacing.setLineRule(STLineSpacingRule.EXACT);
                        spacing.setBefore(BigInteger.valueOf(20));
                        spacing.setAfter(BigInteger.valueOf(0));
                        // 设置两个制表位
                        CTTabs tabs = ppr.getTabs() != null ? ppr.getTabs() : ppr.addNewTabs();
                        // 第一个制表位 - 用户名列
                        CTTabStop tabStop1 = tabs.addNewTab();
                        tabStop1.setVal(STTabJc.LEFT);
                        tabStop1.setPos(BigInteger.valueOf(1000));
                        // 第二个制表位 - 时间列
                        CTTabStop tabStop2 = tabs.addNewTab();
                        tabStop2.setVal(STTabJc.LEFT);
                        tabStop2.setPos(BigInteger.valueOf(2800));
                        if (isFirst) {
                            // 审批类型
                            XWPFRun titleRun = paragraph.createRun();
                            titleRun.setText(approvalType + ":");
                            titleRun.setFontSize(8);
                            titleRun.setBold(true);
                            titleRun.setFontFamily("微软雅黑");
                        }
                        // 添加制表符
                        XWPFRun tabRun = paragraph.createRun();
                        tabRun.addTab();
                        // 用户名
                        XWPFRun nameRun = paragraph.createRun();
                        nameRun.setText(entry.getKey());
                        nameRun.setFontSize(10);
                        nameRun.setFontFamily("微软雅黑");
                        // 添加第二个制表符
                        XWPFRun tabRun2 = paragraph.createRun();
                        tabRun2.addTab();
                        // 时间
                        XWPFRun timeRun = paragraph.createRun();
                        timeRun.setText(entry.getValue());
                        timeRun.setFontSize(10);
                        timeRun.setFontFamily("微软雅黑");
                        isFirst = false;
                    }
                }
            } else {
                // 如果没有记录，只显示审批类型
                XWPFParagraph paragraph = document.createParagraph();
                // 设置段落行高和间距
                CTP ctp = paragraph.getCTP();
                CTPPr ppr = ctp.getPPr();
                if (ppr == null) ppr = ctp.addNewPPr();
                CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();
                spacing.setLine(BigInteger.valueOf(620)); // 设置行高为240 twips (单倍行距)
                spacing.setLineRule(STLineSpacingRule.EXACT); // 设置为固定行高
                spacing.setBefore(BigInteger.valueOf(0)); // 段前间距
                spacing.setAfter(BigInteger.valueOf(0));  // 段后间距
                XWPFRun titleRun = paragraph.createRun();
                titleRun.setText(approvalType + ":");
                titleRun.setFontSize(8);
                titleRun.setBold(true);
                titleRun.setFontFamily("微软雅黑");
            }
        }
    }

    /**
     * 获取流程文件新版本
     */
    public Map<String, Map> getFlowFileNew(String diagramIds, String ciCode, LibType libType) {
        Map<String, Map> result = new HashMap<>();
        CcCiInfo ciInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
        if (ciInfo == null) {
            ciInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
        }
        Map<String, Object> stringMapMap = getStringMapMap1(diagramIds, ciCode, libType);
        Map<String, List<CcCiInfo>> stringMapMap1 = (Map<String, List<CcCiInfo>>) stringMapMap.get("dataList");


        List<CcCiInfo> terms = stringMapMap1.getOrDefault("术语", Collections.emptyList());
        List<CcCiInfo> active = stringMapMap1.getOrDefault("活动", Collections.emptyList());
        List<CcCiInfo> roles = stringMapMap1.getOrDefault("岗位角色", Collections.emptyList());
        List<CcCiInfo> preProcesses = stringMapMap1.getOrDefault("前置流程", Collections.emptyList());
        List<CcCiInfo> postProcesses = stringMapMap1.getOrDefault("后置流程", Collections.emptyList());
        List<CcCiInfo> inputForms = stringMapMap1.getOrDefault("表单", Collections.emptyList());
// 术语
        Map<String, Object> termList = new HashMap<>();
        List<Map> dataList = new ArrayList<>();
        String[] attr = {"名称", "定义"};
        termList.put("attrs", attr);
        if (!terms.isEmpty()) {
            for (CcCiInfo info : terms) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("名称", extractPrimaryValues(info.getAttrs().get("名称"), ",", null));
                attrs.put("定义", extractPrimaryValues(info.getAttrs().get("定义"), ",", null));
                dataList.add(safeGetAttrs(attrs));
            }
        }
        termList.put("dataList", dataList);
        result.put("术语", termList);

        // 处理角色、职责与对应岗位
        Map<String, Object> roleData = new HashMap<>();
        List<Map> roleDataList = new ArrayList<>();
        String[] roleAttrs = {"序号", "角色名称", "职责", "对应职位/岗位"};
        roleData.put("attrs", roleAttrs);
        if (!roles.isEmpty()) {
            int i = 1;
            for (CcCiInfo info : roles) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(i++));
                attrs.put("角色名称", extractPrimaryValues(info.getAttrs().get("角色名称"), ",", "角色"));
                attrs.put("职责", extractPrimaryValues(info.getAttrs().get("角色职责"), ",", null));
                attrs.put("对应职位/岗位", extractPrimaryValues(info.getAttrs().get("关联岗位"), ";", null));
                roleDataList.add(safeGetAttrs(attrs));
            }
        }
        roleData.put("dataList", roleDataList);
        result.put("角色、职责与对应岗位", roleData);

        // 处理流程说明
        Map<String, Object> processData = new HashMap<>();
        Map<String, Object> flowData = new HashMap<>();
        List<Map> calledProcesses = new ArrayList<>();
        List<Map> processDataList = new ArrayList<>();
        String[] processAttrs = {"活动序号", "活动名称", "活动内容", "角色", "输入表单", "输入说明", "输出表单", "输出说明"};
        processData.put("attrs", processAttrs);

        if (!active.isEmpty()) {
            Map<String, String> object1 = (Map<String, String>) stringMapMap.get("活动序号");
            List<CcCiInfo> sortedActive = new ArrayList<>(active);
            sortedActive.sort((a, b) -> {
                String codeA = a.getCi().getCiCode();
                String codeB = b.getCi().getCiCode();
                Long orderA = Long.parseLong(object1.getOrDefault(codeA, String.valueOf(Long.MAX_VALUE)));
                Long orderB = Long.parseLong(object1.getOrDefault(codeB, String.valueOf(Long.MAX_VALUE)));
                return orderA.compareTo(orderB);
            });

            int sequenceNumber = 1;
            for (CcCiInfo esciInfo : sortedActive) {
                Map<String, String> detail = new LinkedHashMap<>();
                detail.put("活动序号", String.valueOf(sequenceNumber++));
                detail.put("活动名称", extractPrimaryValues(esciInfo.getAttrs().get("活动名称"), ";", null));
                detail.put("活动内容", extractPrimaryValues(esciInfo.getAttrs().get("活动内容"), ";", null));
                detail.put("角色", extractPrimaryValues(esciInfo.getAttrs().get("责任角色"), ",", null));
                detail.put("输入表单", extractPrimaryValues(esciInfo.getAttrs().get("输入表单"), ",", "表单"));
                detail.put("输入说明", extractPrimaryValues(esciInfo.getAttrs().get("输入说明"), ",", "表单"));
                detail.put("输出表单", extractPrimaryValues(esciInfo.getAttrs().get("输出表单"), ",", "表单"));
                detail.put("输出说明", extractPrimaryValues(esciInfo.getAttrs().get("输出说明"), ",", "表单"));
                processDataList.add(safeGetAttrs(detail));

                if (!StringUtils.isEmpty(esciInfo.getAttrs().get("调用流程"))) {
                    String s1 = esciInfo.getAttrs().get("调用流程");
                    JSONArray array = JSONArray.parseArray(s1);
                    StringBuilder sb = new StringBuilder();
                    Set<String> processedPrimary = new HashSet<>();
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject object = array.getJSONObject(i);
                        String primary = object.getString("primary");
                        // 如果这个 primary 已经处理过，就跳过
                        if (!processedPrimary.add(primary)) {
                            continue;
                        }
                        Map<String, String> processInfo = new LinkedHashMap<>();
                        String[] primaryParts = primary.split("\\|");
                        processInfo.put("流程名称", primaryParts[1]);
                        processInfo.put("流程编码", primaryParts[0]);
                        calledProcesses.add(processInfo);
                    }
                }
            }
        }
        processData.put("dataList", processDataList);
        result.put("流程说明", processData);
        String[] flowAttrs = {"流程名称", "流程编码"};
        flowData.put("attrs", flowAttrs);
        flowData.put("dataList", calledProcesses);
        result.put("调用流程", flowData);

// 处理前置流程
        Map<String, Object> preProcessData = new HashMap<>();
        List<Map> preProcessDataList = new ArrayList<>();
        String[] preProcessAttrs = {"序号", "前置流程", "(本)流程输入", "(本)流程要求"};
        preProcessData.put("attrs", preProcessAttrs);

        if (!preProcesses.isEmpty()) {
            int j = 1;
            for (CcCiInfo info : preProcesses) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(j++));
                attrs.put("前置流程", extractPrimaryValues(info.getAttrs().get("前置流程名称"), ",", "前置流程"));
                String outputs = extractPrimaryValues(info.getAttrs().get("输出"), ",", "表单");
                attrs.put("(本)流程输入", outputs);
                attrs.put("(本)流程要求", extractPrimaryValues(info.getAttrs().get("要求"), ",", "表单"));
                preProcessDataList.add(safeGetAttrs(attrs));
            }
        }
        preProcessData.put("dataList", preProcessDataList);
        result.put("前置流程", preProcessData);

// 处理后置流程
        Map<String, Object> postProcessData = new HashMap<>();
        List<Map> postProcessDataList = new ArrayList<>();
        String[] postProcessAttrs = {"序号", "后置流程", "(本)流程输出", "(本)流程要求"};
        postProcessData.put("attrs", postProcessAttrs);

        if (!postProcesses.isEmpty()) {
            int k = 1;
            for (CcCiInfo info : postProcesses) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(k++));
                attrs.put("后置流程", extractPrimaryValues(info.getAttrs().get("后置流程名称"), ",", "后置流程"));
                String inputs = extractPrimaryValues(info.getAttrs().get("输入"), ",", "表单");
                attrs.put("(本)流程输出", inputs);
                attrs.put("(本)流程要求", extractPrimaryValues(info.getAttrs().get("要求"), ",", "表单"));
                postProcessDataList.add(safeGetAttrs(attrs));
            }
        }
        postProcessData.put("dataList", postProcessDataList);
        result.put("后置流程", postProcessData);

        // 处理表单数据
        Map<String, Object> formData = new HashMap<>();
        List<Map> formDataList = new ArrayList<>();
        String[] formAttrs = {"序号", "文件名称", "文件编码"};
        formData.put("attrs", formAttrs);

        if (!inputForms.isEmpty()) {
            int l = 1;
            for (CcCiInfo info : inputForms) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(l++));
                attrs.put("文件名称", extractPrimaryValues(info.getAttrs().get("表单名称"), ",", "表单"));
                attrs.put("文件编码", extractPrimaryValues(info.getAttrs().get("表单编号"), ",", "表单"));
                formDataList.add(attrs);
            }
        }
        formData.put("dataList", formDataList);
        result.put("相关文件", formData);

// 处理版本记录
        Map<String, Object> versionData = new HashMap<>();
        List<Map> versionDataList = new ArrayList<>();
        String[] versionAttrs = {"版本", "拟制/修订责任人", "拟制/修订日期", "修订内容及理由"};
        versionData.put("attrs", versionAttrs);

        List<FlowProcessSystemPublishHistoryDto> publishedVersions = findOperationList(ciCode, "published");
        Long flowSystemApproveDataId = null;
        FlowProcessSystemPublishHistoryDto latestVersion = null;
        if (!publishedVersions.isEmpty()) {
            for (FlowProcessSystemPublishHistoryDto version : publishedVersions) {
                if (latestVersion == null || version.getCreateTime() > latestVersion.getCreateTime()) {
                    latestVersion = version;
                }
                Map<String, String> versionDetails = new LinkedHashMap<>();
                versionDetails.put("版本", version.getVersionName());
                versionDetails.put("拟制/修订责任人", version.getCreator());
                if (version.getCreateTime() != null) {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
                    Date date = null;
                    try {
                        date = inputFormat.parse(String.valueOf(version.getCreateTime()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    String formattedDate = outputFormat.format(date);
                    versionDetails.put("拟制/修订日期", formattedDate);
                } else {
                    versionDetails.put("拟制/修订日期", "");
                }
                versionDetails.put("修订内容及理由", version.getChangeData());
                versionDataList.add(safeGetAttrs(versionDetails));
            }
            if (latestVersion != null) {
                flowSystemApproveDataId = latestVersion.getFlowSystemApproveDataId();
            }
        }
        versionData.put("dataList", versionDataList);
        result.put("版本记录", versionData);

// 审批记录
        List<Map> departmentReviews = new ArrayList<>();
        List<Map> businessReviews = new ArrayList<>();
        List<Map> integrationReviews = new ArrayList<>();
        String signUser = "";
        if (libType.equals(LibType.DESIGN) && flowSystemApproveDataId != null) {
            FlowSystemApproveData approveData = flowSystemApproveDataDao.getById(flowSystemApproveDataId);
            if (approveData != null) {
                FlowSystemProcessSingData signData = flowSystemProcessSignDataDao.selectOne(
                        QueryBuilders.termQuery("linkPublishProcessInstanceId.keyword", approveData.getProcessInstanceId()));
                if (signData != null && signData.getSignUserCode() != null) {
                    signUser = userApiSvc.getUserInfoByLoginCode(signData.getSignUserCode().toString()).getUserName();
                }
                String processInstanceId = approveData.getProcessInstanceId();
                List<HistoryTaskResponse> historyTasks = flowableFeign.getHistoryTaskByCurrentProcessInstanceId(processInstanceId);
                for (HistoryTaskResponse task : historyTasks) {
                    if (!task.getVariables().get("pass").equals("pass")) {
                        continue;
                    }
                    String taskName = task.getTaskName();
                    Date commitTime = task.getCommitTime();
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
                    String commitTimeString = dateFormat.format(commitTime);
                    String user = task.getVariables().get("user").toString();
                    UserInfo userInfoByLoginCode = userApiSvc.getUserInfoByLoginCode(user);
                    Map<String, String> reviewDetails = new LinkedHashMap<>();
                    reviewDetails.put(userInfoByLoginCode.getUserName(), commitTimeString);

                    switch (taskName) {
                        case "业务域负责人审批":
                            departmentReviews.add(reviewDetails);
                            break;
                        case "业务审查人审批":
                            businessReviews.add(reviewDetails);
                            break;
                        case "集成审查人审批":
                            integrationReviews.add(reviewDetails);
                            break;
                    }
                }
            }
        }
        Map<String, Object> departmentData = new HashMap<>();
        departmentData.put("dataList", departmentReviews);
        result.put("部门审查", departmentData);
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("dataList", businessReviews);
        result.put("业务审查", businessData);
        Map<String, Object> integrationData = new HashMap<>();
        integrationData.put("dataList", integrationReviews);
        result.put("集成验证", integrationData);

        // 场景, 记录保存, 风险, 流程绩效指标
        extracted(ciCode, libType, result);

        Map<String, String> attributes = ciInfo.getAttrs();
        List<Map> integrationReview = new ArrayList<>();
        if (!attributes.get("编写人").isEmpty()) {
            String writers = extractPrimaryValues(attributes.get("编写人"), ",", "编写");
            String[] writerCodes = writers.split(",");
            String publishTime = attributes.get("发布时间");
            String formattedTime = "";
            // 格式化发布时间
            if (publishTime != null && !publishTime.isEmpty()) {
                try {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = inputFormat.parse(publishTime);
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy.MM.dd");
                    formattedTime = outputFormat.format(date);
                } catch (ParseException e) {
                    log.error("日期格式化失败", e);
                }
            }
            // 处理每个编写人
            for (String writerCode : writerCodes) {
                if (!StringUtils.isEmpty(writerCode)) {
                    UserInfo userInfo = userApiSvc.getUserInfoByLoginCode(writerCode.trim());
                    Map<String, String> reviewDetail = new LinkedHashMap<>();
                    reviewDetail.put(userInfo.getUserName(), formattedTime);
                    integrationReview.add(reviewDetail);
                }
            }
        }
        Map<String, Object> departmentData1 = new HashMap<>();
        departmentData1.put("dataList", integrationReview);
        result.put("编写", departmentData1);
        attributes.put("签发", signUser);
        attributes.put("流程图标题", attributes.get("流程编码") + attributes.get("流程名称") + "流程图");
        String versionName = latestVersion == null ? "V0" : latestVersion.getVersionName();
        if (libType.equals(LibType.PRIVATE)) {
            int version = Integer.parseInt(versionName.replaceAll("[^0-9]", ""));
            versionName = "V" + (version + 1);
        }
        attributes.put("流程编码", String.format("NPIC-BP-[%s]-%s", attributes.get("流程编码"), versionName));
        attributes.put("目的", extractPrimaryValues(attributes.get("流程目的"), ",", "表单"));
        attributes.put("输入", extractPrimaryValues(attributes.get("流程输入"), ",", "表单"));
        attributes.put("输出", extractPrimaryValues(attributes.get("流程输出"), ",", "表单"));
        String configType = bmConfigSvc.getConfigType("FLOW_SYSTEM_FILE_HEADER");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(configType)){
            attributes.put("标题", configType);
        }else {
            attributes.put("标题", "北京优锘科技股份有限公司");
        }
        Map<String, Object> flowDetails = new HashMap<>();
        flowDetails.put("dataList", attributes);
        result.put("流程", flowDetails);

        Map<String, Object> flowDetail = new HashMap<>();
        List<Map> detail1 = new ArrayList<>();
        Map<String, String> detail = new LinkedHashMap<>();
        String[] formAttr = {"流程起点", "流程终点", "输入", "输出"};
        detail.put("流程起点", extractPrimaryValues(attributes.get("流程起点"), ",", null));
        detail.put("流程终点", extractPrimaryValues(attributes.get("流程终点"), ",", null));
        detail.put("输入", extractPrimaryValues(attributes.get("输入描述"), ",", "表单"));
        detail.put("输出", extractPrimaryValues(attributes.get("输出描述"), ",", "表单"));
        detail1.add(detail);
        flowDetail.put("attrs", formAttr);
        flowDetail.put("dataList", detail1);
        result.put("流程范围", flowDetail);
        return result;
    }

    public Long processFileAddData(Long id) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
        // 获取快照数据
        FlowSystemApproveData approveData = flowSystemApproveDataDao.selectOne(
                QueryBuilders.termQuery("id", id)
        );
        if (approveData == null||approveData.getLinkCiMap()==null||!approveData.getCiInfo().getCiClass().getClassName().equals("流程")) {
            return 1L;
        }
        // 处理流程文件数据
        Map<String, Map> flowFileMap = new LinkedHashMap<>();
        if (approveData.getLinkCiMap().keySet().contains("流程文件")) {
            flowFileMap = (Map<String, Map>) approveData.getLinkCiMap().get("流程文件");
        } else {
            Map<String, Object> historyMap = getFlowFileHistory(approveData);
            for (Map.Entry<String, Object> entry : historyMap.entrySet()) {
                if (entry.getValue() instanceof Map) {
                    flowFileMap.put(entry.getKey(), (Map<String, Object>) entry.getValue());
                }
            }
        }
        // 设置版本号
        String version1 = (StringUtils.isEmpty(approveData.getProcessInstanceId()) ? "SV" : "V") + approveData.getApproveSuccessVersion();        // 更新流程编码
        Map<String, Object> processData = (Map<String, Object>) flowFileMap.get("流程").get("dataList");
        String processCode = processData.get("流程编码").toString();
        if (processCode != null) {
            int endIndex = processCode.indexOf(']');
            if (endIndex != -1) {
                processData.put("流程编码", processCode.substring(0, endIndex + 1) + "-" + version1);
            }
        }
        // 处理审批相关数据
        List<Map> departmentReviews = new ArrayList<>();
        List<Map> businessReviews = new ArrayList<>();
        List<Map> integrationReviews = new ArrayList<>();
        // 获取提交版本记录用于时间对比
        List<FlowProcessSystemPublishHistoryDto> sumbitVersions = findOperationList(
                approveData.getCiInfo().getCi().getCiCode(), "submit");
        Long compareTime = null;
        // 获取当前版本的提交时间
        if (!sumbitVersions.isEmpty()) {
            Optional<FlowProcessSystemPublishHistoryDto> currentVersion = sumbitVersions.stream()
                    .filter(item -> item.getVersionName().equals("SV" + approveData.getApproveSuccessVersion()))
                    .findFirst();
            if (currentVersion.isPresent()) {
                compareTime = currentVersion.get().getCreateTime();
            }
        }
        if (approveData.getProcessInstanceId() != null) {
            // 处理签发信息
            FlowSystemProcessSingData signData = flowSystemProcessSignDataDao.selectOne(
                    QueryBuilders.termQuery("linkPublishProcessInstanceId.keyword", approveData.getProcessInstanceId())
            );
            processData.put("签发", signData != null && signData.getSignUserCode() != null ?
                    userApiSvc.getUserInfoByLoginCode(signData.getSignUserCode().toString()).getUserName() : "");
            // 处理审批历史
            List<HistoryTaskResponse> historyTasks = flowableFeign.getHistoryTaskByCurrentProcessInstanceId(approveData.getProcessInstanceId());
            for (HistoryTaskResponse task : historyTasks) {
                if (!"pass".equals(task.getVariables().get("pass"))) {
                    continue;
                }
                String formattedDate = LocalDateTime.ofInstant(task.getCommitTime().toInstant(), ZoneId.systemDefault())
                        .format(dateFormatter);
                UserInfo userInfo = userApiSvc.getUserInfoByLoginCode(task.getVariables().get("user").toString());
                Map<String, String> reviewDetails = new LinkedHashMap<>();
                reviewDetails.put(userInfo.getUserName(), formattedDate);

                switch (task.getTaskName()) {
                    case "业务域负责人审批":
                        departmentReviews.add(reviewDetails);
                        break;
                    case "业务审查人审批":
                        businessReviews.add(reviewDetails);
                        break;
                    case "集成审查人审批":
                        integrationReviews.add(reviewDetails);
                        break;
                }
            }
        }
        // 更新审批数据
        flowFileMap.put("部门审查", Collections.singletonMap("dataList", departmentReviews));
        flowFileMap.put("业务审查", Collections.singletonMap("dataList", businessReviews));
        flowFileMap.put("集成验证", Collections.singletonMap("dataList", integrationReviews));
        // 处理版本记录
        Map<String, Object> versionData = new HashMap<>();
        List<Map> versionDataList = new ArrayList<>();
        versionData.put("attrs", new String[]{"版本", "拟制/修订责任人", "拟制/修订日期", "修订内容及理由"});

        List<FlowProcessSystemPublishHistoryDto> publishedVersions = findOperationList(
                approveData.getCiInfo().getCi().getCiCode(), "published");

        if (!publishedVersions.isEmpty()) {
            long currentVersion = approveData.getApproveSuccessVersion();
            for (FlowProcessSystemPublishHistoryDto version : publishedVersions) {
                // 版本号和时间过滤
                String versionStr = version.getVersionName().replaceAll("[^0-9]", "");
                long versionNum = Long.parseLong(versionStr);
                // 根据processInstanceId是否为空使用不同的过滤条件
                boolean shouldInclude = approveData.getProcessInstanceId() != null
                        ? versionNum <= currentVersion
                        : compareTime != null && version.getCreateTime() <= compareTime;

                if (!shouldInclude) {
                    continue;
                }
                Map<String, String> versionDetails = new LinkedHashMap<>();
                versionDetails.put("版本", version.getVersionName());
                versionDetails.put("拟制/修订责任人", version.getCreator());
                versionDetails.put("拟制/修订日期", formatDateTime(version.getCreateTime()));
                versionDetails.put("修订内容及理由", version.getChangeData());
                versionDataList.add(safeGetAttrs(versionDetails));
            }
        }
        versionData.put("dataList", versionDataList);
        flowFileMap.put("版本记录", versionData);

        // 保存更新后的数据
        approveData.getLinkCiMap().put("流程文件", flowFileMap);
        flowSystemApproveDataDao.saveOrUpdate(approveData);
        return 1L;
    }

    private Map<String, Object> getFlowFileHistory(FlowSystemApproveData approveData) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> linkCiMap = approveData.getLinkCiMap();
        CcCiInfo ciInfo = approveData.getCiInfo();
        if (ciInfo == null) {
            throw new MessageException("流程数据为空");
        }
        List<String> keys = Arrays.asList("活动", "术语", "表单", "前置流程", "后置流程", "风险", "档案", "指标", "岗位角色");
        Map<String, CompletableFuture<List<Map<String, String>>>> futures = keys.stream()
                .collect(Collectors.toMap(
                        key -> key,
                        key -> CompletableFuture.supplyAsync(() -> {
                            try {
                                Map<String, Object> dataMap = (Map<String, Object>) linkCiMap.getOrDefault(key, Collections.emptyMap());
                                List<JSONObject> dataList = Optional.ofNullable(dataMap.get("dataList"))
                                        .map(data -> (List<JSONObject>) data)
                                        .orElse(Collections.emptyList());

                                return dataList.stream()
                                        .map(jsonObj -> {
                                            // 获取 attrs 字段，并转换为 Map<String, String>
                                            Map<String, String> attrs = jsonObj.getObject("attrs", Map.class);
                                            return attrs != null ? attrs : Collections.<String, String>emptyMap();
                                        })
                                        .collect(Collectors.toList());

                            } catch (Exception e) {
                                log.error("处理{}数据失败: {}", key, e.getMessage());
                                return Collections.emptyList();
                            }
                        })
                ));

        // 等待所有任务完成
        CompletableFuture.allOf(futures.values().toArray(new CompletableFuture[0])).join();

        // 收集结果
        Map<String, List<Map<String, String>>> collect = futures.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().join()
                ));

// 术语
        Map<String, Object> termList = new HashMap<>();
        List<Map> dataList = new ArrayList<>();
        String[] attr = {"名称", "定义"};
        termList.put("attrs", attr);
        List<Map<String, String>> terms = collect.get("术语");
        if (!terms.isEmpty()) {
            for (Map<String, String> info : terms) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("名称", info.get("名称"));
                attrs.put("定义", info.get("定义"));
                dataList.add(safeGetAttrs(attrs));
            }
        }
        termList.put("dataList", dataList);
        result.put("术语", termList);

        // 处理角色、职责与对应岗位
        Map<String, Object> roleData = new HashMap<>();
        List<Map> roleDataList = new ArrayList<>();
        String[] roleAttrs = {"序号", "角色名称", "职责", "对应职位/岗位"};
        roleData.put("attrs", roleAttrs);
        List<Map<String, String>> roles = collect.get("岗位角色");
        if (!roles.isEmpty()) {
            int i = 1;
            for (Map<String, String> info : roles) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(i++));
                attrs.put("角色名称", info.get("角色名称"));
                attrs.put("职责", info.get("角色职责"));
                attrs.put("对应职位/岗位", extractPrimaryValues(info.get("关联岗位"), ";", "岗位"));
                roleDataList.add(safeGetAttrs(attrs));
            }
        }
        roleData.put("dataList", roleDataList);
        result.put("角色、职责与对应岗位", roleData);

        // 处理流程说明
        Map<String, Object> processData = new HashMap<>();
        Map<String, Object> flowData = new HashMap<>();
        List<Map> calledProcesses = new ArrayList<>();
        List<Map> processDataList = new ArrayList<>();
        String[] processAttrs = {"活动序号", "活动名称", "活动内容", "角色", "输入表单", "输入说明", "输出表单", "输出说明"};
        processData.put("attrs", processAttrs);
        List<Map<String, String>> active = collect.get("活动");
        if (!active.isEmpty()) {
            int a = 1;
            for (Map<String, String> esciInfo : active) {
                if (!StringUtils.isEmpty(esciInfo.get("责任角色"))) {
                    Map<String, String> detail = new LinkedHashMap<>();
                    detail.put("活动序号", String.valueOf(a++));
                    detail.put("活动名称", esciInfo.get("活动名称"));
                    detail.put("活动内容", esciInfo.get("活动说明"));
                    detail.put("角色", extractPrimaryValues(esciInfo.get("责任角色"), ",", "角色"));
                    detail.put("输入表单", extractPrimaryValues(esciInfo.get("输入表单"), ",", "表单"));
                    detail.put("输入说明", esciInfo.get("输入说明"));
                    detail.put("输出表单", extractPrimaryValues(esciInfo.get("输出表单"), ",", "表单"));
                    detail.put("输出说明", esciInfo.get("输出说明"));
                    processDataList.add(safeGetAttrs(detail));
                }
                if (!StringUtils.isEmpty(esciInfo.get("调用流程"))) {
                    String s1 = esciInfo.get("调用流程");
                    JSONArray array = JSONArray.parseArray(s1);
                    StringBuilder sb = new StringBuilder();
                    Set<String> processedPrimary = new HashSet<>();
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject object = array.getJSONObject(i);
                        String primary = object.getString("primary");
                        // 如果这个 primary 已经处理过，就跳过
                        if (!processedPrimary.add(primary)) {
                            continue;
                        }
                        Map<String, String> processInfo = new LinkedHashMap<>();
                        String[] primaryParts = primary.split("\\|");
                        processInfo.put("流程名称", primaryParts[1]);
                        processInfo.put("流程编码", primaryParts[0]);
                        calledProcesses.add(processInfo);
                    }
                }
            }
        }
        processData.put("dataList", processDataList);
        result.put("流程说明", processData);
        String[] flowAttrs = {"流程名称", "流程编码"};
        flowData.put("attrs", flowAttrs);
        flowData.put("dataList", calledProcesses);
        result.put("调用流程", flowData);

// 处理前置流程
        Map<String, Object> preProcessData = new HashMap<>();
        List<Map> preProcessDataList = new ArrayList<>();
        String[] preProcessAttrs = {"序号", "前置流程", "(本)流程输入", "(本)流程要求"};
        preProcessData.put("attrs", preProcessAttrs);
        List<Map<String, String>> preProcesses = collect.get("前置流程");
        if (!preProcesses.isEmpty()) {
            int j = 1;
            for (Map<String, String> info : preProcesses) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(j++));
                attrs.put("前置流程", extractPrimaryValues(info.get("前置流程名称"), ",", "前置流程"));
                String outputs = extractPrimaryValues(info.get("输出"), ",", "表单");
                attrs.put("(本)流程输入", outputs);
                attrs.put("(本)流程要求", info.get("要求"));
                preProcessDataList.add(safeGetAttrs(attrs));
            }
        }
        preProcessData.put("dataList", preProcessDataList);
        result.put("前置流程", preProcessData);

// 处理后置流程
        Map<String, Object> postProcessData = new HashMap<>();
        List<Map> postProcessDataList = new ArrayList<>();
        String[] postProcessAttrs = {"序号", "后置流程", "(本)流程输出", "(本)流程要求"};
        postProcessData.put("attrs", postProcessAttrs);
        List<Map<String, String>> postProcesses = collect.get("后置流程");
        if (!postProcesses.isEmpty()) {
            int k = 1;
            for (Map<String, String> info : postProcesses) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(k++));
                attrs.put("后置流程", extractPrimaryValues(info.get("后置流程名称"), ",", "后置流程"));
                String inputs = extractPrimaryValues(info.get("输入"), ",", "表单");
                attrs.put("(本)流程输出", inputs);
                attrs.put("(本)流程要求", info.get("要求"));
                postProcessDataList.add(safeGetAttrs(attrs));
            }
        }
        postProcessData.put("dataList", postProcessDataList);
        result.put("后置流程", postProcessData);

        // 处理表单数据
        Map<String, Object> formData = new HashMap<>();
        List<Map> formDataList = new ArrayList<>();
        String[] formAttrs = {"序号", "文件名称", "文件编码"};
        formData.put("attrs", formAttrs);
        List<Map<String, String>> inputForms = collect.get("表单");
        if (!inputForms.isEmpty()) {
            int l = 1;
            for (Map<String, String> info : inputForms) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(l++));
                attrs.put("文件名称", info.get("表单名称"));
                attrs.put("文件编码", info.get("表单编号"));
                formDataList.add(attrs);
            }
        }
        formData.put("dataList", formDataList);
        result.put("相关文件", formData);

// 处理版本记录
        Map<String, Object> versionData = new HashMap<>();
        List<Map> versionDataList = new ArrayList<>();
        String[] versionAttrs = {"版本", "拟制/修订责任人", "拟制/修订日期", "修订内容及理由"};
        versionData.put("attrs", versionAttrs);
        List<FlowProcessSystemPublishHistoryDto> publishedVersions = findOperationList(ciInfo.getCi().getCiCode(), "published");
        FlowProcessSystemPublishHistoryDto latestVersion = null;
        if (!publishedVersions.isEmpty()) {
            for (FlowProcessSystemPublishHistoryDto version : publishedVersions) {
                if (latestVersion == null || version.getCreateTime() > latestVersion.getCreateTime()) {
                    latestVersion = version;
                }
                Map<String, String> versionDetails = new LinkedHashMap<>();
                versionDetails.put("版本", version.getVersionName());
                versionDetails.put("拟制/修订责任人", version.getCreator());
                versionDetails.put("拟制/修订日期", String.valueOf(version.getCreateTime()));
                versionDetails.put("修订内容及理由", version.getChangeData());
                versionDataList.add(safeGetAttrs(versionDetails));
            }
        }
        versionData.put("dataList", versionDataList);
        result.put("版本记录", versionData);
        Map<String, Object> departmentData = new HashMap<>();
        departmentData.put("dataList", new ArrayList<>());
        result.put("部门审查", departmentData);
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("dataList", new ArrayList<>()); // 使用新的ArrayList而不是自引用
        result.put("业务审查", businessData);
        Map<String, Object> integrationData = new HashMap<>();
        integrationData.put("dataList", new ArrayList<>()); // 使用新的ArrayList而不是自引用
        result.put("集成验证", integrationData);

        // 处理指标
        String[] categoryAttrs = new String[]{"指标名称", "设置目的与定义", "计算公式", "计量单位", "统计周期", "说明"};
        List<Map> categoryDataList = new ArrayList<>();
        Map<String, Object> zData = new HashMap<>();
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);
        List<Map<String, String>> category = collect.get("指标");
        if (!category.isEmpty()) {
            for (Map<String, String> info : category) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("指标名称", info.get("名称"));
                attrs.put("设置目的与定义", info.get("设置目的与定义"));
                attrs.put("计算公式", info.get("计算公式"));
                attrs.put("计量单位", info.get("计量单位"));
                attrs.put("统计周期", info.get("统计周期"));
                attrs.put("说明", info.get("说明"));
                categoryDataList.add(attrs);
            }
        }
        result.put("流程绩效指标", zData);

// 处理档案
        categoryAttrs = new String[]{"记录名称", "移交责任人", "保存责任人", "保存场所", "归档时间", "保存期限", "到期处理方式"};
        zData = new HashMap<>();
        categoryDataList = new ArrayList<>();
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);
        category = collect.get("档案");
        if (!category.isEmpty()) {
            for (Map<String, String> info : category) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("记录名称", info.get("记录名称"));
                attrs.put("移交责任人", info.get("移交责任人"));
                attrs.put("保存责任人", info.get("保存责任人"));
                attrs.put("保存场所", info.get("保存场所"));
                attrs.put("归档时间", info.get("归档时间"));
                attrs.put("保存期限", info.get("保存期限"));
                attrs.put("到期处理方式", info.get("到期处理方式"));
                categoryDataList.add(attrs);
            }
        }
        result.put("记录保存", zData);

// 处理风险
        categoryAttrs = new String[]{"序号", "风险名称", "风险描述"};
        zData = new HashMap<>();
        categoryDataList = new ArrayList<>();
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);
        category = collect.get("风险");
        if (!category.isEmpty()) {
            int i = 1;
            for (Map<String, String> info : category) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(i++));
                attrs.put("风险名称", info.get("风险名称"));
                attrs.put("风险描述", info.get("风险描述"));
                categoryDataList.add(attrs);
            }
        }
        result.put("风险", zData);

// 处理场景
        Map<String, List<String>> sceneActiveRlt = (Map<String, List<String>>) approveData.getLinkCiMap().get("场景");
        zData = new HashMap<>();
        categoryDataList = new ArrayList<>();
        categoryAttrs = new String[]{"场景", "活动"};
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);

        if (sceneActiveRlt != null) {
            for (Map.Entry<String, List<String>> entry : sceneActiveRlt.entrySet()) {
                Map<String, String> attrs = new LinkedHashMap<>();
                CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(entry.getKey(),
                        SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                if (ciByCode != null) {
                    List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(entry.getValue(),
                            SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                    String activityNames = ciByCodes.stream()
                            .map(c -> (String) c.getAttrs().get("活动名称"))
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));
                    attrs.put("场景", ciByCode.getAttrs().get("场景名称"));
                    attrs.put("活动", activityNames == null ? "" : activityNames);
                    categoryDataList.add(attrs);
                }
            }
        }
        result.put("裁剪指南", zData);

        Map<String, String> attributes = ciInfo.getAttrs();
        List<Map> integrationReview = new ArrayList<>();
        Map<String, String> reviewDetails = new LinkedHashMap<>();
        integrationReview.add(reviewDetails);
        Map<String, Object> departmentData1 = new HashMap<>();
        departmentData1.put("dataList", integrationReview);
        result.put("编写", departmentData1);
        attributes.put("签发", "");
        attributes.put("流程图标题", attributes.get("流程编码") + attributes.get("流程名称") + "流程图");
        String version = latestVersion != null ? "-" + latestVersion.getVersionName() : "";
        attributes.put("流程编码", "NPIC-BP-[" + attributes.get("流程编码") + "]" + version);
        attributes.put("目的", attributes.get("流程目的"));
        attributes.put("输入", attributes.get("流程输入"));
        attributes.put("输出", attributes.get("流程输出"));
        String configType = bmConfigSvc.getConfigType("FLOW_SYSTEM_FILE_HEADER");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(configType)){
            attributes.put("标题", configType);
        }else {
            attributes.put("标题", "北京优锘科技股份有限公司");
        }
        Map<String, Object> flowDetails = new HashMap<>();
        flowDetails.put("dataList", attributes);
        result.put("流程", flowDetails);

        Map<String, Object> flowDetail = new HashMap<>();
        List<Map> detail1 = new ArrayList<>();
        Map<String, String> detail = new LinkedHashMap<>();
        String[] formAttr = {"流程起点", "流程终点", "输入", "输出"};
        detail.put("流程起点", attributes.get("流程起点"));
        detail.put("流程终点", attributes.get("流程终点"));
        detail.put("输入", attributes.get("输入描述"));
        detail.put("输出", attributes.get("输出描述"));
        detail1.add(detail);
        flowDetail.put("dataList", detail1);
        result.put("流程范围", flowDetail);
        return result;
    }

    public String exportFlowManual(HttpServletResponse response, String ciCode, String base64File) {
        CcCiInfo ciInfo = iciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if (ciInfo == null) {
            throw new BusinessException("目标业务域已被删除，请重新选择！");
        }
        try {
            //读取模版
            InputStream templateStream = getClass().getClassLoader().getResourceAsStream("templates/template_v1.docx");
            if (templateStream == null) {
                throw new FileNotFoundException("Template file not found in classpath");
            }
            Map<String, Object> allData = getAllData(ciInfo);
            Map<String, Object> data = this.getTextData(ciInfo, BinaryUtils.isEmpty(allData.get("${img_jcgx}")) ? "" : allData.get("${img_jcgx}").toString());
            XWPFDocument document = new XWPFDocument(templateStream);
            //插入文本
            replaceTextInDoc(document, data);
            //插入表格及图片
            allData.put("${img_jg}", base64File);
            replaceTableInDoc(document, allData);
            //插入尾部数据
            replaceTailDoc(document, allData);
            //生成文件
            // String word = this.getWord(document,"["+ciInfo.getAttrs().get("密级等级")+"]"+ciInfo.getAttrs().get("流程组名称"));
            String word = this.getWord(document, ciInfo.getAttrs().get("流程组名称"));
            return word;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(e.getMessage());
        }
    }

    private Map<String, String> diagramRelaCiMap(Set<String> ciCodes, LibType libType) {
        List<EamDiagramRelationSys> diagramRelationSysList = new ArrayList<>();
        if (libType.equals(LibType.PRIVATE)) {
            for (String ciCode : ciCodes) {
                diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
            }
        } else {
            diagramRelationSysList = eamDiagramRelationSysService.findDiagramRelationSysList(ciCodes, "flowDiagram");
        }

        if (CollectionUtils.isEmpty(diagramRelationSysList)) {
            return null;
        }
        Map<String, String> diagramRelaCiMap = new HashMap<>();
        Map<String, List<EamDiagramRelationSys>> diamSysListMap = diagramRelationSysList.stream().filter(sys -> !StringUtils.isEmpty(sys.getEsSysId()))
                .collect(Collectors.groupingBy(sys -> sys.getEsSysId()));
        for (Map.Entry<String, List<EamDiagramRelationSys>> item : diamSysListMap.entrySet()) {
            List<EamDiagramRelationSys> diagRelaList = item.getValue();
            if (CollectionUtils.isEmpty(diagRelaList)) {
                continue;
            } else if (Objects.equals(diagRelaList.size(), 1)) {
                EamDiagramRelationSys diagramSys = diagRelaList.get(0);
                diagramRelaCiMap.put(diagramSys.getDiagramEnergy(), diagramSys.getEsSysId());
            } else if (diagRelaList.size() > 1) {
                Optional<EamDiagramRelationSys> first = diagRelaList.stream().sorted(new Comparator<EamDiagramRelationSys>() {
                    @Override
                    public int compare(EamDiagramRelationSys o1, EamDiagramRelationSys o2) {
                        return (int) (o2.getModifyTime() - o1.getModifyTime());
                    }
                }).findFirst();
                EamDiagramRelationSys diagramSys = first.get();
                if (diagramSys != null) {
                    diagramRelaCiMap.put(diagramSys.getDiagramEnergy(), diagramSys.getEsSysId());
                }
            }
        }
        return diagramRelaCiMap;
    }

    private List<ESDiagramDTO> esDiagramDTOList(Map<String, String> diagramRelaCiMap) {
        Long[] diagramIds = diagramApiClient.queryDiagramInfoBydEnergy(diagramRelaCiMap.keySet().toArray(new String[0]));
        if (diagramIds == null || diagramIds.length <= 0) {
            return null;
        }
        List<ESDiagramDTO> esDiagramDTOList = diagramApiClient.queryDiagramInfoByIds(diagramIds, null, false, false);
        if (CollectionUtils.isEmpty(esDiagramDTOList)) {
            return null;
        }
        return esDiagramDTOList;
    }

    private void createTextRun(XWPFParagraph paragraph, String text, String color, int fontSize) {
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setColor(color);
        run.setFontSize(fontSize);
    }

    private void createTable1(XWPFDocument document, String tableName, Map maps) {
        // 表格标题
        XWPFParagraph tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun tableContext = tableParagraph.createRun();
        tableContext.setText(tableName);
        tableContext.setFontFamily("微软雅黑");
        tableContext.setBold(true);
        tableContext.setFontSize(10);
        CTP ctp = tableParagraph.getCTP();
        CTPPr ctppr = ctp.getPPr() == null ? ctp.addNewPPr() : ctp.getPPr();
        CTSpacing spacing = ctppr.addNewSpacing();
        spacing.setAfter(BigInteger.valueOf(10));
        spacing.setBefore(BigInteger.valueOf(10));

        // 获取表头和数据
        Object attrsObj = maps.get("attrs");
        String[] attrs = attrsObj instanceof JSONArray ?
                ((JSONArray) attrsObj).toArray(new String[0]) :
                (String[]) attrsObj;
        List<Map> dataList = (List<Map>) maps.get("dataList");
        boolean hasData = dataList != null && !dataList.isEmpty();

        for (Map dataMap : hasData ? dataList : Collections.singletonList(new HashMap<>())) {
            // 创建表格
            XWPFTable table = document.createTable();

            // 设置表格总宽度为100%
            CTTblWidth tableWidth = table.getCTTbl().addNewTblPr().addNewTblW();
            tableWidth.setType(STTblWidth.PCT);
            tableWidth.setW(BigInteger.valueOf(5000));
            //
            //// 设置表格左边距为0
            //CTTblWidth tblInd = table.getCTTbl().getTblPr().getTblInd();
            //if (tblInd == null) {
            //    tblInd = table.getCTTbl().getTblPr().addNewTblInd();
            //}
            //tblInd.setW(BigInteger.valueOf(0));

            // 设置两列等宽
            CTTblGrid tblGrid = table.getCTTbl().addNewTblGrid();
            BigInteger columnWidth = BigInteger.valueOf(2500);
            tblGrid.addNewGridCol().setW(columnWidth);
            tblGrid.addNewGridCol().setW(columnWidth);

            // 检查当前数据行是否所有值都为空
            boolean allEmpty = true;
            if (!dataMap.isEmpty()) {
                for (String attr : attrs) {
                    Object value = dataMap.get(attr);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        allEmpty = false;
                        break;
                    }
                }
            }

            // 为每个单元格设置内容和样式
            for (int i = 0; i < attrs.length; i++) {
                XWPFTableRow row = (i == 0) ? table.getRow(0) : table.createRow();
                row.setHeight(200);

                // 设置第一列(属性名)
                XWPFTableCell keyCell = row.getCell(0);
                keyCell.setText(attrs[i]);
                keyCell.setColor("D9D9D9");
                setCellProperties(keyCell, columnWidth);

                // 设置第二列(属性值)
                XWPFTableCell valueCell = (row.getCell(1) == null) ? row.createCell() : row.getCell(1);
                if (allEmpty) {
                    // 如果所有值为空，显示"无"
                    valueCell.setText("无");
                } else {
                    Object value = dataMap.get(attrs[i]);
                    if (value == null) {
                        valueCell.setText("");
                    } else {
                        String strValue = value.toString();
                        if (!strValue.isEmpty() && strValue.contains("ciCode")) {
                            valueCell.setText(extractPrimaryValues(strValue, ",", attrs[i]));
                        } else {
                            valueCell.setText(strValue);
                        }
                    }
                }
                setCellProperties(valueCell, columnWidth);
            }

            // 在表格后添加间隔
            addSpacingAfterTable(document);
        }
    }

    private String extractPrimaryValues(Object values, String delimiter, String name) {
        if (values == null || values.toString().isEmpty()) {
            return "";
        }
        String valueStr = values.toString();
        try {
            Object jsonObj = JSON.parse(valueStr);
            if (!(jsonObj instanceof JSONArray)) {
                return valueStr;
            }

            JSONArray array = (JSONArray) jsonObj;
            if (array.isEmpty()) {
                return "";
            }
            JSONObject firstObj = array.getJSONObject(0);
            boolean hasRequiredFields = firstObj.containsKey("primary") ||
                    firstObj.containsKey("ciCode") ||
                    firstObj.containsKey("loginCode");
            if (!hasRequiredFields) {
                return valueStr;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                String primary = object.getString("primary");
                String ciCode = object.getString("ciCode");
                String loginCode = object.getString("loginCode");
                if (primary == null && ciCode != null && loginCode == null) {
                    if (sb.length() > 0) {
                        sb.append(delimiter);
                    }
                    CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
                    if (ciByCode == null) {
                        continue;
                    }
                    String targetName = ("输入".equals(name) || "输出".equals(name)) ? "表单" : name;
                    sb.append(ciByCode.getAttrs().get(targetName + "名称"));
                } else if (primary != null || loginCode != null) {
                    if (sb.length() > 0) {
                        sb.append(delimiter);
                    }
                    sb.append(primary != null ? primary : loginCode);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            return valueStr;
        }
    }

    private Map<String, Object> getStringMapMap1(String diagramIds, String ciCode, LibType libType) {
        Map<String, Object> result = new HashMap<>();
        Set<String> featureMaps = new HashSet<>();
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        // 处理图表数据
        if (!StringUtils.isEmpty(diagramIds)) {
            List<ESDiagramDTO> diagrams = diagramSvcV2.queryDiagramByIds(Arrays.asList(diagramIds));
            if (!diagrams.isEmpty()) {
                // 2. 收集所有节点信息
                Set<String> activityCodes = new HashSet<>();
                Map<String, String> map = new HashMap<>();
                ESDiagramInfoDTO diagram = diagrams.get(0).getDiagram();
                List<ESDiagramModel> models = diagram.getModelList();
                for (ESDiagramModel model : models) {
                    for (ESDiagramNode node : model.getNodeDataArray()) {
                        JSONObject nodeData = JSONObject.parseObject(node.getNodeJson());
                        String className = nodeData.getString("classCode");
                        String ciCode1 = nodeData.getString("ciCode");
                        String activeSortNum = String.valueOf(node.getActiveSortNum());
                        if (className != null && ciCode1 != null) {
                            if ("活动".equals(className)) {
                                activityCodes.add(ciCode1);
                                map.put(ciCode1, activeSortNum);
                            } else if ("前置流程".equals(className) || "后置流程".equals(className)) {
                                featureMaps.add(ciCode1);
                            }
                        }
                    }
                }
                result.put("活动序号", map);
                // 处理活动相关数据
                if (!activityCodes.isEmpty()) {
                    featureMaps.addAll(activityCodes);  // 添加活动codes
                    List<ESCIInfo> relatedCis = ciSwitchSvc.getCiByCodes(new ArrayList<>(activityCodes), loginCode, libType);
                    // 并行处理活动属性
                    CompletableFuture<Set<String>> roleFuture = CompletableFuture.supplyAsync(() ->
                            processResult(relatedCis.stream()
                                    .map(ci -> ci.getAttrs().get("责任角色"))
                                    .collect(Collectors.toList())));
                    CompletableFuture<Set<String>> inputFuture = CompletableFuture.supplyAsync(() ->
                            processResult(relatedCis.stream()
                                    .map(ci -> ci.getAttrs().get("输入表单"))
                                    .collect(Collectors.toList())));
                    CompletableFuture<Set<String>> outputFuture = CompletableFuture.supplyAsync(() ->
                            processResult(relatedCis.stream()
                                    .map(ci -> ci.getAttrs().get("输出表单"))
                                    .collect(Collectors.toList())));
                    // 合并结果
                    CompletableFuture.allOf(roleFuture, inputFuture, outputFuture).join();
                    featureMaps.addAll(roleFuture.join());
                    featureMaps.addAll(inputFuture.join());
                    featureMaps.addAll(outputFuture.join());
                }
            }
        }
        // 处理主CI信息
        CcCiInfo mainCiInfo = ciSwitchSvc.getCiByCode(ciCode, loginCode, libType);
        if (mainCiInfo != null) {
            Map<String, String> mainCiAttrs = mainCiInfo.getAttrs();
            // 并行处理主CI属性
            CompletableFuture<Set<String>> mainInputFuture = CompletableFuture.supplyAsync(() ->
                    processResult(Collections.singletonList(mainCiAttrs.get("输入表单"))));
            CompletableFuture<Set<String>> mainOutputFuture = CompletableFuture.supplyAsync(() ->
                    processResult(Collections.singletonList(mainCiAttrs.get("输出表单"))));
            CompletableFuture<Set<String>> termFuture = CompletableFuture.supplyAsync(() ->
                    processResult(Collections.singletonList(mainCiAttrs.get("关联术语"))));
            // 合并结果
            CompletableFuture.allOf(mainInputFuture, mainOutputFuture, termFuture).join();
            featureMaps.addAll(mainInputFuture.join());
            featureMaps.addAll(mainOutputFuture.join());
            featureMaps.addAll(termFuture.join());
        }

        List<ESCIInfo> relatedCis = ciSwitchSvc.getCiByCodes(new ArrayList<>(featureMaps), loginCode, libType);
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(relatedCis, true);

        // 并行处理不同类型的数据
        Map<String, List<CcCiInfo>> categorizedData = ccCiInfos.parallelStream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ci -> {
                    String className = ci.getCiClass().getClassCode();
                    if (className == null) return "other";
                    switch (className) {
                        case "术语":
                            return "术语";
                        case "岗位角色":
                            return "岗位角色";
                        case "前置流程":
                            return "前置流程";
                        case "后置流程":
                            return "后置流程";
                        case "活动":
                            return "活动";
                        case "表单":
                            return "表单";
                        default:
                            return "other";
                    }
                }));

        result.put("dataList", categorizedData);
        return result;
    }

    private Set<String> processResult(List<Object> result) {
        return result.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(str -> {
                    try {
                        return JSONArray.parseArray(str);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(obj -> obj instanceof JSONObject ? ((JSONObject) obj).getString("ciCode") : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private void extracted(String ciCode, LibType libType, Map<String, Map> result) {
        // 处理记录保存、风险、流程绩效指标
        String[] categories = {"风险", "指标", "档案", "场景"};
        for (String category : categories) {
            Map<String, Object> categoryData = new HashMap<>();
            List<Map> categoryDataList = new ArrayList<>();
            String[] categoryAttrs;
            String resultKey;

            // 设置不同类别的属性和结果键
            switch (category) {
                case "指标":
                    categoryAttrs = new String[]{"指标名称", "设置目的与定义", "计算公式", "计量单位", "统计周期", "说明"};
                    resultKey = "流程绩效指标";
                    break;
                case "档案":
                    categoryAttrs = new String[]{"记录名称", "移交责任人", "保存责任人", "保存场所", "归档时间", "保存期限", "到期处理方式"};
                    resultKey = "记录保存";
                    break;
                case "风险":
                    categoryAttrs = new String[]{"序号", "风险名称", "风险描述"};
                    resultKey = "风险";
                    break;
                case "场景":
                    categoryAttrs = new String[]{"场景", "活动"};
                    resultKey = "裁剪指南";
                    break;
                default:
                    continue;
            }
            categoryData.put("attrs", categoryAttrs);

            // 查询数据
            List<FlowSystemAssociatedFeatures> listByQueryScroll = new ArrayList<>();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("ciCode.keyword", ciCode))
                    .filter(QueryBuilders.termQuery("classCode.keyword", category));
            if (libType.equals(LibType.PRIVATE)) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
                listByQueryScroll = flowSystemAssociatedFeaturesPrivateDao.getListByQueryScroll(boolQueryBuilder);
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
                listByQueryScroll = flowSystemAssociatedFeaturesDao.getListByQueryScroll(boolQueryBuilder);
            }
            if (listByQueryScroll != null && !listByQueryScroll.isEmpty()) {
                Set<String> linkedCiCodes = listByQueryScroll.stream()
                        .filter(o -> o.getLinkedCiCode() != null)
                        .map(FlowSystemAssociatedFeatures::getLinkedCiCode)
                        .collect(Collectors.toSet());
                List<ESCIInfo> relatedCis = ciSwitchSvc.getCiByCodes(new ArrayList<>(linkedCiCodes),
                        SysUtil.getCurrentUserInfo().getLoginCode(), libType);
                if (category.equals("场景")) {
                    List<Map<String, Object>> sceneActiveRltByFlowCiCode = getSceneActiveRltByFlowCiCode(ciCode, LibType.PRIVATE);
                    if (!sceneActiveRltByFlowCiCode.isEmpty()) {
                        for (Map<String, Object> stringObjectMap : sceneActiveRltByFlowCiCode) {
                            Map<String, String> attrs = new LinkedHashMap<>();
                            CcCiInfo ciInfo = (CcCiInfo) stringObjectMap.get("sceneInfo");
                            List<String> activeCiCode = (List<String>) stringObjectMap.get("rltActiveCiCode");
                            if (ciInfo != null) {
                                attrs.put("场景", ciInfo.getAttrs().get("场景名称"));
                            }
                            if (activeCiCode != null) {
                                List<ESCIInfo> active = ciSwitchSvc.getCiByCodes(activeCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
                                if (!active.isEmpty()) {
                                    Map activeSortMap = result.get("流程说明");
                                    List<Map> activeSortList = (List<Map>) activeSortMap.get("dataList");
                                    Set<String> activeNames = active.stream().map(c -> (String) c.getAttrs().get("活动名称")).collect(Collectors.toSet());
                                    List<String> arrActiveSortNames = new ArrayList<>();
                                    for (Map map : activeSortList) {
                                        String activeName = (String) map.get("活动名称");
                                        if(activeNames.contains(activeName)){
                                            arrActiveSortNames.add(activeName);
                                        }
                                    }
                                    attrs.put("活动", org.apache.commons.lang3.StringUtils.join(arrActiveSortNames,","));
                                }
                            } else {
                                attrs.put("活动", "");
                            }
                            categoryDataList.add(safeGetAttrs(attrs));
                        }
                    }
                } else {
                    processRelatedCis(category, relatedCis, categoryDataList);
                }
            }
            categoryData.put("dataList", categoryDataList);
            result.put(resultKey, categoryData);
        }
    }

    private void processRelatedCis(String category, List<ESCIInfo> relatedCis, List<Map> categoryDataList) {
        int i = 1;
        for (ESCIInfo relatedCi : relatedCis) {
            Map<String, String> attrs = new LinkedHashMap<>();
            switch (category) {
                case "指标":
                    attrs.put("指标名称", extractPrimaryValues(relatedCi.getAttrs().get("名称"), ",", null));
                    attrs.put("设置目的与定义", extractPrimaryValues(relatedCi.getAttrs().get("设置目的与定义"), ",", null));
                    attrs.put("计算公式", extractPrimaryValues(relatedCi.getAttrs().get("计算公式"), ",", null));
                    attrs.put("计量单位", extractPrimaryValues(relatedCi.getAttrs().get("计量单位"), ",", null));
                    attrs.put("统计周期", extractPrimaryValues(relatedCi.getAttrs().get("统计周期"), ",", null));
                    attrs.put("说明", extractPrimaryValues(relatedCi.getAttrs().get("说明"), ",", null));
                    break;
                case "档案":
                    attrs.put("记录名称", extractPrimaryValues(relatedCi.getAttrs().get("记录名称"), ",", "名称"));
                    attrs.put("移交责任人", extractPrimaryValues(relatedCi.getAttrs().get("移交责任人"), ",", "角色"));
                    attrs.put("保存责任人", extractPrimaryValues(relatedCi.getAttrs().get("保存责任人"), ",", "角色"));
                    attrs.put("保存场所", extractPrimaryValues(relatedCi.getAttrs().get("保存场所"), ",", null));
                    attrs.put("归档时间", extractPrimaryValues(relatedCi.getAttrs().get("归档时间"), ",", null));
                    attrs.put("保存期限", extractPrimaryValues(relatedCi.getAttrs().get("保存期限"), ",", null));
                    attrs.put("到期处理方式", extractPrimaryValues(relatedCi.getAttrs().get("到期处理方式"), ",", null));
                    break;
                case "风险":
                    attrs.put("序号", String.valueOf(i++));
                    attrs.put("风险名称", extractPrimaryValues(relatedCi.getAttrs().get("风险名称"), ",", null));
                    attrs.put("风险描述", extractPrimaryValues(relatedCi.getAttrs().get("风险描述"), ",", null));
                    break;
            }
            categoryDataList.add(safeGetAttrs(attrs));
        }
    }

    public List<Map<String, Object>> getSceneActiveRltByFlowCiCode(String ciCode, LibType libType) {
        //查询当前流程关联的所有场景数据
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        BoolQueryBuilder privateQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("creator.keyword", loginCode))
                .filter(QueryBuilders.termQuery("classCode.keyword", "场景"));
        List<FlowSystemAssociatedFeatures> privateFeatureList = flowSystemAssociatedFeaturesPrivateDao.getListByQuery(privateQueryBuilder);

        List<Map<String, Object>> resultMap = new ArrayList<>();
        if (!CollectionUtils.isEmpty(privateFeatureList)) {
            //拿到所有的场景
            List<CcCiInfo> sceneCiInfos = new ArrayList<>();
            Set<String> sceneCiCodes = new HashSet<>();

            for (FlowSystemAssociatedFeatures ccCiRltInfo : privateFeatureList) {
                sceneCiCodes.add(ccCiRltInfo.getLinkedCiCode());
            }
            //查找场景关联的所有活动(从es库查询)
            Map<String, List<String>> sceneActiveRlt = getSceneActiveRlt(sceneCiCodes, libType);
            for (FlowSystemAssociatedFeatures ccCiRltInfo : privateFeatureList) {
                CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ccCiRltInfo.getLinkedCiCode(), loginCode, LibType.PRIVATE);
                if (!BinaryUtils.isEmpty(ciByCode)) {
                    Map<String, Object> stringObjectHashMap = new HashMap<>();
                    ciByCode.setCiClass(null);
                    ciByCode.setAttrDefs(null);
                    ciByCode.setFixMapping(null);
                    stringObjectHashMap.put("sceneInfo", ciByCode);
                    stringObjectHashMap.put("rltActiveCiCode", sceneActiveRlt.get(ciByCode.getCi().getCiCode()));
                    resultMap.add(stringObjectHashMap);
                }
            }
        }
        return resultMap;
    }

    private Map<String, List<String>> getSceneActiveRlt(Set<String> sceneCiCodes, LibType libType) {
        CcCiClassInfo linkClass = iRltClassSvc.getRltClassByName(1L, "关联");
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        //目标是场景id
        esRltSearchBean.setTargetCiCodes(sceneCiCodes);
        //关联关系
        esRltSearchBean.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
        //源端类型时活动
        esRltSearchBean.setSourceClassIds(Collections.singletonList(activeClass.getCiClass().getId()));
        List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, libType);
        Map<String, List<String>> sceneActiveMap = new HashMap<>();
        for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
            //源端是活动，目标为场景
            CcCiInfo targetCiInfo = ccCiRltInfo.getTargetCiInfo();
            String sceneCiCode = targetCiInfo.getCi().getCiCode();
            List<String> orDefault = sceneActiveMap.getOrDefault(sceneCiCode, new ArrayList<>());
            orDefault.add(ccCiRltInfo.getSourceCiInfo().getCi().getCiCode());
            sceneActiveMap.put(sceneCiCode, orDefault);
        }
        return sceneActiveMap;
    }

    public List<FlowProcessSystemPublishHistoryDto> findOperationList(String ciCode, String publishType) {
        if (StringUtils.isEmpty(ciCode)) {
            throw new BusinessException("参数不能为空!");
        }
        CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);

        // 关联关系 ——> 包含 关系
        Set<String> ciCodeSet = new HashSet<>();
        CcCiClassInfo flow = iciClassSvc.getCiClassByClassCode("流程");
        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode("流程组");
        CcCiClassInfo c2cFlow = iciClassSvc.getCiClassByClassCode("端到端流程");
        Map<Long, String> classIdCodeMap = new HashMap<>();
        classIdCodeMap.put(flow.getCiClass().getId(),flow.getCiClass().getClassCode());
        classIdCodeMap.put(flowGroup.getCiClass().getId(),flowGroup.getCiClass().getClassCode());
        classIdCodeMap.put(c2cFlow.getCiClass().getId(),c2cFlow.getCiClass().getClassCode());
        Map<String, String> ciAndClassMap = new HashMap<>();
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        if (rltClass != null && rltClass.getCiClass() != null && rltClass.getCiClass().getId() != null) {
            List<Long> rltClassIds = new ArrayList<>();
            // Long classId = Optional.ofNullable(rltClass).map(item -> item.getCiClass()).map(item -> item.getId()).get();
            rltClassIds.add(rltClass.getCiClass().getId());
            List<Long> targetIds = new ArrayList<>();
            targetIds.add(flow.getCiClass().getId());
            targetIds.add(flowGroup.getCiClass().getId());
            List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, designCiInfo.getCi().getId(),
                    targetIds, rltClassIds, 0, 10, false);
            if (!CollectionUtils.isEmpty(vcCiRltInfos)) {
                for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos) {
                    CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
                    ciAndClassMap.put(targetCiInfo.getCi().getCiCode(),classIdCodeMap.get(targetCiInfo.getCi().getClassId()));
                }
                ciCodeSet = vcCiRltInfos.stream()
                        .filter(item -> (item.getCiRlt() != null && !StringUtils.isEmpty(item.getCiRlt().getTargetCiCode())))
                        .map(item -> item.getCiRlt().getTargetCiCode()).collect(Collectors.toSet());
            }
        }
        ciAndClassMap.put(designCiInfo.getCi().getCiCode(),designCiInfo.getCiClass().getClassCode());
        ciCodeSet.add(ciCode);

        List<FlowProcessSystemPublishHistoryDto> flowSystemPublishHistoryList = new ArrayList<>();
        //去除状态为已废止的数据
        if (ciCodeSet != null){
            List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(new ArrayList<>(ciCodeSet), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
            for (ESCIInfo ciByCode : ciByCodes) {
                if (CANCELLED.equals(ciByCode.getAttrs().get(RELEASE_STATE))) {
                    ciCodeSet.remove(ciByCode.getCiCode());
                }
            }
        }
        for (String ciCodeInfo : ciCodeSet) {
            List<FlowProcessSystemPublishHistoryDto> newFlowSystemPublishHistoryList = getFlowSystemPublishHistory(ciCodeInfo, publishType);
            if (!CollectionUtils.isEmpty(newFlowSystemPublishHistoryList)) {
                for (FlowProcessSystemPublishHistoryDto flowProcessSystemPublishHistoryDto : newFlowSystemPublishHistoryList) {
                    String ciCode1 = flowProcessSystemPublishHistoryDto.getCiCode();
                    flowProcessSystemPublishHistoryDto.setFlowType(ciAndClassMap.get(ciCode1));
                }
                flowSystemPublishHistoryList.addAll(newFlowSystemPublishHistoryList);
            }
        }
        if (!CollectionUtils.isEmpty(flowSystemPublishHistoryList)) {
            flowSystemPublishHistoryList = flowSystemPublishHistoryList.stream().sorted(new Comparator<FlowProcessSystemPublishHistoryDto>() {
                @Override
                public int compare(FlowProcessSystemPublishHistoryDto o1, FlowProcessSystemPublishHistoryDto o2) {
                    if (o1.getCreateTime() == null || o2.getCreateTime() == null) {
                        return 0;
                    } else if (o2.getCreateTime() > o1.getCreateTime()) {
                        return 1;
                    } else {
                        return -1;
                    }
                }
            }).collect(Collectors.toList());
        }
        return flowSystemPublishHistoryList;
    }

    /**
     * 关联数据导出组装
     *
     * @param ciInfo
     * @return
     */
    private Map<String, Object> getAllData(CcCiInfo ciInfo) {

        Map<String, Object> data = new HashMap<>();

        //查询流程组,流程，指标的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
                FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);

        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        //查询出来业务域中所有关联数据
        List<Long> targetClassList = new ArrayList<>();
        for (ESCIClassInfo esciClassInfo : esciClassInfos) {
            targetClassList.add(esciClassInfo.getId());
        }

        //查询出来业务域中所有关联数据
        CcCiClassInfo rltClass1 = iciRltSwitchSvc.getRltClassByCode("包含");
//        CcCiClassInfo rltClass2 = iciRltSwitchSvc.getRltClassByCode("关联");
        List<Long> rltClassIds = new ArrayList<>();
        rltClassIds.add(rltClass1.getCiClass().getId());

        List<VcCiRltInfo> queryUpAndDownRlts = iciRltSwitchSvc.queryUpAndDownRlts(LibType.DESIGN, Collections.singletonList(ciInfo.getCi().getId()),
                targetClassList, rltClassIds, 0, 8, false);
        //组装流程组层级
        List<VcCiRltInfo> lczList = queryUpAndDownRlts.stream().filter(item -> classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId().equals(item.getTargetCiInfo().getCi().getClassId())).collect(Collectors.toList());
        Map<Long, List<VcCiRltInfo>> collect = lczList.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        List<VcCiRltInfo> list = collect.get(ciInfo.getCi().getId());
        list = list == null ? new ArrayList<VcCiRltInfo>() : list;
        //创建一个排序规则
        //创建一个排序规则
        Comparator<VcCiRltInfo> comparator = new Comparator<VcCiRltInfo>() {
            @Override
            public int compare(VcCiRltInfo o1, VcCiRltInfo o2) {
                String version1 = o1.getTargetCiInfo().getAttrs().get("流程组编码");
                String version2 = o2.getTargetCiInfo().getAttrs().get("流程组编码");

                String[] parts1 = version1.split("\\.");
                String[] parts2 = version2.split("\\.");

                int length = Math.max(parts1.length, parts2.length);

                for (int i = 0; i < length; i++) {
                    long num1 = i < parts1.length ? Long.parseLong(parts1[i]) : 0;
                    long num2 = i < parts2.length ? Long.parseLong(parts2[i]) : 0;

                    if (num1 < num2) {
                        return -1;
                    } else if (num1 > num2) {
                        return 1;
                    }
                }
                // 如果所有部分都相等，则返回0
                return 0;
            }
        };
        list.sort(comparator);
        List<VcCiRltInfo> allList = new ArrayList<VcCiRltInfo>();
        allList.addAll(list);
        List<VcCiRltInfo> levelList = list;
        addListAll(allList, collect, levelList);

        data.put("lczMap", collect);
        data.put("allList", allList);

        List<String> ciCodes = lczList.stream().map(lcz -> lcz.getTargetCiInfo().getCi().getCiCode()).collect(Collectors.toList());
        //流程
        List<VcCiRltInfo> lcList = queryUpAndDownRlts.stream().filter(item -> classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId().equals(item.getTargetCiInfo().getCi().getClassId())).collect(Collectors.toList());

        Map<String, Map<String, Object>> processInfo = processInfo(lcList);

        Map<Long, List<VcCiRltInfo>> lcMap = lcList.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        data.put("lcMap", lcMap);

        Map<String, List<ESCIInfo>> bdInputMap = new HashMap<String, List<ESCIInfo>>();//输入表单
        Map<String, List<ESCIInfo>> bdOutputMap = new HashMap<String, List<ESCIInfo>>();//输出表单

        List<ESCIInfo> bzList = new ArrayList<ESCIInfo>(); //标准
        List<ESCIInfo> zdList = new ArrayList<ESCIInfo>();//制度
        List<ESCIInfo> gwList = new ArrayList<ESCIInfo>();//岗位角色
        for (Map.Entry<String, Map<String, Object>> entry : processInfo.entrySet()) {
            String code = entry.getKey();
            Map<String, Object> value = entry.getValue();
            List<ESCIInfo> output = value.get("相关输出表单") == null ? new ArrayList<ESCIInfo>() : (List<ESCIInfo>) value.get("相关输出表单");
            List<ESCIInfo> input = value.get("相关输入表单") == null ? new ArrayList<ESCIInfo>() : (List<ESCIInfo>) value.get("相关输入表单");
            bzList.addAll(value.get("相关标准") == null ? new ArrayList<ESCIInfo>() : (List<ESCIInfo>) value.get("相关标准"));
            zdList.addAll(value.get("相关制度") == null ? new ArrayList<ESCIInfo>() : (List<ESCIInfo>) value.get("相关制度"));
            gwList.addAll(value.get("相关岗位角色") == null ? new ArrayList<ESCIInfo>() : (List<ESCIInfo>) value.get("相关岗位角色"));
            List<ESCIInfo> inputList = bdInputMap.getOrDefault(code, new ArrayList<ESCIInfo>());
            inputList.addAll(input);
            List<ESCIInfo> outputList = bdOutputMap.getOrDefault(code, new ArrayList<ESCIInfo>());
            outputList.addAll(output);
            bdInputMap.put(code, inputList);
            bdOutputMap.put(code, outputList);
        }

        data.put("bdInputMap", bdInputMap);
        data.put("bdOutputMap", bdOutputMap);

        //去重
        Map<Long, ESCIInfo> bzdistinctOrdersMap = bzList.stream()
                .collect(Collectors.toMap(
                        ESCIInfo -> ESCIInfo.getId(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        Map<Long, ESCIInfo> zbdistinctOrdersMap = zdList.stream()
                .collect(Collectors.toMap(
                        ESCIInfo -> ESCIInfo.getId(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        data.put("${table_bz}", new ArrayList<>(bzdistinctOrdersMap.values()));
        data.put("${table_zd}", new ArrayList<>(zbdistinctOrdersMap.values()));
        Map<Long, ESCIInfo> gwdistinctOrdersMap = gwList.stream()
                .collect(Collectors.toMap(
                        ESCIInfo -> ESCIInfo.getId(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        List<ESCIInfo> arrayList = new ArrayList<>(gwdistinctOrdersMap.values());
        // 使用Comparator和lambda表达式进行排序
        List<ESCIInfo> sortedList = arrayList.stream()
                .sorted(Comparator.comparing(
                        eSCIInfo -> {
                            Object roleNameObj = eSCIInfo.getAttrs().get("角色名称");
                            if (roleNameObj instanceof String) {
                                return (String) roleNameObj;
                            } else {
                                // 处理非String类型的情况，例如返回一个默认值或抛出异常
                                // 这里我们返回一个空字符串作为默认值
                                return "";
                            }
                        },
                        String::compareToIgnoreCase
                )).collect(Collectors.toList());
        data.put("${table_gw}", sortedList);
        //查询集成关系图关联关系
        ciCodes.add(ciInfo.getCi().getCiCode());
        List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findDiagramRelationSysList(ciCodes, "integrationDiagram");
        Map<String, String> sysMap = diagramRelationSysList.stream().collect(Collectors.toMap(EamDiagramRelationSys::getEsSysId, EamDiagramRelationSys::getDiagramEnergy, (k1, k2) -> k1));
        data.put("sysMap", sysMap);
        String diagramEnergy = sysMap.get(ciInfo.getCi().getCiCode());


        Set<String> diagramEnergyIds = diagramRelationSysList.stream().filter(eamDiagramRelationSys -> !StringUtils.isEmpty(eamDiagramRelationSys.getDiagramEnergy()))
                .map(eamDiagramRelationSys -> eamDiagramRelationSys.getDiagramEnergy()).collect(Collectors.toSet());
        //集成关系图列表
        List<ESDiagram> designEsDiagramsList = diagramApiClient.selectByIds(diagramEnergyIds, null, null);

        Map<String, ESDiagram> designEsDiagramsMap = designEsDiagramsList.stream().collect(Collectors.toMap(ESDiagram::getDEnergy, v -> v, (k1, k2) -> k1));
        data.put("designEsDiagramsMap", designEsDiagramsMap);
        if (SysUtil.StringUtil.isBack(diagramEnergy)) {
            data.put("${img_jcgx}", "");
        } else {
            data.put("${img_jcgx}", (SysUtil.StringUtil.isBack(designEsDiagramsMap.get(diagramEnergy).getIcon1()) ? "" : designEsDiagramsMap.get(diagramEnergy).getIcon1()));
        }
        return data;
    }

    /**
     * 递归-做层级排序合成一个list
     *
     * @param allList
     * @param collect
     * @param levelList
     */
    private void addListAll(List<VcCiRltInfo> allList, Map<Long, List<VcCiRltInfo>> collect, List<VcCiRltInfo> levelList) {
        //创建一个排序规则
        Comparator<VcCiRltInfo> comparator = new Comparator<VcCiRltInfo>() {
            @Override
            public int compare(VcCiRltInfo o1, VcCiRltInfo o2) {
                String version1 = o1.getTargetCiInfo().getAttrs().get("流程组编码");
                String version2 = o2.getTargetCiInfo().getAttrs().get("流程组编码");

                String[] parts1 = version1.split("\\.");
                String[] parts2 = version2.split("\\.");

                int length = Math.max(parts1.length, parts2.length);

                for (int i = 0; i < length; i++) {
                    long num1 = i < parts1.length ? Long.parseLong(parts1[i]) : 0;
                    long num2 = i < parts2.length ? Long.parseLong(parts2[i]) : 0;

                    if (num1 < num2) {
                        return -1;
                    } else if (num1 > num2) {
                        return 1;
                    }
                }
                // 如果所有部分都相等，则返回0
                return 0;
            }
        };
        List<VcCiRltInfo> nextList = new ArrayList<VcCiRltInfo>();
        for (VcCiRltInfo vcCiRltInfo2 : levelList) {
            Long id = vcCiRltInfo2.getTargetCiInfo().getCi().getId();
            List<VcCiRltInfo> list2 = collect.get(id);
            if (!CollectionUtils.isEmpty(list2)) {
                list2.sort(comparator);
                nextList.addAll(list2);
            }
        }
        if (!CollectionUtils.isEmpty(nextList)) {
            allList.addAll(nextList);
            levelList = nextList;
            addListAll(allList, collect, levelList);
        }

    }

    /**
     * 插入表格及图片
     *
     * @param doc
     * @param dataMap
     * @throws Exception
     */
    private void replaceTableInDoc(XWPFDocument doc, Map<String, Object> dataMap) throws Exception {

        List<XWPFParagraph> paragraphs = doc.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph para = paragraphs.get(i);
            List<XWPFRun> runs = para.getRuns();
            for (XWPFRun run : runs) {
                String text = run.getText(0);
                if (text != null && containsPlaceholder(text, dataMap.keySet())) {
                    if (text.contains("table")) {
                        //表格添加
                        // 移除占位符文本
                        run.setText("", 0);
                        // 创建一个新的表格并添加到文档中
                        XmlCursor cursor = para.getCTP().newCursor();
                        // 在指定游标位置插入表格
                        XWPFTable table = doc.insertNewTbl(cursor);

                        CTTblWidth tableWidth = table.getCTTbl().addNewTblPr().addNewTblW();
                        tableWidth.setType(STTblWidth.PCT);
                        tableWidth.setW(BigInteger.valueOf(5000));

                        //CTTblWidth tblInd = table.getCTTbl().getTblPr().getTblInd();
                        //if (tblInd == null) {
                        //    tblInd = table.getCTTbl().getTblPr().addNewTblInd();
                        //}
                        //tblInd.setW(BigInteger.valueOf(0));
                        //table.getCTTbl().getTblPr().setTblInd(tblInd);

                        // 获取表头和数据

                        // 表头行
                        XWPFTableRow headerRow = table.getRow(0);
                        headerRow.setHeight(200);

                        // 设置列宽
                        CTTblGrid tblGrid = table.getCTTbl().addNewTblGrid();
                        int columnCount = 2;
                        BigInteger columnWidth = BigInteger.valueOf(5000).divide(BigInteger.valueOf(Math.max(columnCount, 1)));

                        // 创建表头
                        for (int a = 0; a < 2; a++) {
                            XWPFTableCell cell = headerRow.getCell(a);
                            if (cell == null) {
                                cell = headerRow.addNewTableCell();
                            }
                            CTTc cttc = cell.getCTTc();
                            CTTcPr ctPr = cttc.addNewTcPr();
                            ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                            cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                            cell.setColor("D9D9D9");
                            cell.setWidth(columnWidth.toString());
                            tblGrid.addNewGridCol().setW(columnWidth);
                        }
                        XWPFTableRow row = table.getRow(0);
                        List<ESCIInfo> data = null;

                        String cellNameStr1 = "";
                        String cellNameStr2 = "";

                        String cellStr1 = "";
                        String cellStr2 = "";
                        if (text.contains("table_bz")) {
                            data = (List<ESCIInfo>) dataMap.get("${table_bz}");
                            cellNameStr1 = "标准名称";
                            cellNameStr2 = "标准编号";

                            cellStr1 = "标准条目名称";
                            cellStr2 = "标准编号";
                        } else if (text.contains("table_zd")) {
                            data = (List<ESCIInfo>) dataMap.get("${table_zd}");
                            cellNameStr1 = "制度名称";
                            cellNameStr2 = "制度编号";

                            cellStr1 = "制度名称";
                            cellStr2 = "制度编号";
                        } else if (text.contains("table_gw")) {
                            data = (List<ESCIInfo>) dataMap.get("${table_gw}");
                            cellNameStr1 = "角色名称";
                            cellNameStr2 = "描述";

                            cellStr1 = "角色名称";
                            cellStr2 = "描述";
                        }
                        row.getCell(0).setText(cellNameStr1);
                        row.getCell(1).setText(cellNameStr2);
                        for (ESCIInfo info : data) {
                            XWPFTableRow row2 = table.createRow();
                            for (int col = 0; col < row2.getTableCells().size(); col++) {
                                XWPFTableCell cell = row2.getCell(col);
                                String value = "";
                                Map<String, Object> attrs = info.getAttrs();
                                if (col == 0) {
                                    value = attrs.get(cellStr1) == null ? "-" : (String) attrs.get(cellStr1);
                                } else {
                                    value = attrs.get(cellStr2) == null ? "-" : (String) attrs.get(cellStr2);
                                }
                                cell.setText(value);
                            }
                        }
                    } else if (text.contains("img_jcgx")) {
                        // 读取图片
                        String icon1 = (String) dataMap.get("${img_jcgx}");
                        if (SysUtil.StringUtil.isNotBack(icon1)) {
                            run.setText("", 0);
//                            icon1 = httpResouceUrl + icon1;
                            File destFolder = new File(localPath + "/" + icon1);
                            String base64String = Base64Utils.fileToBase64Str(destFolder);
                            // 解码Base64字符串为字节数组
                            byte[] decodedBytes = Base64.getDecoder().decode(base64String);
                            // 将字节数组转换为InputStream
                            InputStream is = new ByteArrayInputStream(decodedBytes);
                            // 使用图片ID创建并添加图片到运行中
                            BufferedImage image = ImageIO.read(is);
                            // 获取图片的原始宽高
                            int originalWidth = image.getWidth();
                            int originalHeight = image.getHeight();
                            // 计算缩放后的宽高，保持比例
                            double scale = Math.min((double) 430 / originalWidth, (double) 400 / originalHeight);
                            int scaledWidth = (int) (originalWidth * scale);
                            int scaledHeight = (int) (originalHeight * scale);

                            is.reset();
                            String pictureIdx = doc.addPictureData(is, XWPFDocument.PICTURE_TYPE_PNG);
                            is.close(); // 关闭输入流
                            run.addPicture(new ByteArrayInputStream(doc.getPictureDataByID(pictureIdx).getData()),
                                    doc.getPictureDataByID(pictureIdx).getPictureType(), pictureIdx + "image.png", // 图片的文件名（在Word文档中显示）
                                    Units.toEMU(scaledWidth), // 图片宽度，单位EMU
                                    Units.toEMU(scaledHeight)); // 图片高度，单位EMU
                        } else {
                            run.setText("无", 0);
                        }
                    } else if (text.contains("img_jg")) {
                        run.setText("", 0);
                        // 读取图片（base64）
                        String base64String = (String) dataMap.get("${img_jg}");
                        // 解码Base64字符串为字节数组
                        byte[] decodedBytes = Base64.getDecoder().decode(base64String);
                        // 将字节数组转换为InputStream
                        InputStream is = new ByteArrayInputStream(decodedBytes);
                        // 使用图片ID创建并添加图片到运行中
                        BufferedImage image = ImageIO.read(is);
                        // 获取图片的原始宽高
                        int originalWidth = image.getWidth();
                        int originalHeight = image.getHeight();
                        // 计算缩放后的宽高，保持比例
                        double scale = Math.min((double) 430 / originalWidth, (double) 400 / originalHeight);
                        int scaledWidth = (int) (originalWidth * scale);
                        int scaledHeight = (int) (originalHeight * scale);

                        is.reset();
                        String pictureIdx = doc.addPictureData(is, XWPFDocument.PICTURE_TYPE_PNG);
                        is.close(); // 关闭输入流
                        run.addPicture(new ByteArrayInputStream(doc.getPictureDataByID(pictureIdx).getData()),
                                doc.getPictureDataByID(pictureIdx).getPictureType(), pictureIdx + "image.png", // 图片的文件名（在Word文档中显示）
                                Units.toEMU(scaledWidth), // 图片宽度，单位EMU
                                Units.toEMU(scaledHeight)); // 图片高度，单位EMU
                    }
                }
            }
        }
    }

    /**
     * 插入尾部内容
     *
     * @param doc
     * @param dataMap
     * @throws Exception
     */
    private void replaceTailDoc(XWPFDocument doc, Map<String, Object> dataMap) throws Exception {
        //创建一个排序规则
        Comparator<VcCiRltInfo> comparator = new Comparator<VcCiRltInfo>() {
            @Override
            public int compare(VcCiRltInfo o1, VcCiRltInfo o2) {
                String version1 = o1.getTargetCiInfo().getAttrs().get("流程组编码");
                String version2 = o2.getTargetCiInfo().getAttrs().get("流程组编码");

                String[] parts1 = version1.split("\\.");
                String[] parts2 = version2.split("\\.");

                int length = Math.max(parts1.length, parts2.length);

                for (int i = 0; i < length; i++) {
                    long num1 = i < parts1.length ? Long.parseLong(parts1[i]) : 0;
                    long num2 = i < parts2.length ? Long.parseLong(parts2[i]) : 0;

                    if (num1 < num2) {
                        return -1;
                    } else if (num1 > num2) {
                        return 1;
                    }
                }
                // 如果所有部分都相等，则返回0
                return 0;
            }
        };
        Map<Long, List<VcCiRltInfo>> lczMap = (Map<Long, List<VcCiRltInfo>>) dataMap.get("lczMap");//流程组层级map
        List<VcCiRltInfo> lczList = (List<VcCiRltInfo>) dataMap.get("allList");//排序好的流程组
        Map<String, String> sysMap = (Map<String, String>) dataMap.get("sysMap");//视图-ciId与designEsDiagrams
        Map<String, ESDiagram> designEsDiagramsMap = (Map<String, ESDiagram>) dataMap.get("designEsDiagramsMap");//视图map

        Map<Long, List<VcCiRltInfo>> lcMap = (Map<Long, List<VcCiRltInfo>>) dataMap.get("lcMap");//流程层级map

        Map<String, List<ESCIInfo>> bdInputMap = (Map<String, List<ESCIInfo>>) dataMap.get("bdInputMap");//输入表单层级map
        Map<String, List<ESCIInfo>> bdOutputMap = (Map<String, List<ESCIInfo>>) dataMap.get("bdOutputMap");//输出表单层级map
        for (VcCiRltInfo vcCiRltInfo : lczList) {
            Long ciId = vcCiRltInfo.getTargetCiInfo().getCi().getId();
            String ciCode = vcCiRltInfo.getTargetCiInfo().getCi().getCiCode();
            //集成关系图视图
            ESDiagram esDiagram = null;
            String design = sysMap.get(ciCode);
            if (SysUtil.StringUtil.isNotBack(design)) {
                esDiagram = designEsDiagramsMap.get(design);
            }
            //下一层级（不能用属性需要组装）
            List<VcCiRltInfo> zlczList = lczMap.get(ciId);
            String nextFlow = "";
            if (!CollectionUtils.isEmpty(zlczList)) {
                zlczList.sort(comparator);
                nextFlow = zlczList.stream()
                        .map(flow -> flow.getTargetCiInfo().getAttrs().get("流程组编码") + " " + flow.getTargetCiInfo().getAttrs().get("流程组名称"))
                        .collect(Collectors.joining(","));
            }

            List<ESCIInfo> bdInput = new ArrayList<ESCIInfo>();//输入表单
            List<ESCIInfo> bdOutput = new ArrayList<ESCIInfo>();//输出表单

            //下一级末端流程
            List<VcCiRltInfo> lcList = lcMap.get(ciId);
            if (!CollectionUtils.isEmpty(lcList)) {
                for (VcCiRltInfo flow : lcList) {
                    String code = flow.getTargetCiInfo().getCi().getCiCode();
                    if (!CollectionUtils.isEmpty(bdInputMap.get(code))) {
                        bdInput.addAll(bdInputMap.get(code));
                    }
                    if (!CollectionUtils.isEmpty(bdOutputMap.get(code))) {
                        bdOutput.addAll(bdOutputMap.get(code));
                    }
                }
            }
            Map<Long, ESCIInfo> disBdInputMap = bdInput.stream()
                    .collect(Collectors.toMap(
                            ESCIInfo -> ESCIInfo.getId(),
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
            bdInput = new ArrayList<>(disBdInputMap.values());

            Map<Long, ESCIInfo> disBdOutputMap = bdOutput.stream()
                    .collect(Collectors.toMap(
                            ESCIInfo -> ESCIInfo.getId(),
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
            bdOutput = new ArrayList<>(disBdOutputMap.values());

            String flowCode = vcCiRltInfo.getTargetCiInfo().getAttrs().get("流程组编码");
            String flowName = vcCiRltInfo.getTargetCiInfo().getAttrs().get("流程组名称");

            /**-------添加小标题--------**/
            XWPFParagraph paragraph = doc.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(flowCode + " " + flowName);
            // 设置字体加粗
            run.setBold(true);
            // 设置字体大小（三号字体大约对应于16磅）
            run.setFontSize(16);
            // 设置段落对齐方式为居中（注意：这是对段落的设置，而不是对run的设置）
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            run.addBreak();

            /**-------插入表格--------**/
            XWPFTable table = doc.createTable(5, 4);
            CTTblWidth tableWidth = table.getCTTbl().addNewTblPr().addNewTblW();
            tableWidth.setType(STTblWidth.PCT);
            tableWidth.setW(BigInteger.valueOf(5000));

            //CTTblWidth tblInd = table.getCTTbl().getTblPr().getTblInd();
            //if (tblInd == null) {
            //    tblInd = table.getCTTbl().getTblPr().addNewTblInd();
            //}
            //tblInd.setW(BigInteger.valueOf(0));
            //table.getCTTbl().getTblPr().setTblInd(tblInd);


            // 表头行
            XWPFTableRow headerRow = table.getRow(0);
            headerRow.setHeight(200);

            // 设置列宽
            CTTblGrid tblGrid = table.getCTTbl().addNewTblGrid();
            int columnCount = 4;
            BigInteger columnWidth = BigInteger.valueOf(5000).divide(BigInteger.valueOf(Math.max(columnCount, 1)));

            mergeCellsHorizontally(table, 3, 1, 3); // 行索引从0开始，列索引也从0开始
            mergeCellsHorizontally(table, 4, 1, 3); // 行索引从0开始，列索引也从0开始

            //填充数据
            for (int i = 0; i < 5; i++) {
                XWPFTableRow row = table.getRow(i);
                List<XWPFTableCell> tableCells = row.getTableCells();
                for (int j = 0; j < tableCells.size(); j++) {

                    XWPFTableCell cell = row.getCell(j);
                    CTTc cttc = cell.getCTTc();
                    CTTcPr ctPr = cttc.addNewTcPr();
                    ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                    cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                    cell.setWidth(columnWidth.toString());
                    tblGrid.addNewGridCol().setW(columnWidth);
                    if (j % 2 == 0) {
                        cell.setColor("D9D9D9");
                    }

                }
                switch (i) {
                    case 0:
                        row.getCell(0).setText("流程组定义");
                        row.getCell(1).setText(vcCiRltInfo.getTargetCiInfo().getAttrs().get("流程组定义"));
                        row.getCell(2).setText("流程组目的");
                        row.getCell(3).setText(vcCiRltInfo.getTargetCiInfo().getAttrs().get("流程组目的"));
                        break;
                    case 1:
                        row.getCell(0).setText("上一层级");
                        row.getCell(1).setText(vcCiRltInfo.getTargetCiInfo().getAttrs().get("上一层级"));
                        row.getCell(2).setText("下一层级");
                        row.getCell(3).setText(nextFlow);
                        break;
                    case 2:
                        row.getCell(0).setText("所有者");
                        row.getCell(1).setText(StringUtils.isEmpty(vcCiRltInfo.getTargetCiInfo().getAttrs().get("所有者")) ? "" : JSON.parseArray(vcCiRltInfo.getTargetCiInfo().getAttrs().get("所有者")).getJSONObject(0).getString("userName"));
                        row.getCell(2).setText("归口部门");
                        row.getCell(3).setText(vcCiRltInfo.getTargetCiInfo().getAttrs().get("归口部门"));
                        break;
                    case 3:
                        row.getCell(0).setText("输入");
                        row.getCell(1).setText(bdInput.stream()
                                .map(flow -> flow.getAttrs().get("表单名称") + "")
                                .collect(Collectors.joining(",")));
                        break;
                    case 4:
                        row.getCell(0).setText("输出");
                        row.getCell(1).setText(bdOutput.stream()
                                .map(flow -> flow.getAttrs().get("表单名称") + "")
                                .collect(Collectors.joining(",")));
                        break;
                }
            }
            /**-------集成关系图--------**/
            XWPFParagraph imgParagraph = doc.createParagraph();
            XWPFRun imgRun = imgParagraph.createRun();
            if (esDiagram != null && SysUtil.StringUtil.isNotBack(esDiagram.getIcon1())) {
                // 添加文本
                imgRun.setText(flowCode + " " + flowName + "集成关系图");
                imgRun.setFontSize(12);
                imgParagraph.setAlignment(ParagraphAlignment.CENTER);
                // 添加换行
                imgRun.addBreak();
//            	String imgUrl =  httpResouceUrl + esDiagram.getIcon1();
                File destFolder = new File(localPath + "/" + esDiagram.getIcon1());
                String base64String = Base64Utils.fileToBase64Str(destFolder);
                // 解码Base64字符串为字节数组
                byte[] decodedBytes = Base64.getDecoder().decode(base64String);
                // 将字节数组转换为InputStream
                InputStream is = new ByteArrayInputStream(decodedBytes);
                // 使用图片ID创建并添加图片到运行中
                BufferedImage image = ImageIO.read(is);
                // 获取图片的原始宽高
                int originalWidth = image.getWidth();
                int originalHeight = image.getHeight();
                // 计算缩放后的宽高，保持比例
                double scale = Math.min((double) 430 / originalWidth, (double) 400 / originalHeight);
                int scaledWidth = (int) (originalWidth * scale);
                int scaledHeight = (int) (originalHeight * scale);

                is.reset();
                String pictureIdx = doc.addPictureData(is, XWPFDocument.PICTURE_TYPE_PNG);
                is.close(); // 关闭输入流
                imgRun.addPicture(new ByteArrayInputStream(doc.getPictureDataByID(pictureIdx).getData()),
                        doc.getPictureDataByID(pictureIdx).getPictureType(), pictureIdx + "image.png", // 图片的文件名（在Word文档中显示）
                        Units.toEMU(scaledWidth), // 图片宽度，单位EMU
                        Units.toEMU(scaledHeight)); // 图片高度，单位EMU
            }
            imgRun.addBreak();
        }
    }

    private String getWord(XWPFDocument document, String name) {
        OutputStream out = null;
        try {
            Long dateTimeFolder = ESUtil.getNumberDate();
            File destFolder = new File(localPath + "/" + dateTimeFolder);
            if (!destFolder.exists()) {
                destFolder.mkdirs();
            }
            String destFileName = name + "_业务域手册.docx";
            File destFile = new File(destFolder, destFileName);
            out = new FileOutputStream(new File(destFile.getCanonicalPath()));
            document.write(out);
            rsmUtils.uploadRsmFromFile(destFile);
            return httpPath + "/" + dateTimeFolder + "/" + destFileName;
        } catch (Exception e) {
            log.info("导出数据失败!" + e);
            throw new BusinessException("导出数据失败!");
        } finally {
            try {
                if (document != null) {
                    document.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                throw new BusinessException("关闭流错误!");
            }
        }
    }

    /**
     * 文本数据导出组装
     *
     * @param ciInfo
     * @return
     */
    private Map<String, Object> getTextData(CcCiInfo ciInfo, String imgStr) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = currentDate.format(formatter);
        List<FlowProcessSystemPublishHistoryDto> operationList = findOperationList(ciInfo.getCi().getCiCode(), "submit");
        Map<String, Object> data = new HashMap<>();
        Map<String, String> attrs = ciInfo.getAttrs();
        data.put("${flowCode}", attrs.get("流程组编码"));
        if (!CollectionUtils.isEmpty(operationList)) {
            data.put("${version}", operationList.get(0).getVersionName());
        } else {
            data.put("${version}", "SV1");
        }
        data.put("${level}", attrs.get("密级等级"));
        data.put("${flowName}", attrs.get("流程组名称"));
        data.put("${rangeText}", attrs.get("范围") == null ? "" : attrs.get("范围"));
        data.put("${responsible}", BinaryUtils.isEmpty(attrs.get("责任人")) ? "空" : JSON.parseArray(attrs.get("责任人")).getJSONObject(0).getString("userName"));
        data.put("${ownerName}", BinaryUtils.isEmpty(attrs.get("所有者")) ? "空" : JSON.parseArray(attrs.get("所有者")).getJSONObject(0).getString("userName"));
        data.put("${time}", formattedDate);
        data.put("${jcgxName}", StringUtils.isEmpty(imgStr) ? "" : attrs.get("流程组编码") + " " + attrs.get("流程组名称") + "集成关系图");

        return data;
    }

    /**
     * 获取流程的流程图信息以及关联要素信息
     *
     * @param subFlows
     * @return
     */
    private Map<String, Map<String, Object>> processInfo(List<VcCiRltInfo> subFlows) {
        Map<String, Map<String, Object>> ciNodeMap = new HashMap<>(subFlows.size());
        if (BinaryUtils.isEmpty(subFlows)) {
            return ciNodeMap;
        }
        List<String> subFlowCiCodes = subFlows.stream().map(s -> s.getTargetCiInfo().getCi().getCiCode()).collect(Collectors.toList());

        // 查询目标末级流程和流程图的对应关系
        List<EamDiagramRelationSys> processDiagram = eamDiagramRelationSysService.findDiagramRelationSysList(subFlowCiCodes, "flowDiagram");
        if (!BinaryUtils.isEmpty(processDiagram)) {
            // 获取所有流程图的节点信息
            Map<String, String> rltMap = processDiagram.stream().collect(Collectors.toMap(EamDiagramRelationSys::getDiagramEnergy, EamDiagramRelationSys::getEsSysId));
            BoolQueryBuilder nodeBool = QueryBuilders.boolQuery();
            nodeBool.must(QueryBuilders.termsQuery("dEnergy.keyword", new ArrayList<>(rltMap.keySet())));
            List<ESDiagramNode> nodeList = esDiagramNodeDao.getListByQuery(nodeBool);
            // 处理节点数据
            Map<String, List<String>> activityMap = new HashMap<>();
            List<String> nodeClassName = Arrays.asList("活动");
            for (ESDiagramNode esDiagramNode : nodeList) {
                if (rltMap.containsKey(esDiagramNode.getdEnergy())) {
                    JSONObject obj = JSONObject.parseObject(esDiagramNode.getNodeJson());
                    String className = obj.getString("className");
                    // 只选取目标类型节点
                    if (nodeClassName.contains(className)) {
                        String ciCode = rltMap.get(esDiagramNode.getdEnergy());
                        Map<String, Object> nodeMap = ciNodeMap.getOrDefault(ciCode, new HashMap<>(3));
                        List<String> result = StrUtil.split(nodeMap.get(className) == null ? null : nodeMap.get(className).toString(), ",");
                        // 活动节点
                        result.add(obj.getString("ciCode"));
                        activityMap.put(ciCode, result);
                        nodeMap.put(className, StrUtil.join(",", result));
                        ciNodeMap.put(ciCode, nodeMap);
                    }
                }
            }
            // 查询活动关联属性
            List<String> ciCodes = new ArrayList<>();
            for (List<String> value : activityMap.values()) {
                ciCodes.addAll(value);
            }
            List<ESCIInfo> activityCiInfos = ciSwitchSvc.getCiByCodes(ciCodes, null, LibType.DESIGN);
            String activityOutputForm = "相关输出表单";
            String activityInputForm = "相关输入表单";

            String activityRegime = "相关制度";
            String activityStandard = "相关标准";
            String activityPostRole = "相关岗位角色";
            for (Map.Entry<String, List<String>> entry : activityMap.entrySet()) {
                String ciCode = entry.getKey();
                Map<String, Object> nodeMap = ciNodeMap.get(ciCode);
//                List<String> form = StrUtil.split(nodeMap.get(activityForm) == null?null:nodeMap.get(activityForm).toString(),",");
                List<String> outputForm = StrUtil.split(nodeMap.get(activityOutputForm) == null ? null : nodeMap.get(activityOutputForm).toString(), ",");
                List<String> inputForm = StrUtil.split(nodeMap.get(activityInputForm) == null ? null : nodeMap.get(activityInputForm).toString(), ",");


                List<String> regime = StrUtil.split(nodeMap.get(activityRegime) == null ? null : nodeMap.get(activityRegime).toString(), ",");
                List<String> standard = StrUtil.split(nodeMap.get(activityStandard) == null ? null : nodeMap.get(activityStandard).toString(), ",");
                List<String> postRole = StrUtil.split(nodeMap.get(activityPostRole) == null ? null : nodeMap.get(activityPostRole).toString(), ",");

                for (ESCIInfo activityCi : activityCiInfos) {
                    if (entry.getValue().contains(activityCi.getCiCode())) {
                        inputForm.addAll(getTargetResult(String.valueOf(activityCi.getAttrs().get("输入表单")), "ciCode"));
                        outputForm.addAll(getTargetResult(String.valueOf(activityCi.getAttrs().get("输出表单")), "ciCode"));
                        regime.addAll(getTargetResult(String.valueOf(activityCi.getAttrs().get("关联制度")), "ciCode"));
                        standard.addAll(getTargetResult(String.valueOf(activityCi.getAttrs().get("关联标准")), "ciCode"));
                        postRole.addAll(getTargetResult(String.valueOf(activityCi.getAttrs().get("责任角色")), "ciCode"));
                    }
                }
                nodeMap.put(activityOutputForm, outputForm.stream().collect(Collectors.joining(",")));
                nodeMap.put(activityInputForm, inputForm.stream().collect(Collectors.joining(",")));

                nodeMap.put(activityRegime, regime.stream().collect(Collectors.joining(",")));
                nodeMap.put(activityStandard, standard.stream().collect(Collectors.joining(",")));
                nodeMap.put(activityPostRole, postRole.stream().collect(Collectors.joining(",")));

                ciNodeMap.put(ciCode, nodeMap);
            }
        }

        // 获取相关制度
        processingData(subFlows, ciNodeMap, "制度");
        // 获取相关输入表单
        processingData(subFlows, ciNodeMap, "输入表单");
        // 获取相关输出表单
        processingData(subFlows, ciNodeMap, "输出表单");
        // 获取相关标准
        processingData(subFlows, ciNodeMap, "标准");
        // 获取相关岗位角色
        processingData(subFlows, ciNodeMap, "岗位角色");
        return ciNodeMap;
    }

    /**
     * 获取指定字段
     *
     * @param textData
     * @param attr
     * @return
     */
    private List<String> getTargetResult(String textData, String attr) {
        List<String> result = new ArrayList<>();
        JSONArray obj = JSONObject.parseArray(textData);
        if (!BinaryUtils.isEmpty(obj)) {
            for (int i = 0; i < obj.size(); i++) {
                JSONObject Jobj = obj.getJSONObject(i);
                result.add(Jobj.getString(attr));
            }
        }
        return result;
    }

    /**
     * 获取目标ci信息
     *
     * @param subFlows
     * @param ciNodeMap
     * @param attr
     */
    private void processingData(List<VcCiRltInfo> subFlows, Map<String, Map<String, Object>> ciNodeMap, String attr) {
        Map<String, List<String>> map = new HashMap<>();
        List<String> ciCodes = new ArrayList<>();
        // 合并流程和相关活动的关联数据ciCode
        for (VcCiRltInfo subFlow : subFlows) {
            List<String> data = new ArrayList<>();
            String ciCode = subFlow.getTargetCiInfo().getCi().getCiCode();
            data.addAll(getTargetResult(subFlow.getTargetCiInfo().getAttrs().get("关联" + attr), "ciCode"));
            data.addAll(getTargetResult(subFlow.getTargetCiInfo().getAttrs().get("输入" + attr), "ciCode"));
            data.addAll(getTargetResult(subFlow.getTargetCiInfo().getAttrs().get("输出" + attr), "ciCode"));
            if (ciNodeMap.containsKey(ciCode)) {
                data.addAll(StrUtil.split(ciNodeMap.get(ciCode).get("相关" + attr) == null ? null : ciNodeMap.get(ciCode).get("相关" + attr).toString(), ","));
            }
            ciCodes.addAll(data);
            map.put(ciCode, data);
        }
        // 解析获取目标ci的名称信息
        if (!BinaryUtils.isEmpty(ciCodes)) {
            List<ESCIInfo> esCiInfo = ciSwitchSvc.getCiByCodes(ciCodes, null, LibType.DESIGN);
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                List<ESCIInfo> objs = new ArrayList<>();
                Map<String, Object> nodeMap = ciNodeMap.getOrDefault(entry.getKey(), new HashMap<>());
                for (ESCIInfo e : esCiInfo) {
                    if (entry.getValue().contains(e.getCiCode())) {
                        objs.add(e);
                    }
                }
                nodeMap.put("相关" + attr, objs);
                ciNodeMap.put(entry.getKey(), nodeMap);
            }
        }
    }


    /**
     * 获取审批状态描述
     */
    private String getApprovalStatus(Integer status) {
        if (status == null)
            return "未知";
        switch (status) {
            case 0:
                return "待审批";
            case 1:
                return "已审批";
            case 2:
                return "已驳回";
            default:
                return "未知";
        }
    }
}