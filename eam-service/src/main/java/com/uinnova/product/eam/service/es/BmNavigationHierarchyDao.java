package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamHierarchyNavigation;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 导航层级配置
 */
@Service
public class BmNavigationHierarchyDao extends AbstractESBaseDao<EamHierarchyNavigation, EamHierarchyNavigation> {

    @Override
    public String getIndex() {
        return "uino_eam_hierarchy_navigation";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
