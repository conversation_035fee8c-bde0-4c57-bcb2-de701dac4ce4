package com.uinnova.product.eam.service.diagram;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;


import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ESShareDiagramSvc {

    /**
     * 保存或更新分享记录
     * @param shareRecordList
     * @return
     */
    List<ESDiagramShareRecordResult> saveOrUpdateShareRecord(List<DiagramShareRecordDTO> shareRecordList);

    /**
     * 分页查询分享记录
     * @param shareRecordQueryBean
     * @return
     */
    Page<ESDiagramShareRecordResult> queryShareRecordPage(ShareRecordQueryBean shareRecordQueryBean);

    /**
     * 查询视图分享记录 map
     * @param diagramIds
     * @return
     */
    Map<Long, List<ESDiagramShareRecordResult>> queryDiagramShareRecords(Long[] diagramIds, Boolean needAuth);

    /**
     * 删除视图分享记录
     * @param id
     * @return
     */
    Integer removeShareRecord(Long id);

    /**
     * 查询当前视图分享的用户id
     * @param diagramIds
     * @return
     */
    Map<Long, Set<Long>> queryDiagramSharedUserIds(Long[] diagramIds);

    /**
     * <AUTHOR>
     * @Description 新增分享图片
     * @Date 17:21 2021/9/8
     * @Param [shareBean]
     **/
    DiagramShareLink addSharePic(DiagramShareLink shareBean);

    /**
     * <AUTHOR>
     * @Description 查询指定视图的分享图片列表
     * @Date 17:21 2021/9/8
     * @Param [diagramId]
     **/
    List<DiagramShareLink> querySharePic(String diagramId, Integer[] types);

    /**
     * <AUTHOR>
     * @Description 删除分享图片
     * @Date 17:21 2021/9/8
     * @Param [id]
     **/
    Integer deleteSharePic(Long id);

    /**
     * <AUTHOR>
     * @Description 新增分享链接
     * @Date 17:21 2021/9/8
     * @Param [shareBean]
     **/
    DiagramShareLink addShareLink(DiagramShareLink shareBean);

    /**
     * <AUTHOR>
     * @Description 删除分享链接
     * @Date 17:21 2021/9/8
     * @Param [id]
     **/
    Integer deleteShareLink(Long id);

    ESDiagramInfoDTO getDiagramByLinkKey(String linkKey);

    /**
     * <AUTHOR>
     * @Description 根据链接为用户赋权
     * @Date 18:30 2021/9/16
     * @Param [id]
     * @Return TODO
     **/
    String addUserByLink(String id);

    Long updateShareLink(Long id, Integer permission);

    /**
     * 批量删除分享记录 根据视图ID
     * @param removeIds
     */
    void removeShareByDiagramIds(List<Long> removeIds);

    /**
     * 查询视图分享记录
     * @param diagramId 视图加密id
     * @return 分享记录
     */
    List<DiagramShareRecord> getByDiagramId(String diagramId);

    /**
     * 根据视图id及用户查询视图分享记录
     * @param diagramId 视图id
     * @param sharedUserId 分享用户id
     * @return 分享记录
     */
    DiagramShareRecord queryShare(Long diagramId, Long sharedUserId);
}
