package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.vo.EamMatrixAnalysisVo;
import com.uinnova.product.eam.model.vo.EamMatrixTableVo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * 专题分析相关接口
 * <AUTHOR>
 */
public interface ThematicAnalysisSvc {
    /**
     * 获取矩阵分析数据
     * @param id 专题分析配置id
     * @return 矩阵表格数据
     */
    EamMatrixAnalysisVo getMatrixById(Long id);

    /**
     * 获取矩阵表格数据
     * @param id 专题分析配置id
     * @return 矩阵表格数据
     */
    List<EamMatrixTableVo> getMatrixTable(Long id);

    /**
     * 矩阵表格导出
     * @param id 专题分析配置id
     * @return excel
     */
    ResponseEntity<byte[]> export(Long id);

    /**
     * 应用系统关系图谱-接口服务
     * @param rltIds 关系id
     * @return 服务对象信息
     */
    Map<Long, List<CcCiInfo>> serviceAnalysis(List<Long> rltIds);

    /**
     * 应用系统关系图谱导出
     * @param rltIds 关系id
     * @return excel
     */
    ResponseEntity<byte[]> rltGraphExport(List<Long> rltIds);

    /**
     * 应用系统关系图谱接口服务表格展示
     * @param rltIds 关系id
     * @param like 模糊匹配
     * @return 服务对象信息
     */
    List<EamMatrixTableVo> getRltInterface(List<Long> rltIds, String like);
}
