package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 我的空间目录&文件夹Dao
 * <AUTHOR>
 */
@Service
public class EamCategoryPrivateDao extends AbstractESBaseDao<EamCategory, EamCategory> {

    @Override
    public String getIndex() {
        return "uino_eam_category_private";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
