package com.uinnova.product.eam.service.impl;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.FlowSnapTreeDto;
import com.uinnova.product.eam.comm.model.es.FlowSystemApproveData;
import com.uinnova.product.eam.comm.model.es.FlowSystemAssociatedFeatures;
import com.uinnova.product.eam.comm.model.es.IndicatorDetectionInformationAssociation;
import com.uinnova.product.eam.feign.workable.entity.TaskResponse;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.dto.FlowSystemAssociatedFeaturesDto;
import com.uinnova.product.eam.model.dto.FlowSystemFileDto;
import com.uinnova.product.eam.model.vo.FileDocVo;
import com.uinnova.product.eam.model.vo.KcpInfoVo;
import com.uinnova.product.eam.service.FlowProcessSystemService;
import com.uinnova.product.eam.service.flow.approval.FlowProcessApprovalServiceImpl;
import com.uinnova.product.eam.service.flow.basic.FlowProcessBasicServiceImpl;
import com.uinnova.product.eam.service.flow.document.FlowProcessDocumentServiceImpl;
import com.uinnova.product.eam.service.flow.monitor.FlowProcessMonitorServiceImpl;
import com.uinnova.product.eam.service.flow.relation.FlowProcessRelationServiceImpl;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;

/**
 * 流程管理/流程体系服务层实现
 *
 * <AUTHOR>
 * @since 2024/5/24 11:29
 */
@Slf4j
@Service
public class FlowProcessSystemServiceImpl implements FlowProcessSystemService {

    @Resource
    private FlowProcessApprovalServiceImpl flowProcessApprovalService;

    @Resource
    private FlowProcessBasicServiceImpl flowProcessBasicService;

    @Resource
    private FlowProcessDocumentServiceImpl flowProcessDocumentService;

    @Resource
    private FlowProcessMonitorServiceImpl flowProcessMonitorService;

    @Resource
    private FlowProcessRelationServiceImpl flowProcessRelationService;


    @Override
    public Long createFlowSystem(FlowProcessSystemDto flowProcessSystemDto) {
        return flowProcessBasicService.createFlowSystem(flowProcessSystemDto);
    }


    @Override
    public Map<String, Object> getFlowSystemAssociatedFeatures(String ciCode, String classCode, LibType libType) {
        return flowProcessRelationService.getFlowSystemAssociatedFeatures(ciCode, classCode, libType);
    }

    @Override
    public void deleteFlowSystemAssociatedFeatures(Long associatedFeatureId) {
        flowProcessRelationService.deleteFlowSystemAssociatedFeatures(associatedFeatureId);
    }

    @Override
    public FlowSystemAssociatedFeaturesDto addFlowSystemAssociatedFeatures(FlowSystemAssociatedFeatures flowProcessSystem) {
        return flowProcessRelationService.addFlowSystemAssociatedFeatures(flowProcessSystem);
    }

    @Override
    public void batchAddFlowSystemAssociatedFeatures(FlowSystemAssociatedFeaturesDto flowProcessSystemDto) {
        flowProcessRelationService.batchAddFlowSystemAssociatedFeatures(flowProcessSystemDto);
    }

    @Override
    public Long saveOrUpdateFlowSystemFile(FlowSystemFileDto flowSystemFileDto) {
        return flowProcessDocumentService.saveOrUpdateFlowSystemFile(flowSystemFileDto);
    }

    @Override
    public FlowSystemFileDto getFlowSystemFile(String ciCode, LibType libType) {
        return flowProcessDocumentService.getFlowSystemFile(ciCode, libType);
    }

    @Override
    public HashMap<String, Object> checkFlowProcessSystem(String ciCode) {

        return flowProcessBasicService.checkFlowProcessSystem(ciCode);
    }

    @Override
    public Long publishFlowProcessSystem(String ciCode, String loginCode, String publishReason
            , String publishType, Long flowSystemApproveDataId) {
        return flowProcessApprovalService.publishFlowProcessSystem(ciCode, loginCode, publishReason, publishType, flowSystemApproveDataId);
    }

    @Override
    public Map<String, Object> getRedirectDiagramId(String diagramId, String diagramClassType, Boolean alwaysCheck) {
        return flowProcessBasicService.getRedirectDiagramId(diagramId, diagramClassType, alwaysCheck);
    }

    @Override
    public List<FlowProcessSystemPublishHistoryDto> getFlowSystemPublishHistory(String ciCode, String publishType) {
        return flowProcessApprovalService.getFlowSystemPublishHistory(ciCode, publishType);
    }

    @Override
    public List<KcpInfoVo> findKcpInfoList(String ciCode, Integer sign, LibType libType) {
        return flowProcessApprovalService.findKcpInfoList(ciCode, sign, libType);
    }

    @Override
    public List<FileDocVo> findFileDocList(String ciCode, String publishType) {
        return flowProcessApprovalService.findFileDocList(ciCode, publishType);
    }

    @Override
    public List<FlowProcessSystemPublishHistoryDto> findOperationList(String ciCode, String publishType) {
        return flowProcessApprovalService.findOperationList(ciCode, publishType);
    }

    public Long processFileAddData(Long id) {
        return flowProcessDocumentService.processFileAddData(id);
    }

    @Override
    public CcCiInfo createProcessPerformance(ProcessPerformanceDto processPerformanceDto, String classCode) {
        return flowProcessRelationService.createProcessPerformance(processPerformanceDto, classCode);
    }

    @Override
    public HashMap<String, Object> getAutoDiagramByParentCiCode(Long parentCiId) {
        return flowProcessApprovalService.getAutoDiagramByParentCiCode(parentCiId);
    }

    @Override
    public void delFlowProcessSystem(String ciCode) {
        flowProcessBasicService.delFlowProcessSystem(ciCode);
    }

    @Override
    public Map<String, Object> checkFlowDiagramVersion(String ciCode, String diagramEnergy, String diagramClassType) {
        return flowProcessBasicService.checkFlowDiagramVersion(ciCode, diagramEnergy, diagramClassType);
    }

    @Override
    public List<Map<String, Object>> getEndProcessTable(String ciCode, LibType libType) {
        return flowProcessApprovalService.getEndProcessTable(ciCode, libType);
    }


    @Override
    public void batchSaveOrUpdateFlowDiagramNode(List<FlowProcessTableDto> flowProcessTableDtoList) {
        flowProcessBasicService.batchSaveOrUpdateFlowDiagramNode(flowProcessTableDtoList);
    }

    @Override
    public Map<String, Object> getLevelFormsList(LevelFormsDto dto) {
        return flowProcessBasicService.getLevelFormsList(dto);
    }

    @Override
    public Map<String, Object> mergePullFlowDiagram(String ciCode, String diagramClassType) {
        return flowProcessBasicService.mergePullFlowDiagram(ciCode, diagramClassType);
    }

    @Override
    public Map<String, Object> checkFlowCiVersion(String ciCode) {
        return flowProcessBasicService.checkFlowCiVersion(ciCode);
    }

    @Override
    public String mergePullFlowCi(String ciCode, Boolean allUser) {
        return flowProcessBasicService.mergePullFlowCi(ciCode, allUser);
    }


    @Override
    public Collection<FlowProcessSystemTreeDto> getFlowSystemTreeNew(Boolean needCiInfo, Boolean needFlow) {
        return flowProcessBasicService.getFlowSystemTreeNew(needCiInfo, needFlow);
    }

    @Override
    public FlowSnapTreeDto getSingleTreeByProcessCiId(Long ciId) {
        return flowProcessBasicService.getSingleTreeByProcessCiId(ciId);
    }

    public void moveFlowProcess(FlowProcessSystemDto flowProcessSystemDto) {
        flowProcessBasicService.moveFlowProcess(flowProcessSystemDto);
    }

    @Override
    public void renameFlowProcess(FlowReNameDto flowReNameDto) {
        flowProcessBasicService.renameFlowProcess(flowReNameDto);
    }

    @Override
    public Set<FlowProcessSystemTreeDto> getMoveFlowSystem(String ciCode) {
        return flowProcessBasicService.getMoveFlowSystem(ciCode);
    }

    @Override
    public Collection<FlowProcessSystemTreeDto> getMoveFlowSystemNew(String ciCode) {
        return flowProcessBasicService.getMoveFlowSystemNew(ciCode);
    }

    @Override
    public List<Map<String, Object>> getFlowRoleTree(String roleName) {
        return flowProcessBasicService.getFlowRoleTree(roleName);
    }

    @Override
    public List<String> batchSaveSceneActiveRlt(SceneActiveRltDto sceneActiveRltDto) {
        return flowProcessRelationService.batchSaveSceneActiveRlt(sceneActiveRltDto);
    }

    @Override
    public Map<String, Object> getSceneActiveRltRltByFlowCiCodeWithLibType(String ciCode, LibType libType) {
        List<Map<String, Object>> sceneActiveRltByFlowCiCode = getSceneActiveRltByFlowCiCode(ciCode, libType);
        List<ESCIInfo> flowDiagramLocalActiveList = getFlowDiagramLocalActiveList(ciCode, libType);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("sceneActiveRlt", sceneActiveRltByFlowCiCode);
        resultMap.put("flowDiagramActiveList", flowDiagramLocalActiveList);
        return resultMap;
    }

    @Override
    public List<Map<String, Object>> getSceneActiveRltByFlowCiCode(String ciCode, LibType libType) {
        return flowProcessApprovalService.getSceneActiveRltByFlowCiCode(ciCode, libType);
    }

    @Override
    public List<ESCIInfo> getFlowDiagramLocalActiveList(String ciCode, LibType libType) {
        return flowProcessApprovalService.getFlowDiagramLocalActiveList(ciCode, libType);
    }

    @Override
    public Map<String, List<CcCiInfo>> getFlowUserRoleAndPosition() {
        return flowProcessBasicService.getFlowUserRoleAndPosition();
    }


    @Override
    public Map<String, Collection<CcCiInfo>> getFlowAssertCount() {
        return flowProcessBasicService.getFlowAssertCount();
    }

    @Override
    public FlowWholeSceneCountVo getFlowWholeSceneCountInfo() {
        return flowProcessBasicService.getFlowWholeSceneCountInfo();
    }

    /**
     * 获取所有无流程图的末级流程
     *
     * @return
     */
    @Override
    public Collection<FlowProcessSystemTreeDto> getAllFlowWhereNoDiagram() {
        return flowProcessBasicService.getAllFlowWhereNoDiagram();
    }

    /**
     * 流程图复用
     *
     * @param flowDiagramReuseDto
     * @return
     */
    @Override
    public Map<String, String> copyDiagramByFlowChart(FlowDiagramReuseDto flowDiagramReuseDto) {
        return flowProcessBasicService.copyDiagramByFlowChart(flowDiagramReuseDto);
    }

    /**
     * 获取所有顶级流程组
     *
     * @return
     */
    @Override
    public Collection<FlowProcessSystemTreeDto> getTopTierProcesses() {
        return flowProcessBasicService.getTopTierProcesses();
    }

    /**
     * 导出流程地图
     *
     * @param targetCiCode 目标流程组
     * @return
     */
    @Override
    public File exportFlowSystemExcel(List<String> targetCiCode) {
        return flowProcessBasicService.exportFlowSystemExcel(targetCiCode);
    }

    @Override
    public TaskResponse flowSingApproval(FlowSignProcessVo flowSignProcessVo) {
        return flowProcessApprovalService.flowSingApproval(flowSignProcessVo);
    }

    @Override
    public List<FlowSystemProcessSingDataVo> getFlowSignCiList(String businessKey) {
        return flowProcessApprovalService.getFlowSignCiList(businessKey);
    }

    @Override
    public FlowSystemApproveData getFlowSystemApproveDataByProcessInstanceIdId(String processInstanceId) {
        return flowProcessApprovalService.getFlowSystemApproveDataByProcessInstanceIdId(processInstanceId);
    }

    @Override
    public FlowSystemApproveData getFlowSystemApproveDataById(Long flowSystemApproveId) {
        return flowProcessApprovalService.getFlowSystemApproveDataById(flowSystemApproveId);
    }

    @Override
    public void changeFlowStatus(ProcessApprovalChangeDto processApprovalChangeDto) {
        flowProcessApprovalService.changeFlowStatus(processApprovalChangeDto);
    }

    @Override
    public CcCiInfo getApproveCiInfoByProcessesInstanceId(String processInstanceId) {
        return flowProcessApprovalService.getApproveCiInfoByProcessesInstanceId(processInstanceId);
    }

    @Override
    public TaskResponse reSubmitRejectApproveFlow(String businessKey) {
        return flowProcessApprovalService.reSubmitRejectApproveFlow(businessKey);
    }

    @Override
    public void abolishFlow(String ciCode, Boolean upVersion) {
        flowProcessApprovalService.abolishFlow(ciCode, upVersion);
    }

    @Override
    public Boolean stopFlowSystemApprove(String processInstanceId, String deleteReason) {
        return flowProcessApprovalService.stopFlowSystemApprove(processInstanceId, deleteReason);
    }

    @Override
    public void withdrawFlowSystemApprove(String businessKey) {
        flowProcessApprovalService.withdrawFlowSystemApprove(businessKey);
    }

    @Override
    public Boolean carryOutFlow(SignFlowActionVo signFlowActionVo) {
        return flowProcessApprovalService.carryOutFlow(signFlowActionVo);
    }

    /**
     * 末级流程发布
     *
     * @param processApprovalDto
     */
    @Override
    public TaskResponse processLaunch(ProcessLaunchDto processApprovalDto) {
        return flowProcessApprovalService.processLaunch(processApprovalDto);
    }

    @Override
    public void processApproval(ProcessApprovalDto processApprovalDto) {
        flowProcessApprovalService.processApproval(processApprovalDto);
    }

    @Override
    public Page<CcCiInfo> findSingFlowList(Long ciId, Integer pageNum, Integer pageSize, String word) {
        return flowProcessApprovalService.findSingFlowList(ciId, pageNum, pageSize, word);
    }

    @Override
    public Map<String, Map> getFlowSystemAssociatedFeaturesNew(String diagramIds, String ciCode, LibType libType) {
        return flowProcessRelationService.getFlowSystemAssociatedFeaturesNew(diagramIds, ciCode, libType);
    }

    @Override
    public Map obtainTheProcessFile(String ciCode, LibType libType) {
        return flowProcessRelationService.obtainTheProcessFile(ciCode, libType);
    }

    @SneakyThrows
    @Override
    public Map<String, Map> getFlowFileNew(String diagramIds, String ciCode, LibType libType) {
        return flowProcessDocumentService.getFlowFileNew(diagramIds, ciCode, libType);
    }

    @Override
    public String exportProcessFile(HttpServletResponse response, String ciCode, String diagramIds, String id, LibType libType) {
        return flowProcessDocumentService.exportProcessFile(response, ciCode, diagramIds, id, libType);
    }

    @Override
    public Set<String> whetherDataIsChanged(String ciCode, String classCode) {
        return flowProcessRelationService.whetherDataIsChanged(ciCode, classCode);
    }

    @Override
    public Long mergePullAssociationCi(String ciCode, String classCode) {
        return flowProcessRelationService.mergePullAssociationCi(ciCode, classCode);
    }

    @Override
    public Long addMetricMonitoringInformation(List<IndicatorDetectionInformationAssociation> dto) {
        return flowProcessMonitorService.addMetricMonitoringInformation(dto);
    }

    @Override
    public Map<String, Object> queryIndicatorMonitoringInformation(String ciCode) {
        return flowProcessMonitorService.queryIndicatorMonitoringInformation(ciCode);
    }

    @Override
    public String exportFlowManual(HttpServletResponse response, String ciCode, String base64File) {
        return flowProcessDocumentService.exportFlowManual(response, ciCode, base64File);
    }

    @Override
    public List<FlowProcessSystemTreeDto> getAllFlowWhereRunSituation(String sourceId) {
        return flowProcessMonitorService.getAllFlowWhereRunSituation(sourceId);
    }

    @Override
    public Map<String, Integer> queryProcessQuantity() {
        return flowProcessMonitorService.queryProcessQuantity();
    }

}
