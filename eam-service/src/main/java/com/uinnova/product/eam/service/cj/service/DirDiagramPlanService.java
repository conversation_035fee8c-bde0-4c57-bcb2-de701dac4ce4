package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.vo.DirVo;
import com.uinnova.product.eam.model.cj.vo.ESDiagramDirPlanVO;
import com.uinnova.product.eam.model.cj.vo.PublishedDiagramPlanVO;
import com.uinnova.product.eam.model.vo.MineAssetsReq;
import com.uinnova.product.eam.model.vo.MineAssetsVo;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uino.bean.permission.base.SysUser;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * @description: 文件夹 + 制品 + 方案接口
 * @author: Lc
 * @create: 2022-02-23 17:16
 */
public interface DirDiagramPlanService {

    Boolean movePlane(Long planId, Long targetDirId);

    List<CcCi> getPlanAssertName(Long planId);

    @Deprecated
    ESDiagramDirPlanVO getBuildAssert(MineAssetsVo mineAssetsVo, String like);

    /**
     * 我的发布列表
     * @param user
     * @return
     */
    List<PublishedDiagramPlanVO> findMyPublishList(SysUser user);

    String getDirName(DirVo dirVo);

    /**
     * 获取主办系统
     * @param planId
     * @return
     */
    String getHostSystemByPlanId(Long planId);

    ESDiagramDirPlanVO getAllRecentlyDiagramAndPlan() throws ParseException;

    ESDiagramDirPlanVO getAllMineAttention() throws ParseException;

    ESDiagramDirPlanVO getDesignDiagramAndPlan() throws ParseException;

    ESDiagramDirPlanVO getBuildAssertNew(MineAssetsReq req);

    Map<String, Long> getCount() throws ParseException;
}
