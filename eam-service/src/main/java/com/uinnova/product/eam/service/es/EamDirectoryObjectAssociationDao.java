package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamDirectoryObjectAssociation;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 文件目录与对象关联关系表[uino_eam_directory_Object_association_decision]
 */
@Service
public class EamDirectoryObjectAssociationDao extends AbstractESBaseDao<EamDirectoryObjectAssociation, EamDirectoryObjectAssociation> {

    @Override
    public String getIndex() {
        return "uino_eam_directory_object_association_decision";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
