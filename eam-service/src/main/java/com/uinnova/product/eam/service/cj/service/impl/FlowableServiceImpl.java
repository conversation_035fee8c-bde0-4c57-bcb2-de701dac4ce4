package com.uinnova.product.eam.service.cj.service.impl;

import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.feign.workable.entity.PorcessResponse;
import com.uinnova.product.eam.feign.workable.entity.ProcessRequest;
import com.uinnova.product.eam.feign.workable.entity.TaskRequest;
import com.uinnova.product.eam.feign.workable.entity.TaskResponse;
import com.uinnova.product.eam.service.cj.service.FlowableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-07 10:15
 */
@Slf4j
@Service
public class FlowableServiceImpl implements FlowableService {

    @Value("${process.definition.key}")
    private String processDefinitionKey;

    @Resource
    private FlowableFeign flowableFeign;

    @Override
    public PorcessResponse startProcessBindAssignee(ProcessRequest request) {
        request.setProcessDefinitionKey(processDefinitionKey);
        return flowableFeign.startProcessBindAssignee(request);
    }

    @Override
    public TaskResponse completeTask(TaskRequest taskRequest) {
        // 过滤掉已经同意的用户
        taskRequest.setFilterUser(Boolean.TRUE);
        return flowableFeign.completeTask(taskRequest);
    }

    @Override
    public TaskResponse getCurrentTaskDefinitionInfo(String businessKey) {
        return flowableFeign.getCurrentTaskDefinitionInfo(businessKey, processDefinitionKey);
    }

    @Override
    public TaskResponse getCurrentTaskAssignees(String businessKey) {
        return flowableFeign.getCurrentTaskAssignees(businessKey, processDefinitionKey);
    }

}
