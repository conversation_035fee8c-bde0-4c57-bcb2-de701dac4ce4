package com.uinnova.product.eam.service.impl;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamDirectoryObjectAssociation;
import com.uinnova.product.eam.model.InstitutionSystemDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.model.enums.ProcessRootDirectory;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.model.vo.InstitutionSystemTreeNewDto;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.InstitutionSystemService;
import com.uinnova.product.eam.service.es.*;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.util.sys.SysUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/6/15 9:56
 */
@Service
public class InstitutionSystemServiceImpl implements InstitutionSystemService {

    @Resource
    private ICISwitchSvc iciSwitchSvc;

    @Resource
    private ICIClassSvc iciClassSvc;

    @Autowired
    @Lazy
    IamsESCmdbCommDesignSvc commSvc;

    @Resource
    private FlowSystemAssociatedFeaturesDao flowSystemAssociatedFeaturesDao;

    @Resource
    private ICISwitchSvc ciSwitchSvc;

    @Resource
    private EamCategoryDesignDao categoryDesignDao;

    @Resource
    private EamCategorySvc categorySvc;

    @Resource
    private EamDirectoryObjectAssociationDao eamDirectoryObjectAssociationDao;

    @Autowired
    private IamsESCIDesignSvc iamsESCIDesignSvc;

    @Resource
    private IRltClassSvc iRltClassSvc;

    @Resource
    private ICIRltSwitchSvc iciRltSwitchSvc;


    @Override
    public Long saveOrUpdateCategory(EamCategory vo) {
        // 检查未分组是否已存在
        if ("未分组".equals(vo.getDirName())) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("dirName.keyword", "未分组"));
            boolQueryBuilder.must(QueryBuilders.termQuery("parentId", vo.getParentId()));
            List<EamCategory> listByQuery = categoryDesignDao.getListByQuery(boolQueryBuilder);
            if (!listByQuery.isEmpty()) {
                throw new MessageException("已存在未分组，请勿重复创建");
            }
        }
        SysUser loginUser = SysUtil.getCurrentUserInfo();
        EamCategory category = null;

        // 如果是更新操作
        if (vo.getId() != null) {
            category = categoryDesignDao.getById(vo.getId());
        if (category == null) {
                throw new MessageException("分类不存在");
            }
            if ("未分组".equals(category.getDirName())) {
                throw new MessageException("未分组不允许修改");
            }
        } else {
            category = new EamCategory();
            category.setId(ESUtil.getUUID());
            category.setParentId(vo.getParentId());
            category.setDataStatus(1);
            category.setDomainId(loginUser.getDomainId());
            category.setType(vo.getType() != null ? vo.getType() : CategoryTypeEnum.UNIVERSAL.val());
            category.setOwnerCode(loginUser.getLoginCode());
            category.setCreator(loginUser.getLoginCode());
            category.setCreateTime(ESUtil.getNumberDateTime());
            if (ProcessRootDirectory.getAllHandleTypes().contains(vo.getParentId())) {
                category.setDirPath("#" + category.getId() + "#");
                category.setDirLvl(1);
            } else {
                EamCategory parent = categoryDesignDao.getById(vo.getParentId());
                if (parent==null){
                    throw new MessageException("父文件夹不存在!");
                }
                category.setDirPath(parent.getDirPath() + category.getId() + "#");
                category.setDirLvl(parent.getDirLvl() + 1);
            }
        }

        // 检查同名文件夹是否存在
           EamCategory existingCategory = categorySvc.getByName(vo.getDirName(), vo.getParentId(), loginUser.getLoginCode(), LibType.DESIGN);
           if (existingCategory != null && !existingCategory.getId().equals(category.getId())) {
               throw new MessageException("【" + vo.getDirName() + "】文件夹已存在!");
           }

        // 设置目录名称
        if (!BinaryUtils.isEmpty(vo.getDirName())) {
            category.setDirName(vo.getDirName());
        }

        // 设置修改者和修改时间
        category.setModifier(loginUser.getLoginCode());
        category.setModifyTime(ESUtil.getNumberDateTime());
        return categoryDesignDao.saveOrUpdate(category);
    }

    @Override
    public List<InstitutionSystemTreeNewDto> getInstitutionSystemTreeNew(Long id) {
        List<EamCategory> listByQuery = categoryDesignDao.getListByQuery(QueryBuilders.termsQuery("parentId", (Object) id));
        //取出所有name
        List<String> names = listByQuery.stream().map(EamCategory::getDirName).collect(Collectors.toList());
      if (!names.contains("未分组")){
          EamCategory eamCategory = new EamCategory();
          eamCategory.setParentId(id);
          eamCategory.setDirName("未分组");
          saveOrUpdateCategory(eamCategory);
      }
        List<InstitutionSystemTreeNewDto> treeDto = buildTree(categoryDesignDao.getListByQuery(QueryBuilders.termsQuery("parentId", (Object) id)));
        return sortTree(treeDto);
    }

    private List<InstitutionSystemTreeNewDto> buildTree(List<EamCategory> categories) {
        if (categories == null || categories.isEmpty()) {
            return Collections.emptyList();
        }

        List<InstitutionSystemTreeNewDto> treeDto = new ArrayList<>();
        for (EamCategory category : categories) {
            InstitutionSystemTreeNewDto treeDto1 = new InstitutionSystemTreeNewDto();
            treeDto1.setName(category.getDirName());
            treeDto1.setId(category.getId());
            treeDto1.setCreateTime(category.getCreateTime());
            // 查询子目录
            List<InstitutionSystemTreeNewDto> children = buildTree(categoryDesignDao.getListByQuery(QueryBuilders.termsQuery("parentId", (Object) category.getId())));
            // 计算当前节点的总数量（包括子节点）
            int totalCount = 0;

            // 如果有子节点，加上所有子节点的数量
            if (!children.isEmpty()) {
                treeDto1.setChildren(children);
                totalCount += children.stream()
                        .mapToInt(InstitutionSystemTreeNewDto::getCount)
                        .sum();
            }

            // 查询当前目录下的直接项目数量
            int currentCount = eamDirectoryObjectAssociationDao.getListByQuery(
                    QueryBuilders.termQuery("directoryId", category.getId())).size();
            totalCount += currentCount;
            treeDto1.setCount(totalCount);
            treeDto.add(treeDto1);
        }
        return treeDto;
    }

    private List<InstitutionSystemTreeNewDto> sortTree(List<InstitutionSystemTreeNewDto> treeDto) {
        if (treeDto == null || treeDto.isEmpty()) {
            return Collections.emptyList();
        }

        // 将“未分组”节点过滤出来
        List<InstitutionSystemTreeNewDto> ungroupedNode = treeDto.stream()
                .filter(node -> "未分组".equals(node.getName()))
                .collect(Collectors.toList());

        // 过滤出其他节点
        List<InstitutionSystemTreeNewDto> otherNodes = treeDto.stream()
                .filter(node -> !"未分组".equals(node.getName()))
                .collect(Collectors.toList());

        // 对其他节点按创建时间进行排序
        Collections.sort(otherNodes, Comparator.comparing(InstitutionSystemTreeNewDto::getCreateTime));

        // 合并列表
        List<InstitutionSystemTreeNewDto> sortedTree = new ArrayList<>(otherNodes);
        sortedTree.addAll(ungroupedNode);

        // 对子节点进行递归排序
        for (InstitutionSystemTreeNewDto node : sortedTree) {
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                node.setChildren(sortTree(node.getChildren()));
            }
        }

        return sortedTree;
    }


    public Page<CcCiInfo> getInstitutionListNew(InstitutionSystemDto institutionSystemDto) {
        CcCiClassInfo ciClassByClassCode = iciClassSvc.getCiClassByClassCode(institutionSystemDto.getName());
        List<String> ciCodes = new ArrayList<>();
        EamCategory byId = categoryDesignDao.getById(institutionSystemDto.getId());
        if (byId != null && byId.getDirName().equals("未分组")) {
            ESCISearchBeanVO bean = new ESCISearchBeanVO();
            bean.setClassCodes(Collections.singletonList(ciClassByClassCode.getCiClass().getClassCode()));
            List<ESCIInfo> result = iamsESCIDesignSvc.getListByQuery(QueryBuilders.termsQuery("classId", (Object) ciClassByClassCode.getCiClass().getId()));
            List<EamDirectoryObjectAssociation> listByQuery = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.matchAllQuery());
            List<String> existingCiCodes = listByQuery.stream().map(EamDirectoryObjectAssociation::getCiCode).collect(Collectors.toList());

            ciCodes = result.stream()
                    .filter(datum -> !existingCiCodes.contains(datum.getCiCode()))
                    .map(datum -> {
                         return datum.getCiCode();
                    })
                    .collect(Collectors.toList());
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("dataStatus", 1));
            query.must(QueryBuilders.wildcardQuery("dirPath.keyword", "*#" + institutionSystemDto.getId() + "#*"));

            List<EamCategory> categories = categoryDesignDao.getListByQuery(query);
            List<Long> categoryIds = categories.stream().map(EamCategory::getId).collect(Collectors.toList());

            List<EamDirectoryObjectAssociation> associations = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("directoryId", categoryIds)));
            ciCodes = associations.stream().map(EamDirectoryObjectAssociation::getCiCode).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(ciCodes)) {
            return null;
        }

        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setClassIds(Collections.singletonList(ciClassByClassCode.getCiClass().getId()));
        esciSearchBean.setCiCodes(ciCodes);
        if (StringUtils.isNotBlank(institutionSystemDto.getWord())) {
            esciSearchBean.setWords(Collections.singletonList(institutionSystemDto.getWord()));
        }
        if (StringUtils.isNotBlank(institutionSystemDto.getSortField())) {
            esciSearchBean.setSortField("attrs."+institutionSystemDto.getSortField());
        }else {
            String name = institutionSystemDto.getName();
            List<String> collect = ciClassByClassCode.getAttrDefs().stream().map(c -> c.getProName()).collect(Collectors.toList());
            Map<String, String> sortFieldMap = new HashMap<>();
            sortFieldMap.put("制度", "制度编号");
            sortFieldMap.put("标准", "标准编号");
            sortFieldMap.put("表单", "表单编号");
            sortFieldMap.put("岗位角色", "角色编码");
            sortFieldMap.put("要素", "要素编码");
            String defaultSortField = "modifyTime";
            String sortField = sortFieldMap.containsKey(name) && collect.contains(sortFieldMap.get(name))
                    ? "attrs." + sortFieldMap.get(name) : defaultSortField;
            esciSearchBean.setSortField(sortField);
        }
        esciSearchBean.setAsc(institutionSystemDto.getAsc()==null?true:institutionSystemDto.getAsc());
        esciSearchBean.setPageNum(institutionSystemDto.getPageNum());
        esciSearchBean.setPageSize(institutionSystemDto.getPageSize());
        ICISvc ciSvc = iciSwitchSvc.getCiSvc(LibType.DESIGN);
        Page<ESCIInfo> esciInfoPage = ciSvc.searchESCIByBean(esciSearchBean);
        List<ESCIInfo> data = esciInfoPage.getData();

        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(data, false);
            if (!CollectionUtils.isEmpty(ccCiInfos)) {
                if (institutionSystemDto.getName().equals("制度")||institutionSystemDto.getName().equals("要素")) {
                    queryProcessAssociationSystemData(ccCiInfos);
                }
            }
        Page<CcCiInfo> ccCiInfoPage = new Page<>();
        ccCiInfoPage.setPageNum(esciInfoPage.getPageNum());
        ccCiInfoPage.setPageSize(esciInfoPage.getPageSize());
        ccCiInfoPage.setTotalRows(esciInfoPage.getTotalRows());
        ccCiInfoPage.setTotalPages(esciInfoPage.getTotalPages());
        ccCiInfoPage.setData(ccCiInfos);
        return ccCiInfoPage;
    }


    private void queryProcessAssociationSystemData(List<CcCiInfo> ccCiInfos) {
        Set<String> institutionCiCodeList = ccCiInfos.stream()
                .map(c -> c.getCi().getCiCode())
                .collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(institutionCiCodeList)) {
            CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode("流程");
            CcCiClassInfo containLinkClass = iRltClassSvc.getRltClassByName(1L, "关联");
            ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
            // 源端是流程
            esRltSearchBean.setSourceClassIds(Collections.singletonList(classInfo.getCiClass().getId()));
            // 关联关系
            esRltSearchBean.setRltClassIds(Collections.singletonList(containLinkClass.getCiClass().getId()));
            // 目标端是制度/要素
            esRltSearchBean.setTargetCiCodes(institutionCiCodeList);
            List<CcCiRltInfo> privateCcCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, LibType.DESIGN);
            // 使用 Map<String, Set<String>> 存储制度代码和对应的流程名称集合，确保流程名称唯一
            Map<String, Set<String>> processMap = new HashMap<>();
            // 遍历关联关系，收集每个制度对应的流程名称
            for (CcCiRltInfo privateCcCiRltInfo : privateCcCiRltInfos) {
                String process = privateCcCiRltInfo.getSourceCiInfo().getAttrs().get("流程名称");
                String targetCiCode = privateCcCiRltInfo.getTargetCiInfo().getCi().getCiCode();
                processMap.computeIfAbsent(targetCiCode, k -> new LinkedHashSet<>()).add(process);
            }
            // 将收集到的流程名称设置到对应的 CcCiInfo 对象中
            for (CcCiInfo ciInfo : ccCiInfos) {
                String ciCode = ciInfo.getCi().getCiCode();
                Set<String> processes = processMap.getOrDefault(ciCode, new LinkedHashSet<>());
                ciInfo.getAttrs().put("关联流程", String.join("，", processes));
            }
        }
    }

    @Override
    public Integer delete(Long id) {
        if (ProcessRootDirectory.getAllHandleTypes().contains(id)){
            throw new MessageException("不能删除根目录");
        }
        EamCategory categorie = categoryDesignDao.getById(id);
        if (categorie==null){
            throw new MessageException("无此目录");
        }
        if (categorie.getDirName().equals("未分组")){
            throw new MessageException("不能删除未分组目录!");
        }
        List<EamDirectoryObjectAssociation> list = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("directoryId", categorie.getId())));
        List<String> collect = list.stream().map(EamDirectoryObjectAssociation::getCiCode).collect(Collectors.toList());
        if (collect.size()>0){
            throw new MessageException("当前目录下存在数据，请先删除下级数据");
        }
        return categoryDesignDao.deleteById(id);
    }

    @Override
    public Integer moveCi(ObjectMovingDto objectMovingDto) {
        // 检查目标目录是否存在或是否为根目录
        EamCategory targetCategory = null;
        if (!ProcessRootDirectory.getAllHandleTypes().contains(objectMovingDto.getTargetDirectoryId())) {
            targetCategory = categoryDesignDao.getById(objectMovingDto.getTargetDirectoryId());
            if (targetCategory == null) {
                throw new MessageException("目标目录已被删除，请重新选择!");
            }
        }

        if (objectMovingDto.getCiCode() != null) {
            // 处理CI移动
            if (targetCategory != null && targetCategory.getDirName().equals("未分组")) {
                eamDirectoryObjectAssociationDao
                        .deleteByQuery(QueryBuilders.termsQuery("ciCode.keyword", objectMovingDto.getCiCode()), true);
                return 1;
            }
            List<EamDirectoryObjectAssociation> associations = eamDirectoryObjectAssociationDao
                    .getListByQuery(QueryBuilders.termsQuery("ciCode.keyword", objectMovingDto.getCiCode()));
            if (!associations.isEmpty()) {
                EamDirectoryObjectAssociation existingAssociation = associations.get(0);
                if (existingAssociation.getDirectoryId().equals(objectMovingDto.getTargetDirectoryId())) {
                    throw new MessageException("无法移动到当前目录，请重新选择！");
                }
                eamDirectoryObjectAssociationDao.deleteById(existingAssociation.getId());
            }
            EamDirectoryObjectAssociation newAssociation = new EamDirectoryObjectAssociation();
            newAssociation.setCiCode(objectMovingDto.getCiCode());
            newAssociation.setId(ESUtil.getUUID());
            newAssociation.setDirectoryId(objectMovingDto.getTargetDirectoryId());
            eamDirectoryObjectAssociationDao.saveOrUpdate(newAssociation);
        } else {
            // 处理目录移动
            // 查询当前目录及其所有子目录
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("dataStatus", 1));
            query.must(QueryBuilders.wildcardQuery("dirPath.keyword",
                    "*#" + objectMovingDto.getCurrentDirectoryId() + "#*"));
            List<EamCategory> categories = categoryDesignDao.getListByQuery(query);

            if (categories.isEmpty()) {
                throw new MessageException("当前目录不存在，请重新选择");
            }

            // 检查是否移动到自身或子目录
            for (EamCategory category : categories) {
                if (category.getId().equals(objectMovingDto.getTargetDirectoryId())) {
                    throw new MessageException("无法移动到当前目录，请重新选择！");
                }
                if (category.getDirName().equals("未分组")) {
                    throw new MessageException("未分组目录不能移动");
                }
            }

            // 获取当前目录
            EamCategory currentCategory = categoryDesignDao
                    .selectOne(QueryBuilders.matchQuery("id", objectMovingDto.getCurrentDirectoryId()));

            // 如果不是移动到根目录，检查目标目录下是否有同名目录
            if (targetCategory != null) {
                List<EamCategory> targetSubCategories = categoryDesignDao.getListByQuery(
                        QueryBuilders.termsQuery("parentId", (Object) objectMovingDto.getTargetDirectoryId()));
                for (EamCategory subCategory : targetSubCategories) {
                    if (subCategory.getDirName().equals(currentCategory.getDirName())) {
                        throw new MessageException("此文件名与目标文件下级文件名相同,无法移动,请重新选择！");
                    }
                }
            } else {
                // 移动到根目录时，检查根目录下是否有同名目录
                List<EamCategory> rootCategories = categoryDesignDao.getListByQuery(
                        QueryBuilders.termsQuery("parentId", (Object) objectMovingDto.getTargetDirectoryId()));
                for (EamCategory rootCategory : rootCategories) {
                    if (rootCategory.getDirName().equals(currentCategory.getDirName())) {
                        throw new MessageException("此文件名与根目录下文件名相同,无法移动,请重新选择！");
                    }
                }
            }

            // 更新当前目录及其所有子目录信息
            if (targetCategory != null) {
                // 移动到普通目录
                updateCategoryAndSubcategories(currentCategory, targetCategory);
            } else {
                // 移动到根目录
                updateCategoryToRoot(currentCategory, objectMovingDto.getTargetDirectoryId());
            }
        }
        return 1;
    }

    private void updateCategoryToRoot(EamCategory currentCategory, Long rootId) {
        // 更新当前目录信息
        currentCategory.setDirPath("#" + currentCategory.getId() + "#");
        currentCategory.setParentId(rootId);
        currentCategory.setModifier(SysUtil.getCurrentUserInfo().getLoginCode());
        currentCategory.setModifyTime(ESUtil.getNumberDateTime());
        currentCategory.setDirLvl(1);
        categoryDesignDao.saveOrUpdate(currentCategory);

        // 查询当前目录的所有子目录
        BoolQueryBuilder subQuery = QueryBuilders.boolQuery();
        subQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        subQuery.must(QueryBuilders.wildcardQuery("dirPath.keyword", "*#" + currentCategory.getId() + "#*"));
        List<EamCategory> subCategories = categoryDesignDao.getListByQuery(subQuery);

        // 递归更新子目录信息
        for (EamCategory subCategory : subCategories) {
            if (!subCategory.getId().equals(currentCategory.getId())) { // 排除当前目录
                updateCategoryAndSubcategories(subCategory, currentCategory);
            }
        }
    }

    private void updateCategoryAndSubcategories(EamCategory currentCategory, EamCategory targetCategory) {
        // 更新当前目录信息
        currentCategory.setDirPath(targetCategory.getDirPath() + currentCategory.getId() + "#");
        currentCategory.setParentId(targetCategory.getId());
        currentCategory.setModifier(SysUtil.getCurrentUserInfo().getLoginCode());
        currentCategory.setModifyTime(ESUtil.getNumberDateTime());
        currentCategory.setDirLvl(targetCategory.getDirLvl() + 1);
        categoryDesignDao.saveOrUpdate(currentCategory);

        // 查询当前目录的所有子目录
        BoolQueryBuilder subQuery = QueryBuilders.boolQuery();
        subQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        subQuery.must(QueryBuilders.wildcardQuery("dirPath.keyword", "*#" + currentCategory.getId() + "#*"));
        List<EamCategory> subCategories = categoryDesignDao.getListByQuery(subQuery);

        // 递归更新子目录信息
        for (EamCategory subCategory : subCategories) {
            if (!subCategory.getId().equals(currentCategory.getId())) { // 排除当前目录
                updateCategoryAndSubcategories(subCategory, currentCategory);
            }
        }
    }

    @Override
    public List<InstitutionSystemTreeNewDto> getInstitutionSystemTreeWithCiNew(Long id) {
        List<InstitutionSystemTreeNewDto> treeDto = buildTree1(categoryDesignDao.getListByQuery(QueryBuilders.termsQuery("parentId", (Object) id)));
        ProcessRootDirectory byHandleType = ProcessRootDirectory.getByHandleType(id);
        CcCiClassInfo ciClassByClassCode = iciClassSvc.getCiClassByClassCode(byHandleType.getHandleDesc());
        for (InstitutionSystemTreeNewDto institutionSystemTreeNewDto : treeDto) {
            if (institutionSystemTreeNewDto.getName().equals("未分组")) {
                ESCISearchBeanVO bean = new ESCISearchBeanVO();
                bean.setClassCodes(Collections.singletonList(ciClassByClassCode.getCiClass().getClassCode()));
                // 获取所有符合条件的 ESCIInfo 数据
                List<ESCIInfo> result = iamsESCIDesignSvc.getListByQuery(QueryBuilders.termsQuery("classId", (Object) ciClassByClassCode.getCiClass().getId()));

                // 获取所有已存在的 EamDirectoryObjectAssociation 数据
                List<EamDirectoryObjectAssociation> listByQuery = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.matchAllQuery());
                List<String> existingCiCodes = listByQuery.stream().map(EamDirectoryObjectAssociation::getCiCode).collect(Collectors.toList());

                // 过滤出不包含在 existingCiCodes 中的 ESCIInfo 数据
                List<ESCIInfo> notExistingCiCodes = result.stream()
                        .filter(datum -> !existingCiCodes.contains(datum.getCiCode()))
                        .collect(Collectors.toList());
                List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(notExistingCiCodes, true);
                // 将不包含的数据添加到 children 列表中
                if (notExistingCiCodes != null) {
                    institutionSystemTreeNewDto.setChildren1(Arrays.asList(ccCiInfos.toArray()));
                }
            }
        }


        return sortTree(treeDto);
    }

    private List<InstitutionSystemTreeNewDto> buildTree1(List<EamCategory> categories) {
        if (categories == null || categories.isEmpty()) {
            return Collections.emptyList();
        }

        List<InstitutionSystemTreeNewDto> treeDto = new ArrayList<>();
        for (EamCategory category : categories) {
            InstitutionSystemTreeNewDto treeDto1 = new InstitutionSystemTreeNewDto();
            treeDto1.setName(category.getDirName());
            treeDto1.setId(category.getId());
            treeDto1.setCreateTime(category.getCreateTime());
            List<Object> childrens = new ArrayList<>();
            List<EamDirectoryObjectAssociation> listByQuery = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.termsQuery("directoryId",(Object) category.getId()));
            List<String> codes = listByQuery.stream().map(EamDirectoryObjectAssociation::getCiCode).collect(Collectors.toList());
            List<ESCIInfo> ciByCodes = iciSwitchSvc.getCiByCodes(codes, null, LibType.DESIGN);
            List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(ciByCodes, true);
            if (!ccCiInfos.isEmpty()) {
                for (CcCiInfo ccCiInfo : ccCiInfos) {
                    childrens.add(ccCiInfo);
                }
            }
            List<InstitutionSystemTreeNewDto> children = buildTree1(categoryDesignDao.getListByQuery(QueryBuilders.termsQuery("parentId", (Object) category.getId())));
            if (!children.isEmpty()) {
                childrens.addAll(children);
            }
            if (!childrens.isEmpty()) {
                treeDto1.setChildren1(childrens);
            }

            treeDto.add(treeDto1);
        }
        return treeDto;
    }


}
