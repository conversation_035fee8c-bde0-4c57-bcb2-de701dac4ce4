package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.CIRltDelInfo;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.model.enums.CIRltDelType;
import com.uinnova.product.eam.model.vo.CIRltDelInfoVo;
import com.uinnova.product.eam.service.CIRltDelInfoSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.es.CIRltDelInfoDao;
import com.uinnova.product.eam.service.es.EamMatrixInstanceDesignDao;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CIRltDelInfoSvcImpl implements CIRltDelInfoSvc {

    @Autowired
    private CIRltDelInfoDao ciRltDelInfoDao;
    @Autowired
    private EamMatrixInstanceDesignDao instanceDesignDao;
    @Autowired
    private ESDiagramSvc diagramApiClient;


    @Override
    public void saveBatch(List<CIRltDelInfo> ciRltDelInfos) {
        if (CollectionUtils.isEmpty(ciRltDelInfos)) {
            return;
        }
        ciRltDelInfoDao.saveOrUpdateBatch(ciRltDelInfos);
    }

    @Override
    public List<CIRltDelInfoVo> list(Long noticeId) {
        TermQueryBuilder query = QueryBuilders.termQuery("noticeId", noticeId);
        List<CIRltDelInfo> infos = ciRltDelInfoDao.getListByQuery(query);
        if (CollectionUtils.isEmpty(infos)) {
            return new ArrayList<>();
        }
        infos.sort(new Comparator<CIRltDelInfo>() {
            final Collator collator = Collator.getInstance(Locale.CHINA);

            @Override
            public int compare(CIRltDelInfo s1, CIRltDelInfo s2) {
                int rltClassNameCompare = collator.compare(s1.getRltClassName(), s2.getRltClassName());
                if (rltClassNameCompare != 0) {
                    return rltClassNameCompare;
                }
                return collator.compare(s1.getSourceName(), s2.getSourceName());
            }
        });

        Set<String> diagramIds = infos.stream()
                .filter(info -> "DIAGRAM".equals(info.getRelateAssertType()))
                .filter(info -> CIRltDelType.DIAGRAM_PUSH.name().equals(info.getDelType()))
                .map(CIRltDelInfo::getRelateAssertId).collect(Collectors.toSet());
        Set<String> existDiagramIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(diagramIds)) {
            List<ESDiagram> diagrams = diagramApiClient.queryDBDiagramInfoByIds(diagramIds.toArray(new String[0]));
            existDiagramIds = diagrams.stream().map(ESDiagram::getDEnergy).collect(Collectors.toSet());
        }

        Set<Long> matrixIds = infos.stream()
                .filter(info -> "MATRIX".equals(info.getRelateAssertType()))
                .filter(info -> CIRltDelType.MATRIX_PUSH.name().equals(info.getDelType()))
                .map(info -> Long.valueOf(info.getRelateAssertId())).collect(Collectors.toSet());
        Set<String> existMatrixIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(matrixIds)) {
            BoolQueryBuilder matrixQuery = QueryBuilders.boolQuery();
            matrixQuery.must(QueryBuilders.termQuery("status", 1));
            matrixQuery.must(QueryBuilders.termsQuery("id", matrixIds));
            List<EamMatrixInstance> matrixInstances = instanceDesignDao.getListByQuery(matrixQuery);
            existMatrixIds = matrixInstances.stream().map(instance -> instance.getId().toString()).collect(Collectors.toSet());
        }

        List<CIRltDelInfoVo> vos = EamUtil.copy(infos, CIRltDelInfoVo.class);
        for (CIRltDelInfoVo vo : vos) {
            if (CIRltDelType.DIAGRAM_DESIGN_DROP.name().equals(vo.getDelType())
                    || CIRltDelType.MATRIX_DESIGN_DROP.name().equals(vo.getDelType())) {
                vo.setRelateAssertDel(true);
                continue;
            }
            String relateAssertId = vo.getRelateAssertId();
            vo.setRelateAssertDel(AssetType.DIAGRAM.name().equals(vo.getRelateAssertType()) ?
                    !existDiagramIds.contains(relateAssertId) : !existMatrixIds.contains(relateAssertId));
        }
        return vos;
    }
}
