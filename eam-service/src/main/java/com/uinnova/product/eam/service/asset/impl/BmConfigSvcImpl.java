package com.uinnova.product.eam.service.asset.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.util.ESUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BmConfigSvcImpl implements BmConfigSvc {

    @Autowired
    ICISwitchSvc iciSwitchSvc;

    @Resource
    IEamArtifactSvc eamArtifactSvc;

    @Resource
    ICIClassApiSvc iciClassApiSvc;
    private static final String BM_CONFIG = "视图配置";
    @Override
    public JSONObject getBusComponentConfig() {
        String configType = getConfigType("BUS_COMPONENT_CONFIG");
        if (BinaryUtils.isEmpty(configType)) {
            return null;
        }
        return JSON.parseObject(configType);
    }

    @Override
    public String getConfigType(String config){
        ESAttrBean attrBean = new ESAttrBean();
        attrBean.setKey("CONF_TYPE");
        attrBean.setOptType(1);
        attrBean.setValue(config);
        List<ESAttrBean> attrBeans = Collections.singletonList(attrBean);
        ESCIClassSearchBean classBean = new ESCIClassSearchBean();
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(BM_CONFIG);
        classBean.setCdt(cCcCiClass);
        List<CcCiClassInfo> classList = iciClassApiSvc.queryCiClassInfoListBySearchBean(classBean);
        if(CollectionUtils.isEmpty(classList)){
            throw new ServiceException("未查询到"+BM_CONFIG+"分类信息!");
        }
        Long classId = classList.get(0).getCiClass().getId();
        ESCISearchBean bean = new ESCISearchBean();
        bean.setPageNum(1);
        bean.setPageSize(1);
        bean.setClassIds(Collections.singletonList(classId));
        bean.setAndAttrs(attrBeans);
        bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        Page<ESCIInfo> page = iciSwitchSvc.searchESCIByBean(bean, LibType.DESIGN);
        if (!CollectionUtils.isEmpty(page.getData())) {
            Object configJson = page.getData().get(0).getAttrs().get("CONF_JSON");
            if (BinaryUtils.isEmpty(configJson)) {
                return "";
            } else {
                return configJson.toString();
            }
        }
        return "";
    }

    @Override
    public JSONObject checkShowTaskTag() {
        String taskTag = getConfigType("BUS_TASK_TAG");
        if (BinaryUtils.isEmpty(taskTag)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(taskTag);
        String artifactName = jsonObject.getString("artifactName");
        if (BinaryUtils.isEmpty(artifactName)) {
            return null;
        }
        Long artifactId = eamArtifactSvc.getIdByArtifactName(artifactName);
        jsonObject.put("artifactId", artifactId);

        String className = jsonObject.getString("className");
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(className);
        List<CcCiClassInfo> classList = iciClassApiSvc.queryCiClassInfoList(1L, cCcCiClass, null, false);
        if (CollectionUtils.isEmpty(classList)) {
            return jsonObject;
        }
        Long classId = classList.get(0).getCiClass().getId();
        jsonObject.put("classId", classId);
        return jsonObject;
    }

    @Override
    public Long saveOrUpdateConfType(String confType, String confJson,String confName) {

        ESAttrBean attrBean = new ESAttrBean();
        attrBean.setKey("CONF_TYPE");
        attrBean.setOptType(1);
        attrBean.setValue(confType);
        List<ESAttrBean> attrBeans = Collections.singletonList(attrBean);
        ESCIClassSearchBean classBean = new ESCIClassSearchBean();
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(BM_CONFIG);
        classBean.setCdt(cCcCiClass);
        List<CcCiClassInfo> classList = iciClassApiSvc.queryCiClassInfoListBySearchBean(classBean);
        Long classId = classList.get(0).getCiClass().getId();
        ESCISearchBean bean = new ESCISearchBean();
        bean.setPageNum(1);
        bean.setPageSize(1);
        bean.setClassIds(Collections.singletonList(classId));
        bean.setAndAttrs(attrBeans);
        bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        Page<ESCIInfo> page = iciSwitchSvc.searchESCIByBean(bean, LibType.DESIGN);
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        if (!CollectionUtils.isEmpty(page.getData())) {
            ESCIInfo esciInfo = page.getData().get(0);
            ci.setId(esciInfo.getId());
        }
        ciInfo.setCi(ci);
        ci.setClassId(classId);
        ci.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        HashMap<String, String> attrs = new HashMap<>();
        attrs.put("CONF_TYPE", confType);
        attrs.put("CONF_JSON", confJson);
        attrs.put("CONF_NAME", confName);
        ciInfo.setAttrs(attrs);
        return iciSwitchSvc.saveOrUpdateCI(ciInfo, LibType.DESIGN);
    }
}
