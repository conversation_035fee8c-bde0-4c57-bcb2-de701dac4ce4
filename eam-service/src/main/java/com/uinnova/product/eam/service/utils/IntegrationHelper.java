package com.uinnova.product.eam.service.utils;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import lombok.extern.slf4j.Slf4j;
import java.util.*;

/**
 * 统一集成转换类
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class IntegrationHelper {

    public static List<VcCiRltInfo> converCiRlt(List<CcCiRltInfo> data) {
        if (data == null) { return Collections.emptyList(); }
        List<VcCiRltInfo> ret = new ArrayList<VcCiRltInfo>();
        data.forEach(rltInfo -> {
            ret.add(conver(rltInfo));
        });
        return ret;
    }


    public static VcCiRltInfo conver(CcCiRltInfo rltInfo) {
        VcCiRltInfo vr = new VcCiRltInfo();
        vr.setAttrs(getOrCrateAttrs(rltInfo));
        vr.setCiRlt(rltInfo.getCiRlt());
        vr.setSourceCiInfo(rltInfo.getSourceCiInfo());
        vr.setTargetCiInfo(rltInfo.getTargetCiInfo());
        return vr;
    }

    public static Map<String, String> getOrCrateAttrs(CcCiRltInfo rltInfo) {
        return rltInfo.getAttrs() == null ? new HashMap<>(0) : rltInfo.getAttrs();
    }

}
