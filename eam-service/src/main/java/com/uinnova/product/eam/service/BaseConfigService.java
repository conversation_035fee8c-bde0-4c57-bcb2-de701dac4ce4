
package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;

import java.util.List;

/**
 * 配置service
 */
public interface BaseConfigService {

	/**
	 * 查询配置列表
	 *
	 * @param cdt 查询条件
	 * @return 配置列表
	 */
	List<VcBaseConfig> queryBaseConfigList(CVcBaseConfig cdt);

	/**
	 * 配置类更新
	 *
	 * @param record {@link VcBaseConfig}
	 * @return id
	 */
	Long saveOrUpdateBaseConfig(VcBaseConfig record);

	/**
	 * 查找激活的配置
	 *
	 * @param cfgCode 配置代码
	 * @return {@link VcBaseConfig}
	 */
	VcBaseConfig getSelectedConfig(String cfgCode);

	/**
	 * 选择配置/激活配置
	 * @param id 配置id
	 */
	void selectConfig(Long id);

	/**
	 * 删除配置
	 * @param id 配置id
	 */
	void deleteConfig(Long id);

	/**
	 * 通过配置编号获取配置列表
	 * @param cfgCodes
	 * @return
	 */
	List<VcBaseConfig> findBaseConfigList(List<String> cfgCodes);

	/**
	 * 批量新增基础配置
	 * @param baseConfigList
	 * @return
	 */
	Long saveOrUpdateBaseConfigBatch(List<VcBaseConfig> baseConfigList);
}
