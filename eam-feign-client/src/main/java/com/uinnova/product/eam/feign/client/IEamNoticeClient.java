package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.feign.EamFeignConst;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import com.uinnova.product.eam.model.asset.ShareMsgDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = EamFeignConst.SERVER_NAME, path = EamFeignConst.SERVER_ROOT , configuration = EamFeignConfig.class)
public interface IEamNoticeClient {

    @PostMapping("/eam/notice/share/msg/save")
    void pushNotice(@RequestBody List<ShareMsgDTO> shareMsgDTOS);
}
