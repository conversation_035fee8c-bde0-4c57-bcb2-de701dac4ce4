package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.diagram.model.ESDiagramShareRecordResult;
import com.uinnova.product.eam.base.diagram.model.ShareRecordQueryBean;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "${fuxi-feign-server-name:eam-fuxi}", path = "${fuxi-feign-server-path:tarsier-eam}/design" +
        "/shareDiagram",
        configuration = EamFeignConfig.class)
public interface DiagramShareFeignClient {

    @PostMapping("/querySharedDiagramPage")
    Map<String, Object> querySharedDiagramPage(@RequestBody ShareRecordQueryBean queryBean);

    @PostMapping("/querySharedDiagramFeign")
    public List<ESDiagramShareRecordResult> querySharedDiagramFeign(@RequestBody ShareRecordQueryBean queryBean);
}
