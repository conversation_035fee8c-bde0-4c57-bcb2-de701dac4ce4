package com.uinnova.product.eam.feign;

public interface FeignConst {
    String APPLICATION = "tarsier-eam-server";

    String SPECIFICATION_PATH = "feign/eam/asset/specification";
    String EDITION_PATH = "feign/eam/asset/edition";
    String DICT_PATH = "feign/eam/system/dict";
    String RESOURCE_PATH = "feign/eam/resource";
    String SUBSYSTEM_PATH = "feign/eam/asset/subsystem";
    String SYSTEM_PATH = "feign/eam/asset/system";
    String ARCHITECTURAL_PATH = "feign/eam/asset/architectural";
    String SOFTWARE_PATH = "feign/eam/asset/software";
    String FREESOFTWARE_PATH = "feign/eam/asset/software/free";
    String NONFUNCTIONAL_PATH="feign/eam/asset/nonfunctional";
}
