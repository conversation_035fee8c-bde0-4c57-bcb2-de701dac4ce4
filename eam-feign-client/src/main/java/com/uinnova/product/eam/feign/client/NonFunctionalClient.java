package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.model.NonFunctionalMetaData;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.asset.NonFunctionalMasterDTO;
import com.uinnova.product.eam.model.asset.NonFunctionalMasterEleDTO;
import com.uinnova.product.eam.model.asset.NonFunctionalNameCheckDTO;
import com.uinnova.product.eam.model.asset.SearchNonFunctionalDTO;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.NONFUNCTIONAL_PATH)
public interface NonFunctionalClient {
    @GetMapping("selectAttrConf")
    NonFunctionalMasterEleDTO selectAttrConf(@RequestBody NonFunctionalMetaData metaData);

    boolean checkNonFunctionalName(NonFunctionalNameCheckDTO nonFunctionalNameCheckDTO);

    @PostMapping("save")
    Long save(@RequestBody NonFunctionalMasterDTO copy);

    @GetMapping("/selectCount")
    long selectCount(String nonFunctionalClassName);
    @PostMapping("/selectList")
    List<NonFunctionalMasterDTO> selectList(SearchNonFunctionalDTO searchNonFunctionalDTO, NonFunctionalMetaData metaData);
    @PostMapping("/getNonFunctionalById")
    NonFunctionalMasterDTO getNonFunctionalById(NonFunctionalMasterDTO record);
    @GetMapping("/getNonfunctionalByLoginCode")
    List<ESCIInfo> getNonfunctionalByLoginCode(String loginCode);
    @GetMapping("/checkName")
    boolean checkName(NonFunctionalNameCheckDTO nonFunctionalNameCheckDTO);
}
