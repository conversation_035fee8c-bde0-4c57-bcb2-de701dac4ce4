package com.uinnova.product.eam.feign.client;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.asset.SearchSystemDTO;
import com.uinnova.product.eam.model.asset.SpecialMasterDTO;
import com.uinnova.product.eam.model.asset.SystemCheckDTO;
import com.uinnova.product.eam.model.asset.SystemMasterDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.SYSTEM_PATH)
public interface SystemMasterClient {

    @PostMapping("saveSystem")
    Long saveSystem(@RequestBody SystemMasterDTO record);

    @PostMapping("selectSystemList")
    List<Map<String,String>> selectSystemList(@RequestBody SearchSystemDTO record);

    @PostMapping("getSystemByCiCode")
    List getSystemByCiCode(@RequestBody SearchSystemDTO searchSystemDTO);

    @PostMapping("updateSystem")
    Long updateSystem(@RequestBody SystemMasterDTO systemMasterDTO);

    @PostMapping("check")
    boolean check(@RequestBody SystemCheckDTO systemCheckDTO);

    @GetMapping("systemNameisExist")
    boolean systemNameisExist(@RequestParam("ciClassId") Long ciClassId, @RequestParam("subsystemName") String subsystemName);

    @GetMapping("systemLogoisExist")
    boolean systemLogoisExist(@RequestParam("ciClassId") Long ciClassId, @RequestParam("logoName") String logoName, @RequestParam("ciCode") Long ciCode);
}

