package com.uinnova.product.eam.config;

/**
 * 配置类
 * <AUTHOR>
 */
public class Env {
    public static final String BM_TYPE = "BUSINESS";
    public static final String BM_BELONG_ELEMENT = "所属上级元素";

    /**
     * 视图是否删除进入回收站 1表示没删除
     */
    public static final Integer STATUS_VALID = 1;
    /**
     * 视图是否删除进入回收站 0表示删除
     */
    public static final Integer STATUS_NOT_VALID = 0;
    /**
     * 从回收站移除 1表示没移除
     */
    public static final Integer DATA_STATUS_VALID = 1;
    /**
     * 从回收站移除 0表示移除
     */
    public static final Integer DATA_STATUS_NOT_VALID = 0;
    /**
     * 1表示当前视图主版本
     */
    public static final Integer MAJOR_VERSION = 1;
    /**
     * 0表示当前视图历史版本
     */
    public static final Integer HISTORY_VERSION_FLAG = 0;
    /**
     *  视图是否公开 1表示公开
     */
    public static final Integer OPEN = 1;
    /**
     *  视图是否公开 0表示不公开
     */
    public static final Integer NOT_OPEN = 1;

    /**
     * 业务组件建模常量配置
     */
    public static final String BUS_TREE_NAME = "BUS_TREE_NAME";
    public static final String BM_BELONG_ZJ = "BM_BELONG_ZJ";
    public static final int BM_COMPONENT_DIR_TYPE = 100;
    public final static String BM_COMPONENT = "COMPONENT";
    public final static String BM_DOMAIN = "DOMAIN";
    public final static String BM_TASK = "TASK";
    public final static String BM_ACTIVITY = "ACTIVITY";
    public final static String BM_PROCESS = "PROCESS";
    public final static String BM_RULE = "RULE";
    public final static String BM_OBJECT = "BUS_OBJECT";
    public static final String BM_ENTITY = "ENTITY";
    public final static String COMPONENT_DEFINITION_DIAGRAM = "COMPONENT_DEFINITION_DIAGRAM";
    public final static String DOMAIN_COMPONENT_RLT_DIAGRAM = "DOMAIN_COMPONENT_RLT_DIAGRAM";
    public static final int FX_GENERAL_DIR_TYPE = 1;

    /**
     * 新疆农信定制服务分析配置项
     */
    public static final String NX_SERVICE_CONFIG = "NX_SERVICE_CONFIG";
    /**
     * 通用视图发布
     */
    public static final int GENERAL_DIAGRAM_PUBLISH = 4;
    /**
     * 通用视图检出不覆盖
     */
    public static final int GENERAL_DIAGRAM_CHECK_OUT = 2;
    /**
     * 通用视图检出覆盖
     */
    public static final int GENERAL_DIAGRAM_CHECK_OUT_COVER = 3;
    /**
     * 通用视图检出另存为
     */
    public static final int GENERAL_DIAGRAM_COPY = 1;

    /**
     * 视图发布 --- copy视图私有库到设计库
     */
    public static final int DIAGRAM_COPY_LOCAL_TO_PUBLISH = 1;

    /**
     * 视图发布 --- copy视图设计库到设计库
     */
    public static final int DIAGRAM_COPY_PUBLISH_TO_PUBLISH = 2;

    /**
     * 视图检出 --- copy视图设计库到私有库(创建)
     */
    public static final int DIAGRAM_COPY_PUBLISH_TO_LOCAL_CREATE = 3;

    /**
     * 视图检出 --- copy视图设计库到私有库(覆盖)
     */
    public static final int DIAGRAM_COPY_PUBLISH_TO_LOCAL_COVER = 4;

    /**
     * 视图检出 --- copy视图设计库到私有库(另存为)
     */
    public static final int DIAGRAM_COPY_PUBLISH_TO_LOCAL_NORELATION = 5;

    /**
     * 业务流程建模资产顶层目录
     */
    public static final Long BM_TOP_DIR_CODE = 0L;
    private static final EnvConfig ENV = ApplicationContextBean.getBean(EnvConfig.class);
    public static final String TABLE_NAME = "TABLE_NAME";
    public static EnvConfig.App APP = ENV.getApp();
    public static EnvConfig.Dict DICT = ENV.getDict();
    public static EnvConfig.Asset.Specification SPECIFICATION = ENV.getAsset().getSpecification();
    public static EnvConfig.Asset.Specification.Edition EDITION = ENV.getAsset().getSpecification().getEdition();
    public static EnvConfig.Asset.AppSystem APP_SYSTEM = ENV.getAsset().getAppSystem();
    public static EnvConfig.Asset.AppSubSystem APP_SUBSYSTEM = ENV.getAsset().getAppSubSystem();
    public static EnvConfig.Asset.ArchResolution ARCH_RESOLUTION = ENV.getAsset().getArchResolution();
    public static EnvConfig.Asset.FreeSoftware FREE_SOFTWARE = ENV.getAsset().getFreeSoftware();
    public static EnvConfig.Asset.PaySoftware PAY_SOFTWARE = ENV.getAsset().getPaySoftware();
    public static EnvConfig.Asset.NoFunctionalItem NO_FUNCTIONAL_ITEM = ENV.getAsset().getNoFunctionalItem();
    public static EnvConfig.Asset.NoFunctionalMsg NO_FUNCTIONAL_Msg = ENV.getAsset().getNoFunctionalMsg();
    public static final EnvConfig.Asset.HtAppSystem HT_APP_SYSTEM = ENV.getAsset().getHtAppSystem();

    /**
     * 数据建模相关常量
     */
    public static final EnvConfig.DataModel.ConceptionEntity CONCEPTION_ENTITY = ENV.getDataModel().getConceptionEntity();
    public static final EnvConfig.DataModel.LogicEntity LOGIC_ENTITY = ENV.getDataModel().getLogicEntity();
    public static final EnvConfig.DataModel.SysLogicEntity SYS_LOGIC_ENTITY = ENV.getDataModel().getSysLogicEntity();
    public static final EnvConfig.DataModel.Attributes ATTRIBUTES = ENV.getDataModel().getAttributes();
    public static final EnvConfig.DataModel.DataType DATA_TYPE = ENV.getDataModel().getDataType();
    public static final EnvConfig.DataModel.Standard STANDARD = ENV.getDataModel().getStandard();
    public static final EnvConfig.DataModel.StandardClass STANDARD_CLASS = ENV.getDataModel().getStandardClass();
    public static final EnvConfig.DataModel.PhysicalEntity PHYSICAL_ENTITY = ENV.getDataModel().getPhysicalEntity();
    public static final EnvConfig.DataModel.Domain DOMAIN = ENV.getDataModel().getDomain();
    public static final EnvConfig.DataModel.DomainClass DOMAIN_CLASS = ENV.getDataModel().getDomainClass();
    /**
     * 实体与属性间关系分类
     */
    public static final String DM_INCLUDE = "DM-包含";
    /**
     * 实体与实体间关系分类
     */
    public static final String DERIVATION = "衍生";
    /**
     * 实体属性与标准库属性关系分类
     */
    public static final String DM_QUOTE = "DM-引用";
    /**
     * 实体与实体间关系-一对一
     */
    public static final String ONE_TO_ONE = "一对一";
    /**
     * 实体与实体间关系-一对多
     */
    public static final String ONE_TO_MANY = "一对多";
    /**
     * 实体与实体间关系-多对多
     */
    public static final String MANY_TO_MANY = "多对多";
    /**
     * 实体与实体间关系-父子
     */
    public static final String PATERNITY = "父子";
}
