package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("节点路径详情表[VC_NODE_LINKED_PATH]")
public class CVcNodeLinkedPath implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("节点路径ID[LINKED_ID] operate-Equal[=]")
	private Long linkedId;


	@Comment("节点路径ID[LINKED_ID] operate-In[in]")
	private Long[] linkedIds;


	@Comment("节点路径ID[LINKED_ID] operate-GTEqual[>=]")
	private Long startLinkedId;

	@Comment("节点路径ID[LINKED_ID] operate-LTEqual[<=]")
	private Long endLinkedId;


	@Comment("节点代码列表[NODE_CODES] operate-Like[like]    节点代码列表用'隔开")
	private String nodeCodes;


	@Comment("节点列表序号[PATH_ORDER] operate-Equal[=]")
	private Integer pathOrder;


	@Comment("节点列表序号[PATH_ORDER] operate-In[in]")
	private Integer[] pathOrders;


	@Comment("节点列表序号[PATH_ORDER] operate-GTEqual[>=]")
	private Integer startPathOrder;

	@Comment("节点列表序号[PATH_ORDER] operate-LTEqual[<=]")
	private Integer endPathOrder;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getLinkedId() {
		return this.linkedId;
	}
	public void setLinkedId(Long linkedId) {
		this.linkedId = linkedId;
	}


	public Long[] getLinkedIds() {
		return this.linkedIds;
	}
	public void setLinkedIds(Long[] linkedIds) {
		this.linkedIds = linkedIds;
	}


	public Long getStartLinkedId() {
		return this.startLinkedId;
	}
	public void setStartLinkedId(Long startLinkedId) {
		this.startLinkedId = startLinkedId;
	}


	public Long getEndLinkedId() {
		return this.endLinkedId;
	}
	public void setEndLinkedId(Long endLinkedId) {
		this.endLinkedId = endLinkedId;
	}


	public String getNodeCodes() {
		return this.nodeCodes;
	}
	public void setNodeCodes(String nodeCodes) {
		this.nodeCodes = nodeCodes;
	}


	public Integer getPathOrder() {
		return this.pathOrder;
	}
	public void setPathOrder(Integer pathOrder) {
		this.pathOrder = pathOrder;
	}


	public Integer[] getPathOrders() {
		return this.pathOrders;
	}
	public void setPathOrders(Integer[] pathOrders) {
		this.pathOrders = pathOrders;
	}


	public Integer getStartPathOrder() {
		return this.startPathOrder;
	}
	public void setStartPathOrder(Integer startPathOrder) {
		this.startPathOrder = startPathOrder;
	}


	public Integer getEndPathOrder() {
		return this.endPathOrder;
	}
	public void setEndPathOrder(Integer endPathOrder) {
		this.endPathOrder = endPathOrder;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


