package com.uinnova.product.eam.comm.model.es;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/27 14:52
 */
@Getter
@Setter
@RequiredArgsConstructor
@ToString
public class FlowSystemProcessSingData {

    private Long id;

    private String businessKey;

    private String ciCode;

    /**
     * 审批动作：通过or不通过
     */
    private String actionType;

    private String signUserCode;

    private String linkPublishProcessInstanceId;

    private String remarks;

    private Long createTime;

    private Long modifyTime;

}
