package com.uinnova.product.eam.base.diagram.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Filename: RedirectCheckUtil
 * @Author: WangBaoDe
 * @Data:2024/3/11 17:44
 */
@Component
public class RedirectCheckUtil {

    private static final String DEFAULT_PATH = "http://ip:port/";

    private static String allowRedirectHost;

    @Value("${yiqi.redirect.whitelist:http://ip:port/}")
    public void setAllowRedirectHost(String allowRedirectHost){
        RedirectCheckUtil.allowRedirectHost = allowRedirectHost;
    }

    /**
     * 通过白名单对路径做匹配，如果匹配成功，则跳转; 未匹配成功，提示失败
     * @param redirectUrl
     * @return
     */
    public static boolean checkRedirectUrl(String redirectUrl) {
        boolean checkFlag = false;
        if (DEFAULT_PATH.equals(allowRedirectHost)) {
            return true;
        }
        String[] allowHostAddrs = allowRedirectHost.split(";");
        if (allowHostAddrs.length > 0) {
            for (String allowHostAddr : allowHostAddrs) {
                if (redirectUrl.startsWith(allowHostAddr)) {
                    checkFlag = true;
                    break;
                }
            }
        }
        return checkFlag;
    }
}