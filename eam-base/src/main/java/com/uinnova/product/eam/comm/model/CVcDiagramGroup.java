package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图组表[VC_DIAGRAM_GROUP]")
public class CVcDiagramGroup implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("组ID[GROUP_ID] operate-Equal[=]")
	private Long groupId;


	@Comment("组ID[GROUP_ID] operate-In[in]")
	private Long[] groupIds;


	@Comment("组ID[GROUP_ID] operate-GTEqual[>=]")
	private Long startGroupId;

	@Comment("组ID[GROUP_ID] operate-LTEqual[<=]")
	private Long endGroupId;


	@Comment("发布人[DEPLOY_USER_ID] operate-Equal[=]")
	private Long deployUserId;


	@Comment("发布人[DEPLOY_USER_ID] operate-In[in]")
	private Long[] deployUserIds;


	@Comment("发布人[DEPLOY_USER_ID] operate-GTEqual[>=]")
	private Long startDeployUserId;

	@Comment("发布人[DEPLOY_USER_ID] operate-LTEqual[<=]")
	private Long endDeployUserId;


	@Comment("发布时间[DEPLOY_TIME] operate-Equal[=]")
	private Long deployTime;


	@Comment("发布时间[DEPLOY_TIME] operate-In[in]")
	private Long[] deployTimes;


	@Comment("发布时间[DEPLOY_TIME] operate-GTEqual[>=]")
	private Long startDeployTime;

	@Comment("发布时间[DEPLOY_TIME] operate-LTEqual[<=]")
	private Long endDeployTime;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Long getGroupId() {
		return this.groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}


	public Long[] getGroupIds() {
		return this.groupIds;
	}
	public void setGroupIds(Long[] groupIds) {
		this.groupIds = groupIds;
	}


	public Long getStartGroupId() {
		return this.startGroupId;
	}
	public void setStartGroupId(Long startGroupId) {
		this.startGroupId = startGroupId;
	}


	public Long getEndGroupId() {
		return this.endGroupId;
	}
	public void setEndGroupId(Long endGroupId) {
		this.endGroupId = endGroupId;
	}


	public Long getDeployUserId() {
		return this.deployUserId;
	}
	public void setDeployUserId(Long deployUserId) {
		this.deployUserId = deployUserId;
	}


	public Long[] getDeployUserIds() {
		return this.deployUserIds;
	}
	public void setDeployUserIds(Long[] deployUserIds) {
		this.deployUserIds = deployUserIds;
	}


	public Long getStartDeployUserId() {
		return this.startDeployUserId;
	}
	public void setStartDeployUserId(Long startDeployUserId) {
		this.startDeployUserId = startDeployUserId;
	}


	public Long getEndDeployUserId() {
		return this.endDeployUserId;
	}
	public void setEndDeployUserId(Long endDeployUserId) {
		this.endDeployUserId = endDeployUserId;
	}


	public Long getDeployTime() {
		return this.deployTime;
	}
	public void setDeployTime(Long deployTime) {
		this.deployTime = deployTime;
	}


	public Long[] getDeployTimes() {
		return this.deployTimes;
	}
	public void setDeployTimes(Long[] deployTimes) {
		this.deployTimes = deployTimes;
	}


	public Long getStartDeployTime() {
		return this.startDeployTime;
	}
	public void setStartDeployTime(Long startDeployTime) {
		this.startDeployTime = startDeployTime;
	}


	public Long getEndDeployTime() {
		return this.endDeployTime;
	}
	public void setEndDeployTime(Long endDeployTime) {
		this.endDeployTime = endDeployTime;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


