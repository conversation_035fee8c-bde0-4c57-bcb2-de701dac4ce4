package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Getter;
import lombok.Setter;


@Comment("视图设计表[VC_DIAGRAM]")
public class CVcDiagram implements Condition {
	private static final long serialVersionUID = 1L;

	@Comment("发布状态，0：未发布，1：已发布")
	@Getter
	@Setter
	private Integer releaseStatus;

	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("视图名称[NAME] operate-Like[like]")
	private String name;


	@Comment("视图名称[NAME] operate-Equal[=]")
	private String nameEqual;


	@Comment("视图名称[NAME] operate-In[in]")
	private String[] names;


	@Comment("所属用户[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("所属用户[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("所属用户[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("所属用户[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("所属目录[DIR_ID] operate-Equal[=]")
	private Long dirId;


	@Comment("所属目录[DIR_ID] operate-In[in]")
	private Long[] dirIds;


	@Comment("所属目录[DIR_ID] operate-GTEqual[>=]")
	private Long startDirId;

	@Comment("所属目录[DIR_ID] operate-LTEqual[<=]")
	private Long endDirId;


	@Comment("目录类型[DIR_TYPE] operate-Equal[=]   ")
	private Integer dirType;


	@Comment("目录类型[DIR_TYPE] operate-In[in]    ")
	private Integer[] dirTypes;


	@Comment("目录类型[DIR_TYPE] operate-GTEqual[>=]    ")
	private Integer startDirType;

	@Comment("目录类型[DIR_TYPE] operate-LTEqual[<=]   ")
	private Integer endDirType;


	@Comment("视图类型[DIAGRAM_TYPE] operate-Equal[=]    视图类型:1=单图 2=组合视图 3=视图模版")
	private Integer diagramType;


	@Comment("视图类型[DIAGRAM_TYPE] operate-In[in]    视图类型:1=单图 2=组合视图 3=视图模版")
	private Integer[] diagramTypes;


	@Comment("视图类型[DIAGRAM_TYPE] operate-GTEqual[>=]    视图类型:1=单图 2=组合视图 3=视图模版")
	private Integer startDiagramType;

	@Comment("视图类型[DIAGRAM_TYPE] operate-LTEqual[<=]    视图类型:1=单图 2=组合视图 3=视图模版")
	private Integer endDiagramType;


	@Comment("视图描述[DIAGRAM_DESC] operate-Like[like]")
	private String diagramDesc;


	@Comment("视图SVG[DIAGRAM_SVG] operate-Like[like]")
	private String diagramSvg;


	@Comment("视图XML[DIAGRAM_XML] operate-Like[like]")
	private String diagramXml;


	@Comment("视图JSON[DIAGRAM_JSON] operate-Like[like]    视图json格式信息")
	private String diagramJson;


	@Comment("背景图[DIAGRAM_BG_IMG] operate-Like[like]")
	private String diagramBgImg;


	@Comment("背景样式[DIAGRAM_BG_CSS] operate-Like[like]")
	private String diagramBgCss;


	@Comment("视图图标_1[ICON_1] operate-Like[like]")
	private String icon1;


	@Comment("视图图标_2[ICON_2] operate-Like[like]")
	private String icon2;


	@Comment("视图图标_3[ICON_3] operate-Like[like]")
	private String icon3;


	@Comment("视图图标_4[ICON_4] operate-Like[like]")
	private String icon4;


	@Comment("视图图标_5[ICON_5] operate-Like[like]")
	private String icon5;


	@Comment("是否公开[IS_OPEN] operate-Equal[=]    是否公开:1=开放 0=私有")
	private Integer isOpen;


	@Comment("是否公开[IS_OPEN] operate-In[in]    是否公开:1=开放 0=私有")
	private Integer[] isOpens;


	@Comment("是否公开[IS_OPEN] operate-GTEqual[>=]    是否公开:1=开放 0=私有")
	private Integer startIsOpen;

	@Comment("是否公开[IS_OPEN] operate-LTEqual[<=]    是否公开:1=开放 0=私有")
	private Integer endIsOpen;


	@Comment("公开时间[OPEN_TIME] operate-Equal[=]")
	private Long openTime;


	@Comment("公开时间[OPEN_TIME] operate-In[in]")
	private Long[] openTimes;


	@Comment("公开时间[OPEN_TIME] operate-GTEqual[>=]")
	private Long startOpenTime;

	@Comment("公开时间[OPEN_TIME] operate-LTEqual[<=]")
	private Long endOpenTime;


	@Comment("数据驱动类型[DATA_UP_TYPE] operate-Equal[=]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
	private Integer dataUpType;


	@Comment("数据驱动类型[DATA_UP_TYPE] operate-In[in]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
	private Integer[] dataUpTypes;


	@Comment("数据驱动类型[DATA_UP_TYPE] operate-GTEqual[>=]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
	private Integer startDataUpType;

	@Comment("数据驱动类型[DATA_UP_TYPE] operate-LTEqual[<=]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
	private Integer endDataUpType;


	@Comment("视图状态[STATUS] operate-Equal[=]    视图状态:1=正常 0=回收站")
	private Integer status;


	@Comment("视图状态[STATUS] operate-In[in]    视图状态:1=正常 0=回收站")
	private Integer[] statuss;


	@Comment("视图状态[STATUS] operate-GTEqual[>=]    视图状态:1=正常 0=回收站")
	private Integer startStatus;

	@Comment("视图状态[STATUS] operate-LTEqual[<=]    视图状态:1=正常 0=回收站")
	private Integer endStatus;


	@Comment("CI3D坐标[CI_3D_POINT] operate-Like[like]")
	private String ci3dPoint;


	@Comment("CI3D坐标2[CI_3D_POINT2] operate-Like[like]")
	private String ci3dPoint2;


	@Comment("搜索字段[SEARCH_FIELD] operate-Like[like]    搜索字段:视图名称")
	private String searchField;


	@Comment("组合视图行数[COMB_ROWS] operate-Equal[=]    组合视图行数:组合视图字段")
	private Integer combRows;


	@Comment("组合视图行数[COMB_ROWS] operate-In[in]    组合视图行数:组合视图字段")
	private Integer[] combRowss;


	@Comment("组合视图行数[COMB_ROWS] operate-GTEqual[>=]    组合视图行数:组合视图字段")
	private Integer startCombRows;

	@Comment("组合视图行数[COMB_ROWS] operate-LTEqual[<=]    组合视图行数:组合视图字段")
	private Integer endCombRows;


	@Comment("组合视图列数[COMB_COLS] operate-Equal[=]    组合视图列数:组合视图字段")
	private Integer combCols;


	@Comment("组合视图列数[COMB_COLS] operate-In[in]    组合视图列数:组合视图字段")
	private Integer[] combColss;


	@Comment("组合视图列数[COMB_COLS] operate-GTEqual[>=]    组合视图列数:组合视图字段")
	private Integer startCombCols;

	@Comment("组合视图列数[COMB_COLS] operate-LTEqual[<=]    组合视图列数:组合视图字段")
	private Integer endCombCols;


	@Comment("查看次数[READ_COUNT] operate-Equal[=]")
	private Long readCount;


	@Comment("查看次数[READ_COUNT] operate-In[in]")
	private Long[] readCounts;


	@Comment("查看次数[READ_COUNT] operate-GTEqual[>=]")
	private Long startReadCount;

	@Comment("查看次数[READ_COUNT] operate-LTEqual[<=]")
	private Long endReadCount;


	@Comment("应用关联CI[APP_RLT_CI_CODE] operate-Like[like]    关联CI:针对应用墙点击CI弹出组合视图")
	private String appRltCiCode;


	@Comment("应用关联CI[APP_RLT_CI_CODE] operate-Equal[=]    关联CI:针对应用墙点击CI弹出组合视图")
	private String appRltCiCodeEqual;


	@Comment("应用关联CI[APP_RLT_CI_CODE] operate-In[in]    关联CI:针对应用墙点击CI弹出组合视图")
	private String[] appRltCiCodes;


	@Comment("参照版本id[REFER_VERSION_ID] operate-Equal[=]")
	private Long referVersionId;


	@Comment("参照版本id[REFER_VERSION_ID] operate-In[in]")
	private Long[] referVersionIds;


	@Comment("参照版本id[REFER_VERSION_ID] operate-GTEqual[>=]")
	private Long startReferVersionId;

	@Comment("参照版本id[REFER_VERSION_ID] operate-LTEqual[<=]")
	private Long endReferVersionId;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
	private Long endModifyTime;

	@Comment("主题文件夹[SUBJECT_ID]")
	private Long subjectId;

	public Long getSubjectId() {
		return subjectId;
	}

	public void setSubjectId(Long subjectId) {
		this.subjectId = subjectId;
	}



	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getName() {
		return this.name;
	}
	public void setName(String name) {
		this.name = name;
	}


	public String getNameEqual() {
		return this.nameEqual;
	}
	public void setNameEqual(String nameEqual) {
		this.nameEqual = nameEqual;
	}


	public String[] getNames() {
		return this.names;
	}
	public void setNames(String[] names) {
		this.names = names;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public Long getDirId() {
		return this.dirId;
	}
	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}


	public Long[] getDirIds() {
		return this.dirIds;
	}
	public void setDirIds(Long[] dirIds) {
		this.dirIds = dirIds;
	}


	public Long getStartDirId() {
		return this.startDirId;
	}
	public void setStartDirId(Long startDirId) {
		this.startDirId = startDirId;
	}


	public Long getEndDirId() {
		return this.endDirId;
	}
	public void setEndDirId(Long endDirId) {
		this.endDirId = endDirId;
	}


	public Integer getDirType() {
		return this.dirType;
	}
	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}


	public Integer[] getDirTypes() {
		return this.dirTypes;
	}
	public void setDirTypes(Integer[] dirTypes) {
		this.dirTypes = dirTypes;
	}


	public Integer getStartDirType() {
		return this.startDirType;
	}
	public void setStartDirType(Integer startDirType) {
		this.startDirType = startDirType;
	}


	public Integer getEndDirType() {
		return this.endDirType;
	}
	public void setEndDirType(Integer endDirType) {
		this.endDirType = endDirType;
	}


	public Integer getDiagramType() {
		return this.diagramType;
	}
	public void setDiagramType(Integer diagramType) {
		this.diagramType = diagramType;
	}


	public Integer[] getDiagramTypes() {
		return this.diagramTypes;
	}
	public void setDiagramTypes(Integer[] diagramTypes) {
		this.diagramTypes = diagramTypes;
	}


	public Integer getStartDiagramType() {
		return this.startDiagramType;
	}
	public void setStartDiagramType(Integer startDiagramType) {
		this.startDiagramType = startDiagramType;
	}


	public Integer getEndDiagramType() {
		return this.endDiagramType;
	}
	public void setEndDiagramType(Integer endDiagramType) {
		this.endDiagramType = endDiagramType;
	}


	public String getDiagramDesc() {
		return this.diagramDesc;
	}
	public void setDiagramDesc(String diagramDesc) {
		this.diagramDesc = diagramDesc;
	}


	public String getDiagramSvg() {
		return this.diagramSvg;
	}
	public void setDiagramSvg(String diagramSvg) {
		this.diagramSvg = diagramSvg;
	}


	public String getDiagramXml() {
		return this.diagramXml;
	}
	public void setDiagramXml(String diagramXml) {
		this.diagramXml = diagramXml;
	}


	public String getDiagramJson() {
		return this.diagramJson;
	}
	public void setDiagramJson(String diagramJson) {
		this.diagramJson = diagramJson;
	}


	public String getDiagramBgImg() {
		return this.diagramBgImg;
	}
	public void setDiagramBgImg(String diagramBgImg) {
		this.diagramBgImg = diagramBgImg;
	}


	public String getDiagramBgCss() {
		return this.diagramBgCss;
	}
	public void setDiagramBgCss(String diagramBgCss) {
		this.diagramBgCss = diagramBgCss;
	}


	public String getIcon1() {
		return this.icon1;
	}
	public void setIcon1(String icon1) {
		this.icon1 = icon1;
	}


	public String getIcon2() {
		return this.icon2;
	}
	public void setIcon2(String icon2) {
		this.icon2 = icon2;
	}


	public String getIcon3() {
		return this.icon3;
	}
	public void setIcon3(String icon3) {
		this.icon3 = icon3;
	}


	public String getIcon4() {
		return this.icon4;
	}
	public void setIcon4(String icon4) {
		this.icon4 = icon4;
	}


	public String getIcon5() {
		return this.icon5;
	}
	public void setIcon5(String icon5) {
		this.icon5 = icon5;
	}


	public Integer getIsOpen() {
		return this.isOpen;
	}
	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}


	public Integer[] getIsOpens() {
		return this.isOpens;
	}
	public void setIsOpens(Integer[] isOpens) {
		this.isOpens = isOpens;
	}


	public Integer getStartIsOpen() {
		return this.startIsOpen;
	}
	public void setStartIsOpen(Integer startIsOpen) {
		this.startIsOpen = startIsOpen;
	}


	public Integer getEndIsOpen() {
		return this.endIsOpen;
	}
	public void setEndIsOpen(Integer endIsOpen) {
		this.endIsOpen = endIsOpen;
	}


	public Long getOpenTime() {
		return this.openTime;
	}
	public void setOpenTime(Long openTime) {
		this.openTime = openTime;
	}


	public Long[] getOpenTimes() {
		return this.openTimes;
	}
	public void setOpenTimes(Long[] openTimes) {
		this.openTimes = openTimes;
	}


	public Long getStartOpenTime() {
		return this.startOpenTime;
	}
	public void setStartOpenTime(Long startOpenTime) {
		this.startOpenTime = startOpenTime;
	}


	public Long getEndOpenTime() {
		return this.endOpenTime;
	}
	public void setEndOpenTime(Long endOpenTime) {
		this.endOpenTime = endOpenTime;
	}


	public Integer getDataUpType() {
		return this.dataUpType;
	}
	public void setDataUpType(Integer dataUpType) {
		this.dataUpType = dataUpType;
	}


	public Integer[] getDataUpTypes() {
		return this.dataUpTypes;
	}
	public void setDataUpTypes(Integer[] dataUpTypes) {
		this.dataUpTypes = dataUpTypes;
	}


	public Integer getStartDataUpType() {
		return this.startDataUpType;
	}
	public void setStartDataUpType(Integer startDataUpType) {
		this.startDataUpType = startDataUpType;
	}


	public Integer getEndDataUpType() {
		return this.endDataUpType;
	}
	public void setEndDataUpType(Integer endDataUpType) {
		this.endDataUpType = endDataUpType;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Integer[] getStatuss() {
		return this.statuss;
	}
	public void setStatuss(Integer[] statuss) {
		this.statuss = statuss;
	}


	public Integer getStartStatus() {
		return this.startStatus;
	}
	public void setStartStatus(Integer startStatus) {
		this.startStatus = startStatus;
	}


	public Integer getEndStatus() {
		return this.endStatus;
	}
	public void setEndStatus(Integer endStatus) {
		this.endStatus = endStatus;
	}


	public String getCi3dPoint() {
		return this.ci3dPoint;
	}
	public void setCi3dPoint(String ci3dPoint) {
		this.ci3dPoint = ci3dPoint;
	}


	public String getCi3dPoint2() {
		return this.ci3dPoint2;
	}
	public void setCi3dPoint2(String ci3dPoint2) {
		this.ci3dPoint2 = ci3dPoint2;
	}


	public String getSearchField() {
		return this.searchField;
	}
	public void setSearchField(String searchField) {
		this.searchField = searchField;
	}


	public Integer getCombRows() {
		return this.combRows;
	}
	public void setCombRows(Integer combRows) {
		this.combRows = combRows;
	}


	public Integer[] getCombRowss() {
		return this.combRowss;
	}
	public void setCombRowss(Integer[] combRowss) {
		this.combRowss = combRowss;
	}


	public Integer getStartCombRows() {
		return this.startCombRows;
	}
	public void setStartCombRows(Integer startCombRows) {
		this.startCombRows = startCombRows;
	}


	public Integer getEndCombRows() {
		return this.endCombRows;
	}
	public void setEndCombRows(Integer endCombRows) {
		this.endCombRows = endCombRows;
	}


	public Integer getCombCols() {
		return this.combCols;
	}
	public void setCombCols(Integer combCols) {
		this.combCols = combCols;
	}


	public Integer[] getCombColss() {
		return this.combColss;
	}
	public void setCombColss(Integer[] combColss) {
		this.combColss = combColss;
	}


	public Integer getStartCombCols() {
		return this.startCombCols;
	}
	public void setStartCombCols(Integer startCombCols) {
		this.startCombCols = startCombCols;
	}


	public Integer getEndCombCols() {
		return this.endCombCols;
	}
	public void setEndCombCols(Integer endCombCols) {
		this.endCombCols = endCombCols;
	}


	public Long getReadCount() {
		return this.readCount;
	}
	public void setReadCount(Long readCount) {
		this.readCount = readCount;
	}


	public Long[] getReadCounts() {
		return this.readCounts;
	}
	public void setReadCounts(Long[] readCounts) {
		this.readCounts = readCounts;
	}


	public Long getStartReadCount() {
		return this.startReadCount;
	}
	public void setStartReadCount(Long startReadCount) {
		this.startReadCount = startReadCount;
	}


	public Long getEndReadCount() {
		return this.endReadCount;
	}
	public void setEndReadCount(Long endReadCount) {
		this.endReadCount = endReadCount;
	}


	public String getAppRltCiCode() {
		return this.appRltCiCode;
	}
	public void setAppRltCiCode(String appRltCiCode) {
		this.appRltCiCode = appRltCiCode;
	}


	public String getAppRltCiCodeEqual() {
		return this.appRltCiCodeEqual;
	}
	public void setAppRltCiCodeEqual(String appRltCiCodeEqual) {
		this.appRltCiCodeEqual = appRltCiCodeEqual;
	}


	public String[] getAppRltCiCodes() {
		return this.appRltCiCodes;
	}
	public void setAppRltCiCodes(String[] appRltCiCodes) {
		this.appRltCiCodes = appRltCiCodes;
	}


	public Long getReferVersionId() {
		return this.referVersionId;
	}
	public void setReferVersionId(Long referVersionId) {
		this.referVersionId = referVersionId;
	}


	public Long[] getReferVersionIds() {
		return this.referVersionIds;
	}
	public void setReferVersionIds(Long[] referVersionIds) {
		this.referVersionIds = referVersionIds;
	}


	public Long getStartReferVersionId() {
		return this.startReferVersionId;
	}
	public void setStartReferVersionId(Long startReferVersionId) {
		this.startReferVersionId = startReferVersionId;
	}


	public Long getEndReferVersionId() {
		return this.endReferVersionId;
	}
	public void setEndReferVersionId(Long endReferVersionId) {
		this.endReferVersionId = endReferVersionId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


