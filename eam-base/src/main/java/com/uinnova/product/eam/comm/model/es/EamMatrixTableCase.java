package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 矩阵格数据
 * <AUTHOR>
 */
@Data
public class EamMatrixTableCase implements Serializable {

    @Comment("字段定义id")
    private Long id;

    @Comment("ciCode")
    private String code;

    @Comment("字段值")
    private String value;

    @Comment("分类id")
    private Long classId;

    public EamMatrixTableCase(Long id, String code, String value, Long classId) {
        this.id = id;
        this.code = code;
        this.value = value;
        this.classId = classId;
    }
}
