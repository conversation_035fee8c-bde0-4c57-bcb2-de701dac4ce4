package com.uinnova.product.eam.base.diagram.model;

/**
 * 模板目录与视图关联信息
 */
public class TemDiaRelationQuery {

    private Long diagramId;

    private Long templateDirId;

    private Long[] templateDirIds;

    private Long[] diagramIds;

    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(Long diagramId) {
        this.diagramId = diagramId;
    }

    public Long getTemplateDirId() {
        return templateDirId;
    }

    public void setTemplateDirId(Long templateDirId) {
        this.templateDirId = templateDirId;
    }

    public Long[] getDiagramIds() {
        return diagramIds;
    }

    public void setDiagramIds(Long[] diagramIds) {
        this.diagramIds = diagramIds;
    }

    public Long[] getTemplateDirIds() {
        return templateDirIds;
    }

    public void setTemplateDirIds(Long[] templateDirIds) {
        this.templateDirIds = templateDirIds;
    }
}
