package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 版本标签表实体
 * <AUTHOR>
 */
@Data
public class EamVersionTag {
    @Comment("主键id")
    private Long id;

    @Comment("domainId")
    private Long domainId;

    @Comment("关联的业务分支id")
    private Long branchId;

    @Comment("版本名称")
    private String branchName;

    @Comment("版本号")
    private String tagName;

    @Comment("是否删除：0删除,1正常")
    private Integer dataStatus;

    @Comment("版本说明")
    private String message;

    @Comment("打标签日期")
    private Long tagTime;

    @Comment("创建人")
    private String creator;

    @Comment("版本日期")
    private Long createTime;

    @Comment("修改人")
    private String modifier;

    @Comment("修改日期")
    private Long modifyTime;
}
