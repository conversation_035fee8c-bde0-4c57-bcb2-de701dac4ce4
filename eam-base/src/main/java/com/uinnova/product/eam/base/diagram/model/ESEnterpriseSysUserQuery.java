package com.uinnova.product.eam.base.diagram.model;


import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * @Classname 系统用户，集成企业内部产品用户ID，mmd_id
 * @Description 该类用来查询各产品ID和系统用户关联关系
 * <AUTHOR>
 * @Date 2021-10-9-15:10
 */
@Data
@Comment("系统用户企业内部产品用户ID，mmd_id 查询类")
public class ESEnterpriseSysUserQuery {

    @Comment("key")
    private Long id;

    @Comment("userId")
    private Long userId;

    // thingjs唯一标识mmd_id
    @Comment("mmdId")
    private Long mmdId;

    @Comment("nndId")
    private Long nndId;

    @Comment("mndId")
    private Long mndId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMmdId() {
        return mmdId;
    }

    public void setMmdId(Long mmdId) {
        this.mmdId = mmdId;
    }

}
