package com.uinnova.product.eam.comm.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

@Comment("视图CI关系表[VC_DIAGRAM_CI]")
public class VcDiagramCi implements EntityBean {
	private static final long serialVersionUID = 1L;

	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;

	@Comment("CI_ID[CI_ID]")
	private Long ciId;

	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;

	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;

	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;

	public Long getDiagramId() {
		return this.diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public Long getCiId() {
		return this.ciId;
	}

	public void setCiId(Long ciId) {
		this.ciId = ciId;
	}

	public Long getDomainId() {
		return this.domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long getModifyTime() {
		return this.modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public Long getId() {
		// TODO Auto-generated method stub
		return null;
	}

	public void setId(Long id) {
		// TODO Auto-generated method stub

	}

}
