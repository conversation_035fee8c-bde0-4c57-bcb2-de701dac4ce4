package com.uinnova.product.eam.comm.model.es;

import com.uino.bean.permission.base.TypeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class AssetWarehouseDir {

    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value="id",example = "123")
    private Long id;

    /** 模块名称 */
    @ApiModelProperty(value="名称", example = "名称", required = true)
    private String name;

    /** 父节点 */
    @ApiModelProperty(value="父节点", required = true)
    private Long parentId;

    /** 显示排序 */
    @ApiModelProperty(value="显示排序")
    private Integer orderNo;

    /** 链接地址 */
    @ApiModelProperty(value="链接地址")
    private String moduleUrl;

    @ApiModelProperty(value="模块类型", example = "0:菜单, 1:按钮 2：资产仓库小组，3：资产仓库目录", required = true)
    private Integer moduleType = 2;

    @ApiModelProperty("角色id集合")
    private List<Long> roleIdList;

    @ApiModelProperty(value = "存放资产类型-枚举：1=视图；2=模型；3=方案；4=矩阵")
    private List<Integer> assetType;

    @ApiModelProperty(value = "视图类型")
    private List<TypeVo> diagramList;

    @ApiModelProperty(value = "模型类型")
    private List<TypeVo> modelList;

    @ApiModelProperty(value = "方案类型")
    private List<TypeVo> schemeList;

    @ApiModelProperty(value = "矩阵类型")
    private List<TypeVo> matrixList;

    /** 所属域 */
    @ApiModelProperty(value="所属域id",example = "123")
    private Long domainId;

    /** 创建人 */
    @ApiModelProperty(value="创建人",example = "mike")
    private String creator;

    /** 修改人 */
    @ApiModelProperty(value="修改人",example = "mike")
    private String modifier;

    /** 创建时间 */
    @ApiModelProperty(value="创建时间")
    private Long createTime;

    /** 修改时间 */
    @ApiModelProperty(value="修改时间")
    private Long modifyTime;

    public AssetWarehouseDir() {
        this.assetType = Collections.emptyList();
        this.diagramList = Collections.emptyList();
        this.modelList = Collections.emptyList();
        this.schemeList = Collections.emptyList();
        this.matrixList = Collections.emptyList();
    }
}
