package com.uinnova.product.eam.comm.bean;

import lombok.Data;

import java.util.List;

/**
 * 资产属性标签分组
 *
 * <AUTHOR>
 */
@Data
public class CcCiAttrDefTapGroupConfVO {
    private String name;
    private Boolean visible;
    /**
     * 配置类型
     * 资产属性：1
     * 影响关系：2
     * 架构视图：3
     * 架构方案：4
     * 架构决策：5
     * 变更历史：6
     */
    private Integer tabType;
    private Integer type;
    private List<CcCiAttrDefGroupConfVO> tapAttr;
    private List<Long> productTypeList;
}
