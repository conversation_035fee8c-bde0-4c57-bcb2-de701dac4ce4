package com.uinnova.product.eam.comm.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 矩阵表格数据接收响应实体
 * <AUTHOR>
 */
@Data
public class EamMatrixTableSaveVO {

    @Comment("矩阵表格id")
    private Long id;

    @Comment("矩阵表格名称")
    private String name;

    @Comment("矩阵制品Id")
    private Long matrixId;

    @Comment("用户标识")
    private String ownerCode;

    @Comment("矩阵表头")
    private List<EamMatrixHeaderVO> header = new ArrayList<>();

    @Comment("矩阵表格数据(接收)")
    private List<EamMatrixInstanceData> table;

    @Comment("页数")
    private long pageNum;

    @Comment("条数")
    private long pageSize;

    @Comment("总条数")
    private long totalRows;

    @Comment("总页数")
    private long totalPages;

    public EamMatrixTableSaveVO(Long id, String name, Long matrixId){
        this.id = id;
        this.name = name;
        this.matrixId = matrixId;
    }

}
