package com.uinnova.product.eam.base.diagram.model;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Classname
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-07-27-18:01
 */
@Data
public class ESDiagramNode implements EntityBean {

    @JSONField(name = "id")
    private Long id;

    @J<PERSON>NField(name = "key")
    private String key;

    @JSONField(name = "sheetId")
    private String sheetId;

    //    @JSONField(name = "diagramId")
    @JsonIgnore
    private Long diagramId;

    @JSONField(name = "nodeJson")
    private String nodeJson;

    @JSONField(name = "ciCode")
    private String ciCode;

    //0:隐藏态;1:显示态（根据ciCode判断是否是数据态）
    private Integer visible = 1;

    @JSONField(name = "transFlag")
    private Integer transFlag;

    @JSONField(name = "createTime")
    private Long createTime;

    @JSONField(name = "modifyTime")
    private Long modifyTime;

    @Comment("视图ID加密字段")
//    @JsonIgnore
    @JsonProperty("diagramId")
    private String dEnergy;

    public String getdEnergy() {
        return SecureUtil.md5(String.valueOf(diagramId)).substring(8,24);
    }

    @JSONField(name = "reserved1")
    private Long reserved1;

    @JSONField(name = "reserved2")
    private Long reserved2;

    @JSONField(name = "reserved3")
    private Long reserved3;

    @JSONField(name = "activeSortNum")
    private Long activeSortNum;

    @Comment("CI版本号")
    @JSONField(name = "version")
    private Long version;
}
