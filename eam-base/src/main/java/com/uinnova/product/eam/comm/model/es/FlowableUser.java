package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Comment("流程人员审批同意表[UINO_FLOWABLE_USER]")
public class FlowableUser {

    @Comment("人员审批主键")
    private Long id;
    @Comment("流程实例ID")
    private String processInstanceId;
    @Comment("流程实例任务key")
    private String processTaskKey;
    @Comment("同意用户的loginCode")
    private String agreeUserLoginCode;
}
