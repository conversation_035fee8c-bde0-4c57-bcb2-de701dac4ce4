package com.uinnova.product.eam.base.diagram.mix.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.mix.enums.DiagramQ;
import com.uino.bean.permission.query.SearchBase;
import lombok.Data;

@Data
@Comment("我的查询视图参数")
public class TingJsDiagramCdt extends SearchBase {

    private static final long serialVersionUID = 1L;

    //TingJs平台唯一用户标识： 么么哒ID
    @Comment("mmdId")
    private Long mmdId;

	@Comment("父文件夹ID")
	private Long dirId;

	@Comment("目录类型")
	private Integer dirType;

	@Comment("模糊查询字段信息")
	private String like;

	@Comment("筛选类型[0:初始化,1:单图,2:组合视图,3:文件夹,4:我的模版]")
	private Integer type;

	@Comment("查询类型：0:名称,1:作者,2:标签,3:CI")
	private Integer queryType;

	@Comment("tagId")
	private Long tagId;

	@Comment("视图id")
	private Long diagramId;

	@Comment("需要查询的视图附加信息")
	private DiagramQ[] diagramQs;

	@Comment("是否是我协作的查询")
	private Integer isConcert;

	@Comment("按照字段排序")
	private String orders;

	@Comment("是否带视图权限")
	private boolean auth=false;

	private int pageSize = 3000;

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
}
