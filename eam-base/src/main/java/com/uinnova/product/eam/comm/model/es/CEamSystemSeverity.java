package com.uinnova.product.eam.comm.model.es;


import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("操作计数表[UINO_EAM_SYSTEM_SEVERITY]")
public class CEamSystemSeverity implements Condition {

	private Long id;

	private Long questionId;

	private String systemName;

	private String systemCiCode;

	private String answer;

	private Integer score;

	private String severity;

	private String operator;

	private Long domainId;

	private Long createTime;

	private Long modifyTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getSystemCiCode() {
		return systemCiCode;
	}

	public void setSystemCiCode(String systemCiCode) {
		this.systemCiCode = systemCiCode;
	}

	public String getAnswer() {
		return answer;
	}

	public void setAnswer(String answer) {
		this.answer = answer;
	}

	public Integer getScore() {
		return score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}

	public String getSeverity() {
		return severity;
	}

	public void setSeverity(String severity) {
		this.severity = severity;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}
}


