package com.uinnova.product.eam.web.mix.diagram.v2.web;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;
import com.uinnova.product.eam.model.diagram.EamHttpRequestParam;
import com.uinnova.product.eam.service.diagram.IExternalSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;


/**
 * @description:
 * @author: sunshaoyu
 * @time: 2021/9/26
 */
@RestController
@RequestMapping("/eam/externalData")
public class ExternalDataMvc {

    @Autowired
    private IExternalSvc externalSvc;

    @Value("${uino.diagram.visio.external.url:http://**************/restvisio/visio/visioToGojsJson}")
    private String visioExternalUrl;

    @RequestMapping(value = "/requestByUrl")
    @ModDesc(desc = "通过URL请求外部数据，前端给定url", pDesc = "请求连接及参数信息",pType= EamHttpRequestParam.class, rDesc = "信息", rType = Object.class)
    public RemoteResult requestByUrl(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String body, @RequestParam(required = false) Map<String,String> map, @RequestParam(value = "file", required = false) MultipartFile files) {
        EamHttpRequestParam param = new EamHttpRequestParam();
        if (body != null) {
            param = JSON.toObject(body, EamHttpRequestParam.class);
        } else if (map != null) {
            String form = JSON.toString(map);
            param = JSON.toObject(form, EamHttpRequestParam.class);
            if (files!=null) {
                param.setFiles(files);
            }
        }
        param.setUrl(visioExternalUrl);
        Object result = externalSvc.requestByUrl(param);
        return new RemoteResult(result);
    }
}
