package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.bean.ApplicationSysFigureRlt;
import com.uinnova.product.eam.service.sys.IEamApplicationSysFigureRltSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 应用系统的图关系controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/figure")
public class EamApplicationSysFigureRltMvc {

    @Autowired
    private IEamApplicationSysFigureRltSvc applicationSysFigureRltSvc;

    /**
     * 根据应用系统图关系实体类保存或更新
     *
     * @param applicationSysFigureRlt 保存或更新应用系统图关系
     * @return 应用系统图关系id
     */
    @PostMapping("/addAndUpdateFigureRlt")
    public RemoteResult addAndUpdateFigureRltById(@RequestBody ApplicationSysFigureRlt applicationSysFigureRlt) {
        //判断入参
        BinaryUtils.checkEmpty(applicationSysFigureRlt.getCiCode(), "ciCode");
        return new RemoteResult(applicationSysFigureRltSvc.addFigureRlt(applicationSysFigureRlt));
    }

    /**
     * 根据CiCode查询应用系统的图关系信息
     *
     * @param ciCode 应用系统id
     * @return 应用系统图关系实体类
     */
    @GetMapping("/getFigureRltByCiCode")
    public RemoteResult getFigureRltById(String ciCode) {
        //判断入参
        BinaryUtils.checkEmpty(ciCode, "ciCode");
        return new RemoteResult(applicationSysFigureRltSvc.getByCiCode(ciCode));
    }
}
