package com.uinnova.product.eam.web.flow.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.FlowOperationStatisticsDto;
import com.uinnova.product.eam.service.ProcessOperationService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 流程管理/流程运行情况服务层
 *
 * <AUTHOR>
 * @since 2024/12/30
 */
@RestController
@RequestMapping("/flowManager/processOperation")
public class ProcessOperationController {

    @Resource
    private ProcessOperationService processOperationService;

    /**
     * 获取流程运行状态统计
     *
     * @param dto
     * @return
     */
    @PostMapping("/processRuningStatus")
    public RemoteResult getProcessOperationStatus(@RequestBody FlowOperationStatisticsDto dto) {
        BinaryUtils.checkEmpty(dto.getCiCode(), "流程标识");
        return new RemoteResult(processOperationService.getProcessOperationStatus(dto));
    }

    /**
     * 流程办结数量统计
     *
     * @param dto
     * @return
     */
    @PostMapping("/processFinishCount")
    public RemoteResult getProcessFinishCount(@RequestBody FlowOperationStatisticsDto dto) {
        BinaryUtils.checkEmpty(dto.getCiCode(), "流程标识");
        return new RemoteResult(processOperationService.getProcessFinishCount(dto));
    }

    /**
     * 活动运行时长
     *
     * @param dto
     * @return
     */
    @PostMapping("/activityRuningDuration")
    public RemoteResult getActivityRuningDuration(@RequestBody FlowOperationStatisticsDto dto) {
        BinaryUtils.checkEmpty(dto.getCiCode(), "流程标识");
        return new RemoteResult(processOperationService.getActivityRuningDuration(dto));
    }
}
