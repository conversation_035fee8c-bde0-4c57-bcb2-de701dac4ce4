package com.uinnova.product.eam.web.cj;

import cn.hutool.core.lang.Assert;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import com.uinnova.product.eam.model.cj.domain.DeliverableTemplate;
import com.uinnova.product.eam.model.cj.domain.TemplateType;
import com.uinnova.product.eam.model.cj.vo.*;
import com.uinnova.product.eam.model.dmv.ClassDefinitionVO;
import com.uinnova.product.eam.service.cj.service.DeliverableTemplateService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.web.auth.VerifyAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Controller
@RequestMapping("/cj/deliverableTemplate")
public class DeliverableTemplateController {

    public static final String SYS_MODULE_SIGN = "交付物模版管理";
    @Autowired
    private DeliverableTemplateService deliverableTemplateService;

    @Autowired
    private VerifyAuthUtil verifyAuth;

    /**
     * 交付物模板基本信息创建或更新
     * @param request
     * @param response
     * @param cdt
     */
    @PostMapping("/saveOrUpdateDlvrTemplate")
    public void saveOrUpdateDlvrTemplate(HttpServletRequest request, HttpServletResponse response,@Valid @RequestBody DeliverableTemplate cdt) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Long id = deliverableTemplateService.saveOrUpdateDlvrTemplate(cdt);
        ControllerUtils.returnJson(request,response,id);
    }

    /**
     * 交付物模板基本信息查询List
     * @param request
     * @param response
     * @param queryBean
     */
    @PostMapping("/getDlvrTemplateList")
    public void getDlvrTemplateList(HttpServletRequest request, HttpServletResponse response, @RequestBody DlvrTemplateReq queryBean) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Page<DlvrTemplateVO> result = deliverableTemplateService.getDlvrTemplateList(queryBean);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 查询已发布模板，不分页查询
     */
    @PostMapping("/getReleaseTemplateList")
    public void getReleaseTemplateList(HttpServletRequest request, HttpServletResponse response,@RequestBody DlvrTemplateReq dlvrTemplateReq) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        List<DlvrTemplateDTO> result = deliverableTemplateService.getReleaseTemplateList(dlvrTemplateReq);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 根据主键查询交付物模板基本信息
     */
    @GetMapping("/getDlvrTemplateById")
    public void getDlvrTemplateById(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "id",required = true)Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        DlvrTemplateVO result = deliverableTemplateService.getDlvrTemplateById(id);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 添加或更新交付物模板类型
     * @param request
     * @param response
     * @param templateTypes
     */
    @PostMapping("/createOrUpdateTemplateType")
    public void createOrUpdateTemplateType(HttpServletRequest request, HttpServletResponse response, @RequestBody List<TemplateType> templateTypes) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Set<String> typeNameSet = templateTypes.stream().map(TemplateType::getTypeName).collect(Collectors.toSet());
        if (typeNameSet.size() != templateTypes.size()) {
            throw new BusinessException(ResultCodeEnum.DLVR_TYPE_REPEAT);
        }
        Long id = deliverableTemplateService.createOrUpdateTemplateType(templateTypes);
        ControllerUtils.returnJson(request,response,id);
    }

    /**
     * 查询交付物所有类型
     * @param request
     * @param response
     */
    @GetMapping("/getListTemplateType")
    public void getListTemplateType(HttpServletRequest request, HttpServletResponse response) {
        List<TemplateType> result = deliverableTemplateService.getListTemplateType();
        ControllerUtils.returnJson(request,response,result);
    }


    /**
     * 查询交付物类型，如果不被方案设计引用的不显示
     * @param request
     * @param response
     */
    @GetMapping("/getListShowTemplateType")
    public void getListShowTemplateType(HttpServletRequest request, HttpServletResponse response) {
        List<TemplateType> result = deliverableTemplateService.getListShowTemplateType();
        ControllerUtils.returnJson(request,response,result);
    }



    /**
     * 删除模板类型逻辑删除
     */
    @GetMapping("/deleteTemplateType")
    public void deleteTemplateType(HttpServletRequest request, HttpServletResponse response,@RequestParam(name = "id")Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Boolean result = deliverableTemplateService.deleteTemplateType(id);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 校验交付物模板是否可以删除
     */
    @GetMapping("/validDelete")
    public void validDelete(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "id",required = true) Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Boolean result = deliverableTemplateService.validDelete(id);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 删除草稿状态的交付物模板
     */
    @GetMapping("/deleteDraftTemplate")
    public void deleteDraftTemplate(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "templateId") Long templateId) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Boolean result = deliverableTemplateService.deleteDraftTemplate(templateId);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 交付物模板复制
     * @param request
     * @param response
     * @param templateId
     */
    @GetMapping("/copyTemplateInfoById")
    public void copyTemplateInfoById(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "templateId") Long templateId,@RequestParam(name = "templateName",required = false) String templateName) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Assert.isTrue(templateId != null,"复制的交付物模板不能为空");
        if (templateName!=null && templateName.length() > 50) {
            throw new BusinessException("交付物模板名称过长,修改模板名称完成复制",templateName);
        }
        Boolean result = deliverableTemplateService.copyTemplateInfoById(templateId,templateName);
        ControllerUtils.returnJson(request,response,result);
    }

    @PostMapping("/getPublishTemplateInfos")
    public void highPerformance(HttpServletRequest request, HttpServletResponse response) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        List<DeliverableTemplate> result = deliverableTemplateService.getPublishTemplateInfos();
        ControllerUtils.returnJson(request,response,result);
    }

    @GetMapping("/getTemplateChapterList")
    public void getTemplateChapterList(HttpServletRequest request, HttpServletResponse response , @RequestParam Long templateId) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        List<PlanTemplateChapterTreeVo> result = deliverableTemplateService.getTemplateChapterList(templateId);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 通过模板关联的系统
     * 筛选获取表格所有分类
     * @return
     */
    @GetMapping("/findTypeList")
    @ResponseBody
    public ResultMsg findTableContentTypeList(@RequestParam("templateId") Long templateId) {
        List<TableContentTypeVo> typeList = deliverableTemplateService.findTableContentTypeList(templateId);
        return new ResultMsg(typeList);
    }

    /**
     * 通过模板关联的系统
     * 筛选ci分类及属性
     * @return
     */
    @GetMapping("/findClassDefinitionList")
    @ResponseBody
    public ResultMsg findClassDefinitionList(@RequestParam("templateId") Long templateId) {
        List<ClassDefinitionVO> typeList = deliverableTemplateService.findClassDefinitionList(templateId);
        return new ResultMsg(typeList);
    }
}
