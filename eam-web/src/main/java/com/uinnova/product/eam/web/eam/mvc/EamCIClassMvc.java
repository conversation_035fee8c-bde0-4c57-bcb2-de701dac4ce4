package com.uinnova.product.eam.web.eam.mvc;

import cn.hutool.core.lang.hash.Hash;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.model.dto.CiClassDto;
import com.uinnova.product.eam.model.dto.CiCodeDto;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.CIClassInfoDto;
import com.uino.bean.cmdb.business.ClassAttrDefVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/eam/ciClass")
@MvcDesc(author = "zmj", desc = "对象管理")
public class EamCIClassMvc {
    @Autowired
    private IEamCIClassApiSvc ciClassApiSvc;

    @Value("${http.resource.space}")
    private String rsmSlaveRoot;

    @PostMapping(value = "/saveOrUpdate")
    @ModDesc(desc = "保存对象分类信息", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public void saveOrUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody CIClassInfoDto dto) {
        List<ESCIAttrDefInfo> defs = dto.getAttrDefs().stream().filter(each -> (!BinaryUtils.isEmpty(each.getId()) && each.getId().longValue() > 0) || BinaryUtils.isEmpty(each.getId())).collect(Collectors.toList());
        ESCIClassInfo record = EamUtil.copy(dto.getCiClass(), ESCIClassInfo.class);
        record.setAttrDefs(defs);
        Long id = ciClassApiSvc.saveOrUpdateESCIClass(record);
        ControllerUtils.returnJson(request, response, id);
    }

    @PostMapping(value = "/saveOrUpdateAttrDef")
    @ModDesc(desc = "保存对象分类信息", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public RemoteResult saveOrUpdateAttrDef(@RequestBody ClassAttrDefVo dto) {

        ESCIClassInfo esciClassInfo = ciClassApiSvc.queryClassById(dto.getClassId());
        List<ESCIAttrDefInfo> attrDefs = esciClassInfo.getAttrDefs();
        Map<Long, ESCIAttrDefInfo> esciAttrDefInfoHashMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(attrDefs)) {
            for (ESCIAttrDefInfo attrDef : attrDefs) {
                esciAttrDefInfoHashMap.put(attrDef.getId(), attrDef);
            }
        }
        ESCIAttrDefInfo attrDefInfo = dto.getAttrDefInfo();
        if (attrDefInfo.getId() != null) {
            //修改属性
            ESCIAttrDefInfo esciAttrDefInfo = esciAttrDefInfoHashMap.get(attrDefInfo.getId());
            if (esciAttrDefInfo != null) {
                BeanUtils.copyProperties(attrDefInfo, esciAttrDefInfo);
            }
        } else {
            //新增属性
            attrDefs.add(attrDefInfo);
        }
        ciClassApiSvc.saveOrUpdateESCIClass(esciClassInfo);
        return new RemoteResult(attrDefInfo);
    }


    @PostMapping(value = "/getEnCodeNum")
    @ModDesc(desc = "查询分类信息数据的编码", pDesc = "查询分类信息数据的编码", pType = String.class, rDesc = "编码前缀", rType = String.class)
    public void getEnCodeNum(HttpServletRequest request, HttpServletResponse response, @RequestBody CiCodeDto ciCodeDto) {
        String enCodeNum = ciClassApiSvc.getEnCodeNum(ciCodeDto);
        ControllerUtils.returnJson(request, response, enCodeNum);
    }

    @PostMapping(value = "/getIntegerEnCodeNum")
    @ModDesc(desc = "查询分类信息数据的编码", pDesc = "查询分类信息数据的编码", pType = String.class, rDesc = "编码前缀", rType = String.class)
    public void getIntegerEnCodeNum(HttpServletRequest request, HttpServletResponse response, @RequestBody CiCodeDto ciCodeDto) {
        Long enCodeNum = ciClassApiSvc.getIntegerEnCodeNum(ciCodeDto);
        ControllerUtils.returnJson(request, response, enCodeNum);
    }

    @PostMapping(value = "/removeById")
    public void removeById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long id) {
        Integer result = ciClassApiSvc.removeCIClassById(id);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping(value = "/copyCIClassByIds")
    @ModDesc(desc = "复制对象分类信息", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public void copyCIClassByIds(HttpServletRequest request, HttpServletResponse response, @RequestBody CiClassDto cdt) {
        Map<Long, ESCIClassInfo> res = ciClassApiSvc.copyCIClassByIds(cdt.getIds());
        ControllerUtils.returnJson(request, response, res);
    }

    @GetMapping(value = "/getCIClassByCode")
    @ModDesc(desc = "根据codes查询对象分类信息", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public void getCIClassByCode(HttpServletRequest request, HttpServletResponse response, String classCode) {
        CcCiClassInfo res = ciClassApiSvc.getCIClassByCodes(classCode);
        ControllerUtils.returnJson(request, response, res);
    }

    @GetMapping(value = "/queryCIClassByCode")
    @ModDesc(desc = "根据codes查询对象分类信息(查询属性修改后映射信息)", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public void queryCIClassByCode(HttpServletRequest request, HttpServletResponse response, @RequestParam String classCode) {
        BinaryUtils.checkEmpty(classCode,"分类标识");
        CcCiClassInfo res = ciClassApiSvc.queryClassAndAttrMappingByCode(classCode);
        ControllerUtils.returnJson(request, response, res);
    }

    @GetMapping(value = "/queryCIClassByCodeV2")
    @ModDesc(desc = "根据codes查询对象分类信息(查询属性修改后映射信息)", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public void queryCIClassByCodeV2(HttpServletRequest request, HttpServletResponse response, @RequestParam String classCode) {
        BinaryUtils.checkEmpty(classCode,"分类标识");
        ESCIClassInfo res = ciClassApiSvc.getCIClassByCode(classCode);
        ControllerUtils.returnJson(request, response, res);
    }

    @GetMapping(value = "/getCIClassList")
    @ModDesc(desc = "获取分类信息集合（分类名称搜索）", pDesc = "搜索词", pType = List.class, rDesc = "分类信息集合", rType = List.class)
    public RemoteResult getCIClassList(@RequestParam(required = false) String word) {
        List<ESCIClassInfo> res = ciClassApiSvc.getCIClassList(word);
        return new RemoteResult(res);
    }

    @GetMapping(value = "/filterByVisualModel")
    @ModDesc(desc = "根据元模型过滤分类", pDesc = "对象分类id", pType = Long.class, rDesc = "分类信息", rType = Object.class)
    public RemoteResult filterByVisualModel(@RequestParam Long id, Long orgId) {
        List<ESCIClassInfo> res = ciClassApiSvc.filterByVisualModel(id, orgId);
        return new RemoteResult(res);
    }

    @GetMapping(value = "/edit/check")
    @ModDesc(desc = "校验分类是否能编辑", pDesc = "对象分类id", pType = Long.class, rDesc = "是否能编辑", rType = Boolean.class)
    public RemoteResult checkEdit(@RequestParam Long id) {
        Boolean result = ciClassApiSvc.checkEdit(id);
        return new RemoteResult(result);
    }

    @GetMapping(value = "/edit/checkHasData")
    @ModDesc(desc = "校验分类下是否存在数据", pDesc = "对象分类id", pType = Long.class, rDesc = "是否存在", rType = Boolean.class)
    public RemoteResult checkHasDatacheckHasData(@RequestParam Long id) {
        Boolean result = ciClassApiSvc.checkHasData(id);
        return new RemoteResult(result);
    }
}
