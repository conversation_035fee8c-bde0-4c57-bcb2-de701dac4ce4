package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class HtAppSystemVo {

    @Comment("系统分级")
    private String systemRate;

    @Comment("系统标识")
    private String systemLogo;

    @Comment("系统名称")
    private String systemName;

    @Comment("系统层次")
    private String systemLevel;

    @Comment("应用阶段")
    private String systemStage;

    @Comment("系统描述")
    private String systemDesc;

    @Comment("系统负责人")
    private String systemPrincipals;

    @Comment("申请通过时间")
    private Long approvalTime;

    @Comment("cicode")
    private String ciCode;

    @Comment("页面展示标题")
    private List<String> title;

    @Comment("CI对象")
    private CcCi ci;

    @Comment("CI属性")
    private Map<String, String> attrs;

    @Comment("当前CI所属分类")
    private CcCiClass ciClass;

    @Comment("当前CI属性定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("上下游关系信息与架构资产信息")
    private Map<String, ESDiagram> diagramInfo;

    @Comment("属性变更集")
    private List<HtAppSystemCIChangeVo> htAppSystemCIChangeVos;

    @Comment("关系数据信息")
    private List<ESCIRltInfo> rltInfoList;

    @Comment("视图类型")
    private String artiFactName;

    @Comment("视图修改前版本")
    private Integer beforeVersion;

    @Comment("详情关系信息")
    private List<HtAppSystemRltVo> htAppSystemRltVos;

    @Comment("视图ID")
    private String diagramId;

    @Comment("视图本身变更信息")
    private List<HtAppSystemDiagramVo> htAppSystemDiagramVos;

}
