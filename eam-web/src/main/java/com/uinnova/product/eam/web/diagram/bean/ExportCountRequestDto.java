package com.uinnova.product.eam.web.diagram.bean;

/**
 * 导出统计数据入参传输类
 * 
 * <AUTHOR>
 *
 */
public class ExportCountRequestDto {

	private boolean opDiagramCount;
	private boolean diagramCount;
	private boolean userCount;
	private boolean pvCount;
	private Long start;
	private Long end;

	public boolean isOpDiagramCount() {
		return opDiagramCount;
	}

	public void setOpDiagramCount(boolean opDiagramCount) {
		this.opDiagramCount = opDiagramCount;
	}

	public boolean isDiagramCount() {
		return diagramCount;
	}

	public void setDiagramCount(boolean diagramCount) {
		this.diagramCount = diagramCount;
	}

	public boolean isUserCount() {
		return userCount;
	}

	public void setUserCount(boolean userCount) {
		this.userCount = userCount;
	}

	public boolean isPvCount() {
		return pvCount;
	}

	public void setPvCount(boolean pvCount) {
		this.pvCount = pvCount;
	}

	public Long getStart() {
		return start;
	}

	public void setStart(Long start) {
		this.start = start;
	}

	public Long getEnd() {
		return end;
	}

	public void setEnd(Long end) {
		this.end = end;
	}

}
