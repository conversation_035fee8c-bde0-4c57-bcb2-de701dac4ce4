package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uino.bean.permission.base.SysUser;

public class DiagramVersionInfo implements Serializable{
	
	
	private static final long serialVersionUID = 1L;

	@Comment("视图版本信息")
	private com.uinnova.product.eam.base.diagram.model.VcDiagramVersion diagramVersion;
	
	@Comment("操作人信息")
	private SysUser sysUser;

	@Comment("xxx")
	private SysUser user;

	public SysUser getSysUser() {
		return sysUser;
	}

	public void setSysUser(SysUser sysUser) {
		this.sysUser = sysUser;
	}

	public SysUser getUser() {
		return user;
	}

	public void setUser(SysUser user) {
		this.user = user;
	}

	public VcDiagramVersion getDiagramVersion() {
		return diagramVersion;
	}

	public void setDiagramVersion(com.uinnova.product.eam.base.diagram.model.VcDiagramVersion diagramVersion) {
		this.diagramVersion = diagramVersion;
	}

	public SysUser getOp() {
		return sysUser;
	}

	public void setOp(SysUser op) {
		this.sysUser = op;
	}
	
	

}
