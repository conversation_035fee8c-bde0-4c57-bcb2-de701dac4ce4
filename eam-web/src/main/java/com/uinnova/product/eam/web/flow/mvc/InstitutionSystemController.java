package com.uinnova.product.eam.web.flow.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.InstitutionSystemDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.vo.InstitutionSystemTreeNewDto;
import com.uinnova.product.eam.service.InstitutionSystemService;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 制度体系,标准体系,表单库,术语库,岗位角色库的目录树相关操作
 *
 * <AUTHOR>
 * @since 2024/6/14 19:11
 */
@RestController
@RequestMapping("/flowManager/institutionSystem/")
public class InstitutionSystemController {

    @Resource
    private InstitutionSystemService institutionSystemService;


    @PostMapping("saveOrUpdate")
    @ModDesc(desc = "保存或更新文件夹信息", pDesc = "文件夹信息", rDesc = "文件夹id", rType = RemoteResult.class)
    public RemoteResult saveOrUpdate(@RequestBody EamCategory vo) {
        BinaryUtils.checkEmpty(vo, "vo");
        BinaryUtils.checkEmpty(vo.getDirName(),"dirName");
        Assert.notNull(vo.getParentId(), "父文件夹id不可为空!");
        Long id = institutionSystemService.saveOrUpdateCategory(vo);
        return new RemoteResult(id);
    }

    @PostMapping("delete")
    public RemoteResult delete(@RequestParam Long id) {
        BinaryUtils.checkEmpty(id, "目录id不能为空");
        Integer id1= institutionSystemService.delete(id);
        return new RemoteResult(id1);
    }

    /**
     * 移动目录或者制度
     * @param objectMovingDto
     * @return 结果
     */
    @PostMapping("moveCi")
    public RemoteResult moveCi(@RequestBody ObjectMovingDto objectMovingDto) {
        BinaryUtils.checkEmpty(objectMovingDto, "参数不能为空");
        return new RemoteResult(institutionSystemService.moveCi(objectMovingDto));
    }

    @PostMapping("getInstitutionSystemTreeNew")
    public RemoteResult getInstitutionSystemTreeNew(@RequestParam Long id) {
        BinaryUtils.checkEmpty(id, "目录id不能为空");
        List<InstitutionSystemTreeNewDto> result = institutionSystemService.getInstitutionSystemTreeNew(id);
        return new RemoteResult(result);
    }

    @PostMapping("getInstitutionSystemTreeWithCiNew")
    public RemoteResult getInstitutionSystemTreeWithCiNew(@RequestParam Long id) {
        BinaryUtils.checkEmpty(id, "目录id不能为空");
        List<InstitutionSystemTreeNewDto> result = institutionSystemService.getInstitutionSystemTreeWithCiNew(id);
        return new RemoteResult(result);
    }

    @PostMapping("getInstitutionListNew")
    public RemoteResult getInstitutionListNew(@RequestBody InstitutionSystemDto institutionSystemDto) {
        Assert.notNull(institutionSystemDto,"根目录id不能为空");
        return new RemoteResult(institutionSystemService.getInstitutionListNew(institutionSystemDto));
    }

}
