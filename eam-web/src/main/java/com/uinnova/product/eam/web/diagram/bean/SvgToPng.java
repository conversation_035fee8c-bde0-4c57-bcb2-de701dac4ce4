package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

public class SvgToPng implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("图片的名字")
	private String name;

	@Comment("svg的内容")
	private String content;
	
	@Comment("svg文件路径")
	private String url;
	

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

}
