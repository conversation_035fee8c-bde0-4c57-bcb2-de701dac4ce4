package com.uinnova.product.eam.web.eam.mvc.diagram;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.web.RemoteResult;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamAttention;
import com.uinnova.product.eam.model.constants.DiagramConstant;
import com.uinnova.product.eam.model.dto.AttentionDto;
import com.uinnova.product.eam.model.vo.AttentionVo;
import com.uinnova.product.eam.model.vo.CiDirVo;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.asset.IAttentionSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 我的关注
 */
@RestController
@RequestMapping("/eam/attention")
@Slf4j
public class AttentionMvc {

    @Autowired
    private IAttentionSvc attentionSvc;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Resource
    private BmConfigSvc bmConfigSvc;

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @Autowired
    private IUserApiSvc userApiSvc;

    @PostMapping("/saveAttention")
    public RemoteResult saveAttention(@RequestBody AttentionDto attentionDto) {
        MessageUtil.checkEmpty(attentionDto, "attentionDto");
        MessageUtil.checkEmpty(attentionDto.getAttentionType(), "attentionType");
        MessageUtil.checkEmpty(attentionDto.getAttentionId(), "attentionId");
        MessageUtil.checkEmpty(attentionDto.getSource(), "source");
        EamAttention eamAttention = new EamAttention();
        BeanUtils.copyProperties(attentionDto, eamAttention);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        eamAttention.setUserId(currentUserInfo.getId());
        // 只有视图解密操作
        if (attentionDto.getAttentionType() == 2) {
            // 加密字符串获取Long类型id
            Long diagramId = esDiagramSvc.queryDiagramInfoByEnergy(attentionDto.getAttentionId());
            if (BinaryUtils.isEmpty(diagramId)) {
                throw new ServiceException("获取视图信息出错!");
            }
            eamAttention.setAttentionId(diagramId);
        } else if (attentionDto.getAttentionType() == 4) {
            MessageUtil.checkEmpty(attentionDto.getAttentionCode(), "attentionCode");
            String ownerCode = null;
            LibType libType = LibType.DESIGN;
            if (0 == attentionDto.getSource()) {
                ownerCode = currentUserInfo.getLoginCode();
                libType = LibType.PRIVATE;
            }
            List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(Lists.newArrayList(attentionDto.getAttentionCode()), ownerCode, libType);
            if (CollectionUtils.isEmpty(ciByCodes)) {
                throw new ServiceException("获取资产信息出错!");
            }
            eamAttention.setAttentionId(ciByCodes.get(0).getId());
            eamAttention.setConfigId(attentionDto.getAppSquareConfId());
        } else {
            eamAttention.setAttentionId(Long.valueOf(attentionDto.getAttentionId()));
        }


        eamAttention.setSpecialView(0);
        Long result;
        if (DiagramConstant.IS_ATTENTION.equals(attentionDto.getIsFocus())) {
            result = attentionSvc.saveOrUpdateAttention(eamAttention);
        } else {
            Integer re = attentionSvc.cancelAttention(eamAttention);
            result = Long.valueOf(re);
        }
        return new RemoteResult(result);
    }

    @PostMapping("/findAttentionList")
    public RemoteResult findAttentionList(@RequestBody String data) {
        QueryPageCondition<AttentionDto> attentionPage = RestTypeUtil.toPageCondition(data, AttentionDto.class);
        AttentionDto attentionDto = attentionPage.getCdt();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        attentionDto.setUserId(currentUserInfo.getId());
        AttentionVo attentionVo = attentionSvc.findAttentionList(attentionPage.getPageNum(), attentionPage.getPageSize(), attentionDto);
        return new RemoteResult(attentionVo);
    }

    @PostMapping("/findMineAttentionSysList")
    public RemoteResult findMineAttentionSysList() {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<CiDirVo> list = attentionSvc.findMineAttentionSysList(currentUserInfo.getId());
        return new RemoteResult(list);
    }

    @PostMapping("ifAttention")
    public RemoteResult ifAttention(@RequestBody AttentionDto attentionDto) {
        MessageUtil.checkEmpty(attentionDto, "attentionDto");
        MessageUtil.checkEmpty(attentionDto.getAttentionType(), "attentionType");
        MessageUtil.checkEmpty(attentionDto.getAttentionId(), "attentionId");


        long attentionId;
        // 只有视图解密操作
        if (attentionDto.getAttentionType() == 2) {
            // 加密字符串获取Long类型id
            List<ESDiagram> diagrams = esDiagramSvc.queryDBDiagramInfoByIds(new String[]{attentionDto.getAttentionId()});
            if (CollectionUtils.isEmpty(diagrams)) {
                return new RemoteResult(false);
            }
            Long diagramId = diagrams.get(0).getId();
            if (BinaryUtils.isEmpty(diagramId)) {
                throw new ServiceException("获取视图信息出错!");
            }
            attentionId = diagramId;
        } else if (attentionDto.getAttentionType() == 4) {
            MessageUtil.checkEmpty(attentionDto.getSource(), "source");
            MessageUtil.checkEmpty(attentionDto.getAttentionCode(), "attentionId");
            attentionId = Long.parseLong(attentionDto.getAttentionId());
        } else {
            attentionId = Long.parseLong(attentionDto.getAttentionId());
        }
        EamAttention eamAttention = attentionSvc.getEamAttentionByType(attentionDto.getAttentionType(), attentionId,
                SysUtil.getCurrentUserInfo().getId(),attentionDto.getAttentionCode(),attentionDto.getSource());
        boolean result = false;
        if (!BinaryUtils.isEmpty(eamAttention)) {
            result = true;
        }
        return new RemoteResult(result);
    }

    @GetMapping("sourceList")
    public RemoteResult sourceList() {
        String sourceList = bmConfigSvc.getConfigType("ATTENTION_SOURCE");
        if (BinaryUtils.isEmpty(sourceList)) {
            sourceList = "[\n" +
                    "    {\n" +
                    "        \"sourceTag\": 0,\n" +
                    "        \"showName\": \"架构设计\"\n" +
                    "    },\n" +
                    "    {\n" +
                    "        \"sourceTag\": 1,\n" +
                    "        \"showName\": \"资产仓库\"\n" +
                    "    }\n" +
                    "]";
            log.error("未配置我的收藏下拉显示列表配置，返回默认列表");
        }
        return new RemoteResult(sourceList);
    }

}
