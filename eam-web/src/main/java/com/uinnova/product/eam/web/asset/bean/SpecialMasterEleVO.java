package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class SpecialMasterEleVO implements Serializable {

    @Comment("标准规范主信息元素定义")
    private AttrDefInfo specialEle;

    @Comment("标准规范版本信息元素定义")
    private AttrDefInfo editionEle;
}
