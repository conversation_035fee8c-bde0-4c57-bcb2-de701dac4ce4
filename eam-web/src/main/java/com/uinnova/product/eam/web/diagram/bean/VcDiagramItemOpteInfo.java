package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagramItemOpte;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;

public class VcDiagramItemOpteInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@Comment("CI分类信息")
	private CcCiClass ciClass;
	
	@Comment("视图图标操作信息")
	private VcDiagramItemOpte itemOpte;


	public CcCiClass getCiClass() {
		return ciClass;
	}


	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}


	public VcDiagramItemOpte getItemOpte() {
		return itemOpte;
	}


	public void setItemOpte(VcDiagramItemOpte itemOpte) {
		this.itemOpte = itemOpte;
	}

	
	

}
