package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.base.local.TaskFromWorkflowContext;
import com.uinnova.product.eam.base.local.TaskFromWorkflowContextValue;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.comm.model.es.FlowSystemProcessSingData;
import com.uinnova.product.eam.comm.model.es.WorkbenchChargeDone;
import com.uinnova.product.eam.config.ApplicationContextBean;
import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.feign.workable.entity.*;
import com.uinnova.product.eam.model.cj.vo.PlanDesignInstanceVO;
import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.service.DiagramProcessSvc;
import com.uinnova.product.eam.service.WorkbenchChargeDoneSvc;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.es.FlowSystemProcessSignDataDao;
import com.uinnova.product.eam.service.es.WorkbenchChargeDoneDao;
import com.uinnova.product.eam.service.flowable.FlowableApprovalSvc;
import com.uinnova.product.eam.web.eam.bean.TaskMessageDTO;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/3/4 16:25
 */
@Slf4j
@RestController
@RequestMapping("/flowable")
public class FlowableController {

    private static final String planProcessDefineKey = "xw_it_technical_approve";

    public static final String PLAN_DEFINITION_KEY1 = "cj_technical_scheme_approve";

    public static final String MODEL_DEFINITION_KEY = "xw_model_approve";

    private static final String viewProcessDefineKey = "xw_it_diagram_approve";

    @Resource
    private ESDiagramSvc diagramApiClient;

    @Resource
    private FlowableFeign flowableFeign;
    @Resource
    private IUserApiSvc userApiSvc;
    @Resource
    private DiagramProcessSvc diagramProcessSvc;
    @Resource
    private WorkbenchChargeDoneSvc workbenchChargeDoneSvc;
    @Resource
    private PlanDesignInstanceService planInstanceService;

    @Resource
    private FlowSystemProcessSignDataDao flowSystemProcessSignDataDao;
    @Autowired
    private WorkbenchChargeDoneDao workbenchChargeDoneDao;

    @GetMapping("/getUserTaskList")
    public RemoteResult getUserTaskList(Integer pageSize, Integer pageNum) {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        PageEntityInfo<TaskResponse> taskListByUserId = flowableFeign.getTaskListByUserId(loginCode, pageSize, pageNum);
        List<TaskMessageDTO> taskList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(taskListByUserId.getList())) {
            taskList = EamUtil.copy(taskListByUserId.getList(), TaskMessageDTO.class);
            convertTaskUserName(taskList);
        }
        PageEntityInfo<TaskMessageDTO> result = new PageEntityInfo<>();
        BeanUtils.copyProperties(taskListByUserId, result);
        result.setList(taskList);
        return new RemoteResult(result);
    }

    @GetMapping("/getDoneUserTaskList")
    public RemoteResult getDoneUserTaskList(Integer pageSize, Integer pageNum) {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        PageEntityInfo<TaskResponse> taskListByUserId = flowableFeign
                .getDoneTaskListByUserId(loginCode, pageSize, pageNum);
        List<TaskMessageDTO> taskList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(taskListByUserId.getList())) {
            taskList = EamUtil.copy(taskListByUserId.getList(), TaskMessageDTO.class);
            convertTaskUserName(taskList);
        }
        PageEntityInfo<TaskMessageDTO> result = new PageEntityInfo<>();
        BeanUtils.copyProperties(taskListByUserId, result);
        result.setList(taskList);
        return new RemoteResult(result);
    }


    @GetMapping("/flushData")
    public Integer flushData() {
        List<TaskResponse> doneAllTask = flowableFeign.getDoneAllTask();
        List<String> taskIdList = doneAllTask.stream().filter(item -> StringUtils.isNotEmpty(item.getTaskId())).map(TaskResponse::getTaskId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskIdList)) {
            return 0;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
        boolQueryBuilder.must(QueryBuilders.termsQuery("taskId.keyword",taskIdList));
        List<WorkbenchChargeDone> listByQuery = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
        for (WorkbenchChargeDone workbenchChargeDone : listByQuery) {
            workbenchChargeDone.setAction(2);
        }
        return workbenchChargeDoneDao.saveOrUpdateBatch(listByQuery);
    }

    @GetMapping("/flushWorkbenchData")
    public Integer flushWorkbenchData() {
        List<TaskResponse> currentAllTask = flowableFeign.getCurrentAllTask();
        List<String> taskIdList = currentAllTask.stream().filter(item -> StringUtils.isNotEmpty(item.getTaskId())).map(TaskResponse::getTaskId).collect(Collectors.toList());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("type",2));
        List<WorkbenchChargeDone> listByQuery = workbenchChargeDoneDao.getListByQueryScroll(boolQueryBuilder);
        listByQuery.removeIf(item -> taskIdList.contains(item.getTaskId()));
        for (WorkbenchChargeDone workbenchChargeDone : listByQuery) {
            workbenchChargeDone.setAction(2);
        }
        return workbenchChargeDoneDao.saveOrUpdateBatch(listByQuery);
    }

    @GetMapping("/genProcessDiagram")
    public RemoteResult genProcessDiagram(String processInstanceId){
        Assert.notNull(processInstanceId,"流程实例id不能为空");
        String s = flowableFeign.genProcessDiagram(processInstanceId);
        return new RemoteResult(s+"?t="+ ESUtil.getUUID());
    }

    @GetMapping("/getProcessInstanceByProcessInstanceId")
    public RemoteResult getProcessInstanceByProcessInstanceId(String processInstanceId){
        Assert.notNull(processInstanceId,"流程实例id不能为空");
        PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(processInstanceId);
        return new RemoteResult(processInstanceByProcessInstanceId);
    }

    @GetMapping("/getTaskInfoByTaskId")
    public RemoteResult getTaskInfoByTaskId(String taskId){

        TaskResponse taskResponse = flowableFeign.getTaskInfoByTaskId(taskId);
        return new RemoteResult(taskResponse);
    }

    @GetMapping("/getTaskCountByUserId")
    public RemoteResult getTaskCountByUserId() {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        long taskCountByUserId = flowableFeign.getTaskCountByUserId(loginCode);
        return new RemoteResult(taskCountByUserId);
    }

    @GetMapping("/getHistoryTaskByCurrentProcessInstanceId")
    public RemoteResult getHistoryTaskByCurrentProcessInstanceId(String processInstanceId) throws ParseException {
        Assert.notNull(processInstanceId,"流程实例id不能为空");
        List<HistoryTaskResponse> historyTaskByCurrentProcessInstances = flowableFeign.getHistoryTaskByCurrentProcessInstanceId(processInstanceId);
        PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(processInstanceId);
        if(!CollectionUtils.isEmpty(historyTaskByCurrentProcessInstances)){
            historyTaskByCurrentProcessInstances = getSignHistoryTaskResponses(historyTaskByCurrentProcessInstances, processInstanceByProcessInstanceId);
        }
        return new RemoteResult(historyTaskByCurrentProcessInstances);
    }

    private void extracted(List<HistoryTaskResponse> historyTaskByCurrentProcessInstances) {
        List<String> userList = historyTaskByCurrentProcessInstances.stream().map(HistoryTaskResponse::getUserId).collect(Collectors.toList());
        String startUserId = historyTaskByCurrentProcessInstances.get(0).getStartUserId();
        log.info("startUserId: " + startUserId);
        if(!BinaryUtils.isEmpty(startUserId)) {
            userList.add(startUserId);
        }
        HashSet<String> userIdSet = new HashSet<>(userList);
        String[] userIds = new String[userIdSet.size()];
        CSysUser cSysUser = new CSysUser();
        userIdSet.toArray(userIds);
        cSysUser.setLoginCodes(userIds);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(cSysUser);
        Map<String, SysUser> sysUserMap = sysUserByCdt.parallelStream()
                .collect(Collectors.toMap(SysUser::getLoginCode, u -> u));
        for (HistoryTaskResponse historyTaskByCurrentProcessInstance : historyTaskByCurrentProcessInstances) {
            String userId = historyTaskByCurrentProcessInstance.getUserId();
            Map<String, Object> variables = historyTaskByCurrentProcessInstance.getVariables();
            Object remarks = variables.get("remarks");
            if (remarks != null && "流程撤回".equalsIgnoreCase(remarks.toString())
                    && FlowableConstant.FLOW_SYSTEM_PUBLISH_APPROVE
                    .equalsIgnoreCase(historyTaskByCurrentProcessInstance.getProcessDefinitionKey())) {
                variables.put("user", sysUserMap.get(startUserId).getUserName());
                variables.put("icon", sysUserMap.get(startUserId).getIcon());
                variables.put("pass", "withdraw");
                variables.put("remarks", "");
            } else {
                variables.put("user", sysUserMap.get(userId).getUserName());
                variables.put("icon", sysUserMap.get(userId).getIcon());
            }
            if (FlowableConstant.FLOW_SYSTEM_PUBLISH_APPROVE
                    .equalsIgnoreCase(historyTaskByCurrentProcessInstance.getProcessDefinitionKey())) {
                if (historyTaskByCurrentProcessInstance.getTaskName().equalsIgnoreCase("提交审批")
                        || historyTaskByCurrentProcessInstance.getTaskName().equalsIgnoreCase("提交人修改")) {
                    variables.put("pass", "submit");
                }
            }
            if (FlowableConstant.FLOW_SYSTEM_FLOW_SIGN
                    .equalsIgnoreCase(historyTaskByCurrentProcessInstance.getProcessDefinitionKey())) {
                if (historyTaskByCurrentProcessInstance.getTaskName().equalsIgnoreCase("集成审查人发起签发")) {
                    variables.put("pass", "submit");
                }
            }
        }
    }

    @GetMapping("/getCurrentTaskByCurrentProcessInstanceId")
    public RemoteResult getCurrentTaskByCurrentProcessInstanceId(@RequestParam String processInstanceId) throws ParseException {
        Assert.notNull(processInstanceId, "流程实例id不能为空");
        List<HistoryTaskResponse> history = flowableFeign.getHistoryTaskByCurrentProcessInstanceId(processInstanceId);
        List<TaskResponse> current = flowableFeign.getCurrentTaskByCurrentProcessInstanceId(processInstanceId);
        List<TaskMessageDTO> activeList;
        Map<String, Object> resultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(current)) {
            activeList = EamUtil.copy(current, TaskMessageDTO.class);
            convertTaskUserName(activeList);
            resultMap.put("active", activeList);
        }
        PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(processInstanceId);
        if (!BinaryUtils.isEmpty(history)) {
            List<HistoryTaskResponse> historyTaskResponses = history.stream().sorted(Comparator.comparing(HistoryTaskResponse::getCommitTime)).collect(Collectors.toList());
            historyTaskResponses = getSignHistoryTaskResponses(historyTaskResponses, processInstanceByProcessInstanceId);
            resultMap.put("history", historyTaskResponses);
        }
        if (!BinaryUtils.isEmpty(processInstanceByProcessInstanceId)) {
            resultMap.put("processDefinitionKey", processInstanceByProcessInstanceId.getProcessDefinitionKey());
        }
        return new RemoteResult(resultMap);
    }


    @GetMapping("getTaskListByBusinessId")
    public RemoteResult getTaskListByBusinessId(String businessKey, String processDefinitionKey) {
        Assert.notNull(businessKey, "业务主键不能为空");
        Assert.notNull(processDefinitionKey, "审批类型不能为空");
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setBusinessKey(businessKey);
        processRequest.setProcessDefinitionKey(processDefinitionKey);
        List<PorcessResponse> porcessResponseList = flowableFeign.getTaskListByBusinessId(processRequest);
        convertProcessUserName(porcessResponseList);
        return new RemoteResult(porcessResponseList);
    }

    @PostMapping("/completeTask")
    public RemoteResult completeTask(@RequestBody TaskRequest taskRequest) {
        TaskResponse task = flowableFeign.getTaskInfoByTaskId(taskRequest.getTaskId());
        // 查看当前任务节点是否已经完结
        Map<String, Object> taskEchoVariables = task.getTaskEchoVariables();
        if (!BinaryUtils.isEmpty(taskEchoVariables) && !BinaryUtils.isEmpty(taskEchoVariables.get("pass"))) {
            return new RemoteResult(Boolean.TRUE, ResultCodeEnum.REPEAT_OPERATE.getCode(), ResultCodeEnum.REPEAT_OPERATE.getMessage());
        }
        // 校验多审批用户同时操作流程情况
        Boolean existByTaskId = flowableFeign.isExistByTaskId(task.getTaskId());
        if (!existByTaskId) {
            return new RemoteResult(Boolean.TRUE, ResultCodeEnum.TASK_FAILURE.getCode(), ResultCodeEnum.TASK_FAILURE.getMessage());
        }

        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        if (StringUtils.isEmpty(task.getCurrentAssignees())||!task.getCurrentAssignees().contains(loginCode)) {
            throw new BusinessException("当前用户没有执行权限");
        }
        if (!BinaryUtils.isEmpty(task.getDescription()) && task.getDescription().equals(FlowableConstant.IT_VIEW_END_NODE) && taskRequest.getAction().equals(FLOWACTION.ACCETP)) {
            // IT视图二级审批流程通过 校验发布位置存在性
            Boolean existDir = diagramProcessSvc.isExistDir(task.getBusinessKey(), task.getProcessDefinitionKey());
            if (!existDir) {
                // 审批通过发布目录无权限 后台转为驳回处理
                taskRequest.setAction(FLOWACTION.REJECT);
                taskRequest.setRemarks(ResultCodeEnum.TERMINATE_PROCESS.getMessage());
                flowableFeign.completeTask(taskRequest);
                return new RemoteResult(true, ResultCodeEnum.TERMINATE_PROCESS.getCode(), ResultCodeEnum.TERMINATE_PROCESS.getMessage());
            }
        }
        if (taskRequest.getAction().equals(FLOWACTION.ACCETP) && task.getProcessDefinitionKey().equals(FlowableConstant.MODEL_DEFINITION_KEY)) {
            // 业务流程模型审批通过校验发布位置存在性
            Boolean existDir = diagramProcessSvc.isExistDir(task.getBusinessKey(), task.getProcessDefinitionKey());
            if (!existDir) {
                // 审批通过发布目录无权限 后台转为驳回处理
                taskRequest.setAction(FLOWACTION.REJECT);
                taskRequest.setRemarks(ResultCodeEnum.TERMINATE_PROCESS.getMessage());
                flowableFeign.completeTask(taskRequest);
                return new RemoteResult(true, ResultCodeEnum.TERMINATE_PROCESS.getCode(), ResultCodeEnum.TERMINATE_PROCESS.getMessage());
            }
        }
        TaskResponse taskResponse = flowableFeign.completeTask(taskRequest);

        return new RemoteResult(taskResponse);
    }

    private List<HistoryTaskResponse> getSignHistoryTaskResponses(List<HistoryTaskResponse> historyTaskByCurrentProcessInstances, PorcessResponse processInstanceByProcessInstanceId) throws ParseException {
        if (FlowableConstant.FLOW_SYSTEM_FLOW_SIGN.equalsIgnoreCase(processInstanceByProcessInstanceId.getProcessDefinitionKey())) {
            List<HistoryTaskResponse> signHistoryTaskResponses = new ArrayList<>();
            HistoryTaskResponse historyTaskResponse1 = historyTaskByCurrentProcessInstances.get(0);
            signHistoryTaskResponses.add(historyTaskResponse1);
            //签发逻辑拼接审批记录数据
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("businessKey.keyword", processInstanceByProcessInstanceId.getBusinessKey()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("actionType.keyword", "noSign"));
            List<FlowSystemProcessSingData> listByQueryScroll = flowSystemProcessSignDataDao.getListByQueryScroll(boolQueryBuilder);
            if (!CollectionUtils.isEmpty(listByQueryScroll)) {
                for (FlowSystemProcessSingData flowSystemProcessSingData : listByQueryScroll) {
                    HistoryTaskResponse historyTaskResponse = new HistoryTaskResponse();
                    historyTaskResponse.setTaskName("签发人签发");
                    historyTaskResponse.setUserId(flowSystemProcessSingData.getSignUserCode());
                    historyTaskResponse.setProcessDefinitionKey(historyTaskResponse1.getProcessDefinitionKey());
                    historyTaskResponse.setProcessInstanceName(historyTaskResponse1.getProcessInstanceName());
                    historyTaskResponse.setStartUserId(historyTaskResponse1.getStartUserId());
                    historyTaskResponse.setTaskId(UUID.randomUUID().toString());
                    historyTaskResponse.setUserId(historyTaskResponse1.getUserId());
                    Long modifyTime = flowSystemProcessSingData.getModifyTime();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                    Date parse = sdf.parse(modifyTime + "");
                    historyTaskResponse.setCommitTime(parse);
                    Map<String, Object> tmpMap = new HashMap<>();
                    tmpMap.put("pass", flowSystemProcessSingData.getActionType());
                    tmpMap.put("remarks",flowSystemProcessSingData.getRemarks());
                    historyTaskResponse.setVariables(tmpMap);
                    signHistoryTaskResponses.add(historyTaskResponse);
                }
            }
            historyTaskByCurrentProcessInstances = signHistoryTaskResponses.stream().sorted(Comparator.comparing(HistoryTaskResponse::getCommitTime)).collect(Collectors.toList());
        }
        extracted(historyTaskByCurrentProcessInstances);
        return historyTaskByCurrentProcessInstances;
    }

    private void convertTaskUserName(List<TaskMessageDTO> taskResponses) {
        Set<String> userIdSet = Sets.newHashSet();
        for (TaskMessageDTO taskRespons : taskResponses) {
            userIdSet.add(taskRespons.getUserId());
            userIdSet.add(taskRespons.getSubmitter());
            if (StringUtils.isNotBlank(taskRespons.getCurrentAssignees()) && !"-".equals(taskRespons.getCurrentAssignees())) {
                String[] split = taskRespons.getCurrentAssignees().split(",");
                userIdSet.addAll(Arrays.asList(split));
            }
        }
        Map<String, SysUser> sysUserMap = getStringSysUserMap(userIdSet);
        for (TaskMessageDTO taskRespons : taskResponses) {
            if(FlowableConstant.PLAN_DEFINITION_KEY1.equals(taskRespons.getProcessDefinitionKey())
                    || FlowableConstant.PLAN_DEFINITION_KEY2.equals(taskRespons.getProcessDefinitionKey())
                    || FlowableConstant.XW_BUSINESS_SCENARIO_APPROVE.equals(taskRespons.getProcessDefinitionKey())){

                Map<String, Object> taskEchoVariables = taskRespons.getTaskEchoVariables();
                Object childVariable = taskEchoVariables.get("childVariable");
                if(childVariable!=null){
                    taskRespons.setType(4);
                }else {
                    taskRespons.setType(1);
                }
            } else if (FlowableConstant.DIAGRAM_DEFINITION_KEY.equals(taskRespons.getProcessDefinitionKey())) {
                taskRespons.setType(3);
            } else if (FlowableConstant.MODEL_DEFINITION_KEY.equals(taskRespons.getProcessDefinitionKey())) {
                taskRespons.setType(4);
            }
            if (sysUserMap.get(taskRespons.getUserId()) != null) {
                taskRespons.setUserId(sysUserMap.get(taskRespons.getUserId()).getUserName());
            }
            if (sysUserMap.get(taskRespons.getSubmitter()) != null) {
                taskRespons.setSubmitter(sysUserMap.get(taskRespons.getSubmitter()).getUserName());
            }
            if (StringUtils.isNotBlank(taskRespons.getCurrentAssignees()) && !"-".equals(taskRespons.getCurrentAssignees())) {
                String[] split = taskRespons.getCurrentAssignees().split(",");
                Set<String> collect = Arrays.stream(split).filter(s -> sysUserMap.get(s) != null)
                        .map(s -> sysUserMap.get(s).getUserName()).collect(Collectors.toSet());
                taskRespons.setCurrentAssignees(StringUtils.join(collect, ","));
            }
        }
    }

    private void convertProcessUserName(List<PorcessResponse> porcessResponseList) {
        if (!CollectionUtils.isEmpty(porcessResponseList)) {
            Set<String> userIdSet = porcessResponseList.stream().map(PorcessResponse::getProcessStartUserId).collect(Collectors.toSet());
            Map<String, SysUser> stringSysUserMap = getStringSysUserMap(userIdSet);
            for (PorcessResponse porcessResponse : porcessResponseList) {
                porcessResponse.setProcessStartUserName(stringSysUserMap.get(porcessResponse.getProcessStartUserId()).getUserName());
            }
        }
    }

    private Map<String, SysUser> getStringSysUserMap(Set<String> userIdSet) {
        String[] userIds = new String[userIdSet.size()];
        userIdSet.toArray(userIds);
        CSysUser cSysUser = new CSysUser();
        cSysUser.setLoginCodes(userIds);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(cSysUser);
        Map<String, SysUser> sysUserMap = sysUserByCdt.parallelStream()
                .collect(Collectors.toMap(SysUser::getLoginCode, u -> u));
        return sysUserMap;
    }

    @GetMapping("/getApprovalUser")
    @ModDesc(desc = "根据流程节点查询对应审批用户", pDesc = "流程定义key,业务key,任务节点key", rDesc = "用户标识集合", rType = RemoteResult.class)
    public List<String> getApprovalUser(@RequestParam String defKey, @RequestParam String businessKey, @RequestParam String taskKey) {
        log.info("defKey={},businessKey={},taskKey={}", defKey, businessKey, taskKey);
        FlowableApprovalSvc approvalSvc = ApplicationContextBean.getBean(defKey, FlowableApprovalSvc.class);
        return approvalSvc.getApprovalUser(businessKey, taskKey);
    }

    @GetMapping("/approval/task")
    @ModDesc(desc = "根据流程节点审批", pDesc = "流程定义key,业务key,任务节点key", rDesc = "用户标识集合", rType = RemoteResult.class)
    public Integer approvalTask(@RequestParam String approval, @RequestParam String defKey, @RequestParam String businessKey,@RequestParam(defaultValue = "false") Boolean childTask,@RequestParam(defaultValue = "") String startUserLoginCode,@RequestParam(defaultValue = "") String processInstanceId) {
        FlowableApprovalSvc approvalSvc = ApplicationContextBean.getBean(defKey, FlowableApprovalSvc.class);
        TaskFromWorkflowContextValue contextValue = new TaskFromWorkflowContextValue(Boolean.TRUE, startUserLoginCode);
        contextValue.setProcessInstanceId(processInstanceId);
        TaskFromWorkflowContext.setContext(contextValue);
        if(Objects.equals(approval, "pass")){
            approvalSvc.pass(businessKey);
        } else if(Objects.equals(approval, "noPass")) {
            if(childTask){
                approvalSvc.childReject(businessKey);
            }else{
                approvalSvc.reject(businessKey);
            }
        } else if(Objects.equals(approval, "cancel")) {
            approvalSvc.cancel(businessKey);
        }
        TaskFromWorkflowContext.release();
        return 1;
    }

    @DeleteMapping("/process/delete")
    @ModDesc(desc = "删除流程实例", pDesc = "业务key", rDesc = "", rType = RemoteResult.class)
    public RemoteResult deleteFlowable(@RequestParam String businessKey) {
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setBusinessKey(businessKey);
        processRequest.setDeleteReason("1");
        flowableFeign.deleteProcessInstanceByBusinessId(processRequest);
        return new RemoteResult(1);
    }

    @GetMapping("/getCurrentUserTask")
    public RemoteResult getCurrentUserTask(@RequestParam String businessKey, @RequestParam String processDefinitionKey,
                                           @RequestParam String userCode) {
        TaskResponse result = flowableFeign.getCurrentUserTask(businessKey,processDefinitionKey,userCode);
        return new RemoteResult(result);
    }

    @GetMapping("/batchModifyWorkbenchTask")
    public Boolean batchModifyWorkbenchTask(@RequestParam String processInstanceId,@RequestParam String businessKey) {
        return workbenchChargeDoneSvc.batchModifyWorkbenchTask(processInstanceId,businessKey);
    }

    @GetMapping("/selectFlowStatus")
    @ApiOperation(value = "查询终止审批视图/方案，true则表示该数据已被终止，流程已经结束,false在流程中", notes = "", httpMethod = "GET")
    public RemoteResult selectFlowStatus(@RequestParam String businessKey, @RequestParam String processDefinitionKey){
        //是否中止，true= 终止；false=未终止；
        Boolean isAbort = flowableFeign.selectFlowStatus(businessKey, processDefinitionKey);
        boolean result = false;
        if(Boolean.TRUE.equals(isAbort)){
            if(viewProcessDefineKey.equals(processDefinitionKey)){
                //视图 isOpen=0 表示图在私有库，设计空间的图isOpen=1
                ESDiagram esDiagram = diagramApiClient.getEsDiagram(businessKey, 0);
                if(BinaryUtils.isEmpty(esDiagram) || esDiagram.getDataStatus() == 0 || esDiagram.getStatus() == 0){
                    result = true;
                }
            }
            if(planProcessDefineKey.equals(processDefinitionKey) || PLAN_DEFINITION_KEY1.equals(processDefinitionKey)){
                //方案
                PlanDesignInstanceVO instanceVO = planInstanceService.getById(Long.valueOf(businessKey));
                if(BinaryUtils.isEmpty(instanceVO) || instanceVO.getStatus().equals("deleted")){
                    result = true;
                }
            }
//            if(MODEL_DEFINITION_KEY.equals(processDefinitionKey)){
//                //模型
//                DiagramApproveRlt approveRlt = diagramApproveRltSvc.getApproveRlt(Long.valueOf(businessKey));
//                if(!BinaryUtils.isEmpty(approveRlt) && !BinaryUtils.isEmpty(approveRlt.getApprovelDirList())){
//                    List<Long> dirIds = approveRlt.getApprovelDirList();
//                    BoolQueryBuilder query = new BoolQueryBuilder();
//                    query.must(QueryBuilders.termQuery("dataStatus",1))
//                            .must(QueryBuilders.termsQuery("id",dirIds));
//                    List<EamCategory> categoryList = categorySvc.selectCategoryByQuery(query,LibType.PRIVATE);
//                    //只有审批的目录都被删除，才给提示；
//                    if(BinaryUtils.isEmpty(categoryList)){
//                        result = true;
//                    }
//                }
//            }
        }
        return new RemoteResult(result);
    }

    /**
     *  工作台已办校验任务数据状态
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    @GetMapping("/selectDataStatus")
    public RemoteResult selectDataStatus(@RequestParam String businessKey,@RequestParam String processDefinitionKey) {
        Assert.notNull(businessKey, "businessKey不能为空");
        Assert.notNull(processDefinitionKey, "processDefinitionKey不能为空");
        return new RemoteResult(diagramProcessSvc.selectDataStatus(businessKey,processDefinitionKey));
    }

    /**
     *  工作台待办数据校验task是否存在
     * @param taskId
     * @return
     */
    @GetMapping("/selectTaskStatus")
    public RemoteResult selectTaskStatus(@RequestParam String taskId) {
        Assert.notNull(taskId, "taskId不能为空");
        Boolean existByTaskId = flowableFeign.isExistByTaskId(taskId);
        if (!existByTaskId) {
            throw new ServerException("当前待办任务已完结，请刷新页面");
        }
        return new RemoteResult(existByTaskId);
    }

}
