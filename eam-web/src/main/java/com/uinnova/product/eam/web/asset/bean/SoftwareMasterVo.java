package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.CIDataInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class SoftwareMasterVo implements Serializable {

    @Comment("外购软件主信息元素集合")
    private CIDataInfo software;

    @Comment("外购软件版本信息集合")
    private List<CIDataInfo> editionList;

    @Comment("当前标准规范所属最新版本")
    private String editionNo;




//    @Comment("标准规范主信息隐藏字段")
//    private List<String> specialHiddenAttrs;

//    @Comment("标准规范版本隐藏字段")
//    private List<String> editionHiddenAttrs;

}
