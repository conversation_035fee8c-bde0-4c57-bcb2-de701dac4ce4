package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.WorkbenchChargeDone;
import com.uinnova.product.eam.model.WorkbenchChargeDoneDto;
import com.uinnova.product.eam.model.vo.WorkbenchBO;
import com.uinnova.product.eam.service.WorkbenchChargeDoneSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/eam/workbenchChargeDone")
public class WorkbenchChargeDoneMvc {

    @Autowired
    private WorkbenchChargeDoneSvc workbenchChargeDoneSvc;

    @PostMapping("/saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody List<WorkbenchChargeDone> workbenchChargeDoneList) {
        Boolean result = workbenchChargeDoneSvc.saveOrUpdate(workbenchChargeDoneList);
        return new RemoteResult(result);
    }

    @PostMapping("/deleteByCondition")
    public boolean deleteByCondition(@RequestBody WorkbenchChargeDone workbenchChargeDone) {
        return workbenchChargeDoneSvc.deleteByCondition(workbenchChargeDone);
    }

    @GetMapping("/changeAction")
    public RemoteResult changeAction(@RequestParam String businessId) {
        boolean result = workbenchChargeDoneSvc.changeAction(businessId);
        return new RemoteResult(result);
    }


    @GetMapping("/pageQueryList")
    public RemoteResult pageQueryList(WorkbenchChargeDoneDto doneDto) {
        Page<WorkbenchChargeDone> result = workbenchChargeDoneSvc.pageQueryList(doneDto);
        return new RemoteResult(result);
    }

    @GetMapping("/getCount")
    public RemoteResult getCount() {
        WorkbenchBO workbenchBO = workbenchChargeDoneSvc.getCount();
        return new RemoteResult(workbenchBO);
    }
    /**
     * 免登录获取待办数量
     */
    @GetMapping("/openApi/todoCount")
    public RemoteResult todoCount(String loginCode) {
        Assert.notNull(loginCode,"用户编码loginCode不能为空");
        return new RemoteResult(workbenchChargeDoneSvc.todoCount(loginCode));
    }


    @GetMapping("/transform")
    public RemoteResult transform(@RequestParam String businessId , @RequestParam String transId,@RequestParam(required = false) Integer dirType) {
        return workbenchChargeDoneSvc.transform(businessId,transId,dirType);
    }

    @GetMapping("/updateInfoStatus")
    public RemoteResult updateInfoStatus(@RequestParam(required = false) Long planId, @RequestParam(required = false) String diagramId,@RequestParam Integer type) {
       boolean result =  workbenchChargeDoneSvc.updateInfoStatus(planId,diagramId,type);
       return new RemoteResult(result);
    }

}
