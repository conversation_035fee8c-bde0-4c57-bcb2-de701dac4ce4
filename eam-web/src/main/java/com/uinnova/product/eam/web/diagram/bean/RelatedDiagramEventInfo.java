package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.annotation.Comment;

@Comment("关联视图告警信息")
public class RelatedDiagramEventInfo {

	@Comment("查询的视图id")
	private Long diagramId;
	
	@Comment("ciId")
	private Long ciId;
	
	@Comment("ciCode")
	private String ciCode;
	
	@Comment("最高告警级别")
	private Integer maxEventLevel;

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public Long getCiId() {
		return ciId;
	}

	public void setCiId(Long ciId) {
		this.ciId = ciId;
	}

	public String getCiCode() {
		return ciCode;
	}

	public void setCiCode(String ciCode) {
		this.ciCode = ciCode;
	}

	public Integer getMaxEventLevel() {
		return maxEventLevel;
	}

	public void setMaxEventLevel(Integer maxEventLevel) {
		this.maxEventLevel = maxEventLevel;
	}
	
	
	
}
