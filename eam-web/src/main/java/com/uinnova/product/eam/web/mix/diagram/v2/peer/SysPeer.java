package com.uinnova.product.eam.web.mix.diagram.v2.peer;


import cn.hutool.json.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.api.diagram.SysApiClient;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.base.diagram.model.NewUserRecord;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

import java.util.List;

@Service
public class SysPeer {

    @Resource
    private SysApiClient sysApiClient;


    public String uploadFile(MultipartFile file) {
        return sysApiClient.uploadFile(file);
    }

    public List<NewUserRecord> getIsOlderUser(Long userId) {
        return sysApiClient.getIsOlderUser(userId);
    }

    public Long setIsOlderUser(Long userId, JSONObject Object) {
        return sysApiClient.setIsOlderUser(userId, Object);
    }

    public ESUploadManage upload(MultipartFile file) {
        return sysApiClient.upload(file);
    }

    public Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders) {
        return sysApiClient.queryData(pageNum,pageSize,orders);
    }

    public Object deleteImage(CcImage image) {
        return sysApiClient.deleteImage(image);
    }
}
