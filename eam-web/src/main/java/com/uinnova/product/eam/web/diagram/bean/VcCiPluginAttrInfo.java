package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.web.integration.bean.VcCiClassInfo;
import com.uinnova.product.vmdb.comm.model.ci.CcCiPluginAttr;

public class VcCiPluginAttrInfo implements Serializable{

	private static final long serialVersionUID = 1L;

	@Comment("ci分类外挂属性")
	private CcCiPluginAttr pluginAttr;
	
	@Comment("ci分类信息")
	private VcCiClassInfo classInfo;

	public CcCiPluginAttr getPluginAttr() {
		return pluginAttr;
	}

	public void setPluginAttr(CcCiPluginAttr pluginAttr) {
		this.pluginAttr = pluginAttr;
	}

	public VcCiClassInfo getClassInfo() {
		return classInfo;
	}

	public void setClassInfo(VcCiClassInfo classInfo) {
		this.classInfo = classInfo;
	}
	
	
}
