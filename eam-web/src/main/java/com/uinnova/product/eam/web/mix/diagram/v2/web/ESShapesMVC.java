package com.uinnova.product.eam.web.mix.diagram.v2.web;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.service.diagram.ESShapeSvc;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.ESShapePeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Classname
 * @Description ES我的形状类
 * <AUTHOR>
 * @Date 2021-08-23-16:56
 */
@RestController
@RequestMapping("/eam/esShape")
public class ESShapesMVC {

    @Autowired
    private ESShapeSvc esShapeSvc;

    @Autowired
    private ESShapePeer svc;

    @PostMapping({"/sort"})
    @ModDesc(
            desc = "我的形状排序",
            pDesc = "排序",
            rDesc = "形状排序",
            rType = List.class,
            rcType = ImageCount.class
    )
    public void sort(HttpServletRequest request, HttpServletResponse response, @RequestBody() ESUserShapeQuery cdt) throws Exception {
        ControllerUtils.returnJson(request, response, this.svc.sort(cdt));
    }


    @CrossOrigin(origins = {})
    @PostMapping({"/queryImageDirList"})
    @ModDesc(
            desc = "查询图标文件夹列表",
            pDesc = "无",
            rDesc = "图标文件夹列表",
            rType = List.class,
            rcType = ImageCount.class
    )
    public void queryImageDirList(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) CCcCiClassDir cdt) throws Exception {
        ControllerUtils.returnJson(request, response, this.svc.queryImageDirList(cdt));
    }

    @PostMapping({"/queryImagePage"})
    @ModDesc(
            desc = "分页查询图标数据",
            pDesc = "查询条件",
            pType = ESSearchImageBean.class,
            rDesc = "分页查询结果",
            rType = Page.class,
            rcType = CcImage.class
    )
    public void queryImagePage(HttpServletRequest request, HttpServletResponse response, @RequestBody ESImageBean bean) {
        ControllerUtils.returnJson(request, response, this.svc.queryImagePage(bean));
    }

    @PostMapping({"/queryAllImage"})
    @ModDesc(
            desc = "全量查询图标数据",
            pDesc = "查询条件",
            pType = ESSearchImageBean.class,
            rDesc = "全量查询结果",
            rType = Page.class,
            rcType = ESUserShape.class
    )
    public void queryAllImage(HttpServletRequest request, HttpServletResponse response, @RequestBody ESImageBean bean) {
        ControllerUtils.returnJson(request, response, this.svc.queryAllImage(bean));
    }

    @PostMapping({"/saveOrUpdateShape"})
    @ModDesc(
            desc = "保存或修改图形",
            pDesc = "保存或修改图形",
            pType = CcImage.class,
            rDesc = "图形id",
            rType = Long.class
    )
    public void updateImageRlt(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        ESUserShape param = JSON.toObject(body, ESUserShape.class);
        Long result = this.svc.updateImageRlt(param);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping({"/deleteImage"})
    @ModDesc(
            desc = "删除形状",
            pDesc = "要删除的形状信息",
            pType = CcImage.class,
            rDesc = "操作是否成功",
            rType = Boolean.class
    )
    public void deleteImage(@RequestBody CcImage image, HttpServletRequest request, HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, this.svc.deleteImage(image));
    }

    @PostMapping({"/deleteDirImage"})
    @ModDesc(
            desc = "删除图标文件夹里及其下图标",
            pDesc = "文件夹id",
            pType = Long.class,
            rDesc = "操作是否成功",
            rType = Boolean.class
    )
    public void deleteDirImage(@RequestBody ESShapeDir dir, HttpServletRequest request, HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, this.svc.deleteDirImage(dir.getId()));
    }

    @RequestMapping("/nodeToEnergy")
    @ModDesc(desc = "删除我的图形", pDesc = "", rDesc = "图形信息", rType = RemoteResult.class)
    private RemoteResult remove(@RequestBody ESUserShapeDTO dto) {
        return new RemoteResult(esShapeSvc.nodeToEnergy(dto.getId()));
    }

    @RequestMapping("/queryDiagrams")
    @ModDesc(desc = "查询我的图形", pDesc = "", rDesc = "图形信息", rType = RemoteResult.class)
    private RemoteResult queryDiagrams() throws InterruptedException {

        return new RemoteResult(esShapeSvc.addDenergy().toString());
    }

    @RequestMapping("/updateHttp")
    @ModDesc(desc = "查询我的图形", pDesc = "", rDesc = "图形信息", rType = RemoteResult.class)
    private RemoteResult updateHttp(@RequestBody ESUserShapeDTO dto) throws InterruptedException {

        return new RemoteResult(esShapeSvc.updateHttp(dto.getKey(), dto.getShapeJson()).toString());
    }


//    @PostMapping({"/importZipImage"})
//    @ModDesc(
//            desc = "上传图标文件(必须是zip格式,且以压缩包中文件夹作为图标目录,文件夹下图标将被导入对应目录下)",
//            pDesc = "图标zip文件",
//            pType = MultipartFile.class,
//            rDesc = "导入明细",
//            rType = ImportResultMessage.class
//    )
//    public void importZipImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "sourceType", required = false) Integer sourceType, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.importZipImage(sourceType, file));
//    }
//
//    @PostMapping({"/importImage"})
//    @ModDesc(
//            desc = "单个添加图标",
//            pDesc = "图标文件及对应文件夹id",
//            rDesc = "操作是否成功",
//            rType = Boolean.class
//    )
//    public void importImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "dirId") Long dirId, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.importImage(dirId, file));
//    }
//
//    @PostMapping({"/importImages"})
//    @ModDesc(
//            desc = "批量添加图标",
//            pDesc = "图标文件数组及对应文件夹id",
//            rDesc = "添加明细",
//            rType = ImportDirMessage.class
//    )
//    public void importImages(@RequestParam(name = "file") MultipartFile[] files, @RequestParam(name = "dirId") Long dirId, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.importImages(dirId, files));
//    }
//
//    @PostMapping({"/replaceImage"})
//    @ModDesc(
//            desc = "2D图标替换",
//            pDesc = "图标文件及被替换的图标id",
//            rDesc = "操作是否成功",
//            rType = Boolean.class
//    )
//    public void replaceImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "imgId") Long imgId, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.replaceImage(imgId, file));
//    }


//    @RequestMapping({"/exportImageZipByDirIds"})
//    @ModDesc(
//            desc = "导出指定文件夹下图标",
//            pDesc = "文件夹id集合",
//            pType = Set.class,
//            pcType = Long.class,
//            rDesc = "图标导出文件",
//            rType = ResponseEntity.class
//    )
//    public ResponseEntity<byte[]> exportImageZipByDirId(@RequestBody Set<Long> dirIds, HttpServletRequest request, HttpServletResponse response) {
//        ResponseEntity<byte[]> file = this.svc.exportImageZipByDirIds(dirIds);
//        return file;
//    }
//
//    @PostMapping({"/queryTopImage"})
//    @ModDesc(
//            desc = "查询置顶图标",
//            pDesc = "查询条件",
//            pType = SearchKeywordBean.class,
//            rDesc = "置顶图标集合",
//            rType = List.class,
//            rcType = CcImage.class
//    )
//    public void queryTopImage(@RequestBody SearchKeywordBean bean, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.queryTopImage(bean));
//    }
//
//    @PostMapping({"/import3DZipImage"})
//    @ModDesc(
//            desc = "导入3D图标(必须是zip格式,且以Tarsier 3D文件夹名为3D图标标识,每个图标需包含对应名称的文件夹)",
//            pDesc = "图标文件",
//            pType = MultipartFile.class,
//            rDesc = "导入明细",
//            rType = ImportDirMessage.class
//    )
//    public void import3DZipImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "dirId") Long dirId, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.import3DZipImage(file, dirId, false));
//    }
//
//    @PostMapping({"/replace3DImage"})
//    @ModDesc(
//            desc = "3D图标替换(必须是zip格式)",
//            pDesc = "图标zip文件及被替换的图标id",
//            rDesc = "操作是否成功",
//            rType = Boolean.class
//    )
//    public void replace3DImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "imgId") Long imgId, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.replace3DImage(imgId, file));
//    }
//
//    @PostMapping({"/delete3DImage"})
//    @ModDesc(
//            desc = "删除3D图标",
//            pDesc = "要删除的图标信息",
//            pType = CcImage.class,
//            rDesc = "操作是否成功",
//            rType = Boolean.class
//    )
//    public void delete3DImage(@RequestBody CcImage image, HttpServletRequest request, HttpServletResponse response) {
//        ControllerUtils.returnJson(request, response, this.svc.delete3DImage(image));
//    }

//    @PostMapping({"/downloadImageResource"})
//    @ModDesc(
//            desc = "下载图标资源",
//            pDesc = "要下载的资源id",
//            pType = List.class,
//            pcType = Long.class,
//            rDesc = "图标文件",
//            rType = Resource.class
//    )
//    @ApiOperation("下载图标资源")
//    public ResponseEntity<byte[]> downloadImageResource(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response) {
//        return this.svc.downloadImageResource(ids);
//    }

//    @PostMapping("/saveOrUpdateShape")
//    @ModDesc(desc = "新增形状", pDesc = "", rDesc = "我的图形信息", rType = RemoteResult.class)
//    private RemoteResult saveOrUpdateShape(@RequestBody ESUserShapeDTO esUserShapeDTO) {
//        return this.svc.saveOrUpdateShape(esUserShapeDTO);
//    }


}
