package com.uinnova.product.eam.web.eam.mvc.diagram;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamAttention;
import com.uinnova.product.eam.comm.model.es.EamRecentlyView;
import com.uinnova.product.eam.model.dto.AttentionDto;
import com.uinnova.product.eam.model.enums.AttentionTypeEnum;
import com.uinnova.product.eam.model.enums.HandleTypeEnum;
import com.uinnova.product.eam.model.vo.AttentionVo;
import com.uinnova.product.eam.model.vo.MineAssetsVo;
import com.uinnova.product.eam.service.asset.IAttentionSvc;
import com.uinnova.product.eam.service.asset.IRecentlyViewSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 我的资产包含：
 * 我的发布
 * 我的关注
 * 最近常看
 */
@RestController
@RequestMapping("/eam/mine/asstes")
public class MineAssetsMvc {

    @Autowired
    private IRecentlyViewSvc recentlyViewSvc;

    @Autowired
    private IAttentionSvc attentionSvc;
    @Resource
    private ESDiagramSvc diagramApiClient;

    @PostMapping("/getBuildAssets")
    @Deprecated
    public RemoteResult getBuildAssets(@RequestBody MineAssetsVo mineAssetsVo) {
        MessageUtil.checkEmpty(mineAssetsVo, "mineAssetsVo");
        MessageUtil.checkEmpty(mineAssetsVo.getHandleType(), "handleType");

        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        if (HandleTypeEnum.MINE_PUBLISH.getHandleType().equals(mineAssetsVo.getHandleType())) {

            BoolQueryBuilder diagramQuery = QueryBuilders.boolQuery();
            diagramQuery.must(QueryBuilders.termQuery("isOpen", 1));
            diagramQuery.must(QueryBuilders.termQuery("creator.keyword", currentUserInfo.getLoginCode()));
            diagramQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
            Page<ESDiagram> diagramPage = diagramApiClient.selectListByQuery(1, 1000, diagramQuery);
            // 我的发布数据返现关注字段信息
            EamAttention eamAttention = new EamAttention();
            eamAttention.setAttentionType(AttentionTypeEnum.FILE_VIEW.getAttentionType());
            eamAttention.setUserId(currentUserInfo.getId());
            List<EamAttention> myAttentionList = attentionSvc.findUserAttentionList(eamAttention);
            List<Long> myAttentId = new ArrayList<>();
            myAttentionList.forEach(e -> {
                myAttentId.add(e.getAttentionId());
            });
            AttentionVo attentionVo = new AttentionVo();
            attentionVo.setDiagramList(diagramPage.getData());
            return new RemoteResult(attentionVo);
        }else if (HandleTypeEnum.MINE_ATTENTION.getHandleType().equals(mineAssetsVo.getHandleType())) {
            AttentionDto attentionDto = new AttentionDto();
            attentionDto.setUserId(currentUserInfo.getId());
            AttentionVo attentionList = attentionSvc.findAttentionList(1, 500, attentionDto);
            return new RemoteResult(attentionList);
        } else if (HandleTypeEnum.RECENTLY_VIEW.getHandleType().equals(mineAssetsVo.getHandleType())) {
            EamRecentlyView eamRecentlyView = new EamRecentlyView();
            eamRecentlyView.setUserId(currentUserInfo.getId());
            List<ESDiagram> recentlyViewList = recentlyViewSvc.findRecentlyViewList(eamRecentlyView);
            AttentionVo attentionVo = new AttentionVo();
            attentionVo.setDiagramList(recentlyViewList);
            return new RemoteResult(attentionVo);
        }
        return null;
    }

    @PostMapping("/findChildDiagramList")
    public RemoteResult findChildDiagramList(@RequestBody MineAssetsVo mineAssetsVo) {
        AttentionVo attentionVo = new AttentionVo();
        //用的话需要重构
        /*if (AttentionBuildEnum.ENTERPRISE_BUILD_ASSETS.getBuildType().equals(mineAssetsVo.getBuildType())) {
            DiagramDirInfoDTO dto = new DiagramDirInfoDTO();
            dto.setDirId(mineAssetsVo.getDirId());
            dto.setQueryType(0);
            dto.setPageNum(1);
            dto.setPageSize(3000);
            ESDiagramDirInfo esDiagramDirInfo = epdiagramSvc.queryAssetDirInfoPageByParentId(dto);
            Page<ESSimpleDiagramDTO> diagramInfoPage = esDiagramDirInfo.getDiagramInfoPage();
            attentionVo.setAssetsDirList(esDiagramDirInfo.getChildrenDirs());
            attentionVo.setDiagramList(diagramInfoPage.getData());
        } else if (AttentionBuildEnum.SYSTEM_BUILD_ASSETS.getBuildType().equals(mineAssetsVo.getBuildType())) {
            DiagramDirInfoDTO diagramDirInfoDTO = new DiagramDirInfoDTO();
            diagramDirInfoDTO.setPageNum(1);
            diagramDirInfoDTO.setPageSize(3000);
            diagramDirInfoDTO.setDirId(mineAssetsVo.getDirId());
            diagramDirInfoDTO.setQueryType(0);
            EsSystemDiagramDirInfo esDiagramDirInfo = systemDiagramPeer.queryAssetsDirPageByParentId(diagramDirInfoDTO);
            Page<ESSimpleDiagramDTO> diagramInfoPage = esDiagramDirInfo.getDiagramInfoPage();
            List<VcDiagramDirVo> childrenDirs = esDiagramDirInfo.getChildrenDirs();
            List<EamDiagramDirVo> AllAssetsDirList = new ArrayList<>();
            childrenDirs.forEach(vcDiagramDirVo -> {
                EamDiagramDirVo eamDiagramDirVo = new EamDiagramDirVo();
                BeanUtils.copyProperties(vcDiagramDirVo, eamDiagramDirVo);
                AllAssetsDirList.add(eamDiagramDirVo);
            });
            attentionVo.setAssetsDirList(AllAssetsDirList);
            attentionVo.setDiagramList(diagramInfoPage.getData());
            attentionVo.setCiGroupPage(esDiagramDirInfo.getCiGroupPage());
        }*/
        return new RemoteResult(attentionVo);
    }
}
