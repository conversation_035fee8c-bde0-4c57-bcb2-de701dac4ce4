package com.uinnova.product.eam.web.system.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.base.model.DictInfo;
import com.uinnova.product.eam.web.system.peer.DictPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 系统管理相关接口
 */
@Controller
@RequestMapping("/dict/")
@MvcDesc(author="Shaolong.fan",desc="数据字典管理")
public class DictManagerMvc {

    @Resource
    private DictPeer dictPeer;

    @GetMapping("/selectList")
    public void selectList(HttpServletRequest request, HttpServletResponse response, @RequestParam("codeType") String codeType, @RequestParam(value = "parentCode", required = false) String parentCode){
        parentCode = (BinaryUtils.isEmpty(parentCode) || "\"\"".equals(parentCode)) ? null : parentCode;
        List<DictInfo> list = dictPeer.selectListByType(codeType, parentCode);
        ControllerUtils.returnJson(request, response, list);
    }

    @PostMapping("/selectGroupList")
    public void selectGroupList(HttpServletRequest request, HttpServletResponse response, @RequestBody List<String> codeTypes){
        Map<String, List<DictInfo>> groupList = dictPeer.selectGroupList(codeTypes);
        ControllerUtils.returnJson(request, response, groupList);
    }

}
