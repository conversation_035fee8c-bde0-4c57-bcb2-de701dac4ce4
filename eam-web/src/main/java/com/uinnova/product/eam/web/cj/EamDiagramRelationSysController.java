package com.uinnova.product.eam.web.cj;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.EamDiagramRelationSys;
import com.uinnova.product.eam.service.EamDiagramRelationSysService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-07-15 10:28
 */
@RestController
@RequestMapping("/diagramRelationSys")
public class EamDiagramRelationSysController {

    @Resource
    private EamDiagramRelationSysService eamDiagramRelationSysService;

    @GetMapping("/updateDiagramRelationSys")
    private RemoteResult updateDiagramRelationSys(@RequestParam Long dirId) {
        List<EamDiagramRelationSys> list =  eamDiagramRelationSysService.updateDiagramRelationSys(dirId);
        return new RemoteResult(list);
    }

}
