package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.comm.bean.DirSettingBO;
import com.uinnova.product.eam.comm.bean.DirSettingDTO;
import com.uinnova.product.eam.comm.model.es.ClassSetting;
import com.uinnova.product.eam.model.dmv.ClassDefinitionVO;
import com.uinnova.product.eam.service.IOperateSettingService;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * 操作设置系统文件夹配置信息
 */
@Controller
@RequestMapping("/eam/operateSetting")
public class OperateSettingMvc {


    @Autowired
    private IOperateSettingService operateSettingService;

    /**
     * 添加操作设置的领域配置
     * @param request
     * @param response
     * @param classSetting
     */
    @PostMapping("/saveClassSetting")
    public void saveClassSetting(HttpServletRequest request, HttpServletResponse response, @RequestBody ClassSetting classSetting) {
        Long result = operateSettingService.saveClassSetting(classSetting);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping("/saveClassSettingList")
    public void saveClassSettingList(HttpServletRequest request, HttpServletResponse response, @RequestBody List<ClassSetting> classSettings) {
        Integer result = operateSettingService.saveClassSettingList(classSettings);
        ControllerUtils.returnJson(request, response, result);
    }



    /**
     * 获取分类配置列表
     * @param request
     * @param response
     */
    @GetMapping("/getClassSettingList")
    public void getClassSettingList(HttpServletRequest request, HttpServletResponse response,@RequestParam(required = false) Long artifactId) {
        List<ClassSetting> result = operateSettingService.getClassSettingList(artifactId);
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 获取分类对应的对象定义
     * @param request
     * @param response
     */
    @GetMapping("/findClassDefinitionList")
    public void findClassDefinitionList(HttpServletRequest request, HttpServletResponse response) {
        List<ClassDefinitionVO> attrDefInfos = operateSettingService.findClassDefinitionList();
        ControllerUtils.returnJson(request, response, attrDefInfos);
    }

    /**
     * 根据id删除领域配置
     * @param request
     * @param response
     * @param id
     */
    @GetMapping("/deleteClassSettingById")
    public void deleteClassSettingById(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        Assert.isTrue(id!= null,"要删除的领域id不能为空");
        Integer result = operateSettingService.deleteClassSettingById(id);
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 添加分类下面的目录
     * @param request
     * @param response
     * @param dirSettingBO dirSettingBO
     */
    @PostMapping("/addDirSetting")
    public void addDirSetting(HttpServletRequest request, HttpServletResponse response, @RequestBody DirSettingBO dirSettingBO) {
        Boolean result = operateSettingService.addDirSetting(dirSettingBO);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/judgeDirLevel")
    public void judgeDirLevel(HttpServletRequest request, HttpServletResponse response, @RequestParam Long parentId) {
        Integer result = operateSettingService.judgeDirLevel(parentId);
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 根据分类id获取下面的目录配置
     * @param request
     * @param response
     * @param classId
     */
    @GetMapping("/getDirSettingByClassId")
    public void getDirSettingByClassId(HttpServletRequest request, HttpServletResponse response, @RequestParam Long classId) {
        List<DirSettingDTO> result = operateSettingService.getDirSettingByClassId(classId);
        ControllerUtils.returnJson(request, response, result);
    }

    @ResponseBody
    @PostMapping("getSettings")
    public List<ClassSetting> getSettings() {
        return operateSettingService.getEsSettings();
    }


    @ResponseBody
    @GetMapping("/addPlanBindClass")
    public void addPlanBindClass(HttpServletRequest request, HttpServletResponse response, @RequestParam Long classId) {
        Boolean result = operateSettingService.addPlanBindClass(classId);
        ControllerUtils.returnJson(request, response, result);
    }


}
