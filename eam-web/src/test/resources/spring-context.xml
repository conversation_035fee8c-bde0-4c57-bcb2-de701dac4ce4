<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
<!--	 数据库用户名和密码加密-->
	  <bean id="propertyConfigurer" class="com.uino.tarsier.tarsiercom.util.PropertyPlaceholderConfigurerExt">
		  <property name="locations">
			<list>
				<value>classpath:application-local.properties</value>
				<value>classpath:application-minor.properties</value>
			</list>
		</property>
		  <property name="fileEncoding" value="UTF-8"/>
		  <property name="order" value="1" />
	 </bean>
	
	
<!--	<import resource="classpath:spring/spring-binarys-sys-frame.xml" />-->
<!--	<import resource="classpath:spring/spring-binarys-sys-mvc.xml" />-->
<!--	<import resource="classpath:spring/spring-binarys-sys-biz-impl.xml" />-->
<!--	<import resource="classpath:spring/spring-vmdb-comm.xml" />
	<import resource="classpath:spring/spring-impl-vmdb.xml"/>-->
	<import resource="classpath:spring/spring-iams-web.xml" />
</beans>


