[{"uid": "meta-model-bj", "name": "元模型管理-编辑", "dataValue": "meta-model-bj"}, {"uid": "meta-model-add", "name": "元模型管理-新建", "dataValue": "meta-model-add"}, {"uid": "meta-model-import", "name": "元模型管理-导入", "dataValue": "meta-model-import"}, {"uid": "artifact-edit", "name": "制品类型管理-编辑", "dataValue": "artifact-edit"}, {"uid": "deliverable-template-edit", "name": "交付物模板管理-编辑", "dataValue": "deliverable-template-edit"}, {"uid": "modeling-craft-edit", "name": "建模工艺管理-编辑", "dataValue": "modeling-craft-edit"}, {"uid": "asset-management-config", "name": "信息资产管理-配置", "dataValue": "asset-management-config"}, {"uid": "warehouse-management-config", "name": "设计资产管理-配置", "dataValue": "warehouse-management-config"}, {"uid": "architecture-map-config", "name": "架构地图-配置", "dataValue": "architecture-map-config"}, {"uid": "architecture-data-config", "name": "架构地图数据-配置", "dataValue": "architecture-data-config"}, {"uid": "topic-analysis-config", "name": "专题分析-配置", "dataValue": "topic-analysis-config"}, {"uid": "free-diagram-create", "name": "自由绘图-新建", "dataValue": "free-diagram-create"}, {"uid": "process-system-permission", "name": "流程体系-权限", "dataValue": "process-system-permission"}, {"uid": "process-system-add-flow", "name": "流程体系-添加流程架构", "dataValue": "process-system-add-flow"}, {"uid": "process-system-add-group", "name": "流程体系-添加流程组", "dataValue": "process-system-add-group"}, {"uid": "process-system-add-node", "name": "流程体系-添加末级流程", "dataValue": "process-system-add-node"}, {"uid": "process-system-export-flow-map", "name": "流程体系-导出流程地图", "dataValue": "process-system-export-flow-map"}, {"uid": "process-system-save-flow", "name": "流程体系-流程-提交", "dataValue": "process-system-save-flow"}, {"uid": "process-system-check-flow", "name": "流程体系-流程-校验", "dataValue": "process-system-check-flow"}, {"uid": "process-system-check-group", "name": "流程体系-流程组-提交", "dataValue": "process-system-check-group"}, {"uid": "process-system-save-group", "name": "流程体系-流程组-保存", "dataValue": "process-system-save-group"}, {"uid": "process-system-delete-tree", "name": "流程体系-删除流程树", "dataValue": "process-system-delete-tree"}, {"uid": "process-system-invalid-risk", "name": "流程体系-流程风险作废", "dataValue": "process-system-invalid-risk"}, {"uid": "process-system-change-risk", "name": "流程体系-流程风险变更", "dataValue": "process-system-change-risk"}, {"uid": "process-system-add-risk", "name": "流程体系-添加流程风险", "dataValue": "process-system-add-risk"}, {"uid": "process-system-invalid-performance", "name": "流程体系-流程绩效作废", "dataValue": "process-system-invalid-performance"}, {"uid": "process-system-change-performance", "name": "流程体系-流程绩效变更", "dataValue": "process-system-change-performance"}, {"uid": "process-system-performance-monitor-edit", "name": "流程体系-流程绩效-维护监测值", "dataValue": "process-system-performance-monitor-edit"}, {"uid": "process-system-add-performance", "name": "流程体系-添加流程绩效", "dataValue": "process-system-add-performance"}, {"uid": "process-system-archive-invalid", "name": "流程体系-档案管理作废", "dataValue": "process-system-archive-invalid"}, {"uid": "process-system-archive-change", "name": "流程体系-档案管理变更", "dataValue": "process-system-archive-change"}, {"uid": "process-system-add-archive", "name": "流程体系-添加档案管理", "dataValue": "process-system-add-archive"}, {"uid": "process-system-group-move", "name": "流程体系-流程组移动", "dataValue": "process-system-group-move"}, {"uid": "process-system-move", "name": "流程体系-流程移动", "dataValue": "process-system-move"}, {"uid": "process-system-rename-node", "name": "流程体系-流程组重命名", "dataValue": "process-system-rename-node"}, {"uid": "process-system-group-rename-node", "name": "流程体系-流程重命名", "dataValue": "process-system-group-rename-node"}, {"uid": "process-system-import-flow-file", "name": "流程体系-导入", "dataValue": "process-system-import-flow-file"}, {"uid": "process-system-export-flow-file", "name": "流程体系-导出流程文件", "dataValue": "process-system-export-flow-file"}, {"uid": "process-system-property-edit", "name": "流程体系-流程属性编辑", "dataValue": "process-system-property-edit"}, {"uid": "process-system-property-config", "name": "流程体系-流程属性配置", "dataValue": "process-system-property-config"}, {"uid": "process-system-graph-edit", "name": "流程体系-流程图编辑", "dataValue": "process-system-graph-edit"}, {"uid": "process-system-graph-preview", "name": "流程体系-流程图预览", "dataValue": "process-system-graph-preview"}, {"uid": "process-system-integration-graph-edit", "name": "流程体系-集成关系图编辑", "dataValue": "process-system-integration-graph-edit"}, {"uid": "process-system-integration-graph-preview", "name": "流程体系-集成关系图预览", "dataValue": "process-system-integration-graph-preview"}, {"uid": "process-system-schema-card-edit", "name": "流程体系-架构卡编辑", "dataValue": "process-system-schema-card-edit"}, {"uid": "process-system-cutting-guide-view", "name": "流程体系-裁剪指南查看详情", "dataValue": "process-system-cutting-guide-view"}, {"uid": "process-system-cutting-guide-invalid", "name": "流程体系-裁剪指南作废", "dataValue": "process-system-cutting-guide-invalid"}, {"uid": "process-system-cutting-guide-config", "name": "流程体系-裁剪指南配置", "dataValue": "process-system-cutting-guide-config"}, {"uid": "process-system-cutting-guide-edit", "name": "流程体系-裁剪指南编辑", "dataValue": "process-system-cutting-guide-edit"}, {"uid": "process-system-add-cutting-guide", "name": "流程体系-裁剪指南添加", "dataValue": "process-system-add-cutting-guide"}, {"uid": "institutional-system-edit", "name": "制度体系-编辑", "dataValue": "institutional-system-edit"}, {"uid": "standard-system-edit", "name": "标准体系-编辑", "dataValue": "standard-system-edit"}, {"uid": "organization-system-edit", "name": "组织体系-编辑", "dataValue": "organization-system-edit"}, {"uid": "form-library-edit", "name": "表单库-编辑", "dataValue": "form-library-edit"}, {"uid": "term-edit", "name": "术语-编辑", "dataValue": "term-edit"}, {"uid": "risk-system-edit", "name": "风险体系-编辑", "dataValue": "risk-system-edit"}, {"uid": "role-library-edit", "name": "角色库-编辑", "dataValue": "role-library-edit"}, {"uid": "process-panorama-edit", "name": "流程全景-编辑", "dataValue": "process-panorama-edit"}, {"uid": "process-panorama-auto-graph", "name": "流程全景-自动成图", "dataValue": "process-panorama-auto-graph"}, {"uid": "process-tree-sign", "name": "流程树-发起签发", "dataValue": "process-tree-sign"}, {"uid": "process-system-approve-flow", "name": "流程体系-流程-发布", "dataValue": "process-system-approve-flow"}, {"uid": "process-system-rollback-flow", "name": "流程体系-流程-撤回", "dataValue": "process-system-rollback-flow"}, {"uid": "process-system-update-flow", "name": "流程体系-流程-升版", "dataValue": "process-system-update-flow"}, {"uid": "process-system-update-flow-cancel", "name": "流程体系-流程-取消升版", "dataValue": "process-system-update-flow-cancel"}, {"uid": "process-system-abolish-flow", "name": "流程体系-流程-废止", "dataValue": "process-system-abolish-flow"}, {"uid": "c2c-directory-tree-edit", "name": "端到端流程-目录树-编辑", "dataValue": "c2c-directory-tree-edit"}, {"uid": "c2c-flow-edit", "name": "端到端流程-编辑", "dataValue": "c2c-flow-edit"}, {"uid": "c2c-flow-save", "name": "端到端流程-保存", "dataValue": "c2c-flow-save"}, {"uid": "c2c-flow-check", "name": "端到端流程-校验", "dataValue": "c2c-flow-check"}, {"uid": "c2c-flow-delete", "name": "端到端流程-删除", "dataValue": "c2c-flow-delete"}, {"uid": "c2c-flow-diagram-edit", "name": "端到端流程-架构图-编辑", "dataValue": "c2c-flow-diagram-edit"}, {"uid": "c2c-flow-diagram-preview", "name": "端到端流程-架构图-预览", "dataValue": "c2c-flow-diagram-preview"}, {"uid": "process-panorama-full-screen", "name": "流程全景-全屏模式", "dataValue": "process-panorama-full-screen"}, {"uid": "maturity-assessment-view", "name": "成熟度评价-查看", "dataValue": "maturity-assessment-view"}, {"uid": "maturity-assessment-config", "name": "成熟度评价-配置", "dataValue": "maturity-assessment-config"}, {"uid": "process-system-export-business-domain-manual", "name": "流程体系-导出业务域手册", "dataValue": "process-system-export-business-domain-manual"}, {"uid": "information-asset-management-config", "name": "信息资产管理-配置", "dataValue": "information-asset-management-config"}, {"uid": "architecture-map-config", "name": "架构地图-配置", "dataValue": "architecture-map-config"}]