<?xml version="1.0" encoding="UTF-8"?>
<!--      Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，
            当设置成trace时，可以看到log4j2内部各种详细输出
-->
<!-- monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数 -->
<configuration monitorInterval="5">
    <!-- 日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->

    <!-- 变量配置 -->
    <Properties>
        <!--
            格式化输出：
            %d表示日期，
            %thread表示线程名，
            %-5level：级别从左显示5个字符宽度
            %msg：日志消息，%n是换行符
            %logger{36} 表示 Logger 名字最长36个字符
        -->
        <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{%-5level}[%thread] %style{%logger{36}}{cyan} : %enc{%m}{CRLF}%n" />
        <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level[%thread] %logger{36} : %enc{%m}{CRLF}%n" />
        <!-- 定义日志存储的路径 -->
        <property name="FILE_PATH" value="./logs" />
        <property name="FILE_NAME" value="eam-fuxi" />
    </Properties>

    <appenders>
        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${CONSOLE_LOG_PATTERN}" disableAnsi="false" noConsoleNoAnsi="false"/>
            <!--控制台只输出level及其以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
<!--            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>-->
        </console>

        <!--
        　　这个会打印出所有的info及以下级别的信息，每次大小超过size，
        　　则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档
        -->
        <RollingFile name="RollingFile" fileName="${FILE_PATH}/project.log" filePattern="${FILE_PATH}/${FILE_NAME}-project-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
<!--            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>-->
            <PatternLayout pattern="${FILE_LOG_PATTERN}"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次，默认是1 hour-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件开始覆盖 -->
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </appenders>
    <!--Logger节点用来单独指定日志的形式，比如要为指定包下的class指定不同的日志级别等。-->
    <!--然后定义loggers，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <AsyncLogger name="RollingFile" level="info" additivity="false">
            <appender-ref ref="RollingFile"/>
        </AsyncLogger>
        <root level="info" includeLocation="true">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFile"/>
        </root>
    </loggers>
</configuration>