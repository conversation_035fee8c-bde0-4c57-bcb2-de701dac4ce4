package com.uinnova.product.eam.feign.workable.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 任务信息
 *
 * <AUTHOR>
 * @since 2022/2/24 14:43
 */
@Data
public class TaskResponse {

    private String taskId;

    private String processInstanceId;

    private String processDefinitionKey;

    private String businessKey;

    private String processInstanceName;

    private String taskName;

    private String taskDefinitionKey;

    private String description;

    private String userId;

    private String submitter;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date processStartTime;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTime;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date taskEndTime;

    private String currentAssignees;

    /**
     * 流程是否结束，true是结束，false是未结束
     */
    private Boolean processEnd = Boolean.FALSE;

    private int errorCode;

    private String category;

    /**
     * 跟任务绑定的参数,查询任务时会携带回显（任务本地变量）
     */
    private Map<String, Object> taskEchoVariables;
}
