package com.uinnova.product.eam.feign.workable;

import com.uinnova.product.eam.feign.workable.entity.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 流程Feign接口
 *
 * <AUTHOR>
 * @since 2022/2/22 15:21
 */
@FeignClient(name = "${flowable-feign-server-name:eam-flowable}"
        , path = "${flowable-feign-server-path:eam-flowable}/flowable")
public interface FlowableFeign {

    /**
     * 开启一个流程并绑定发起人
     * @param processRequest
     * @return
     */
    @PostMapping("/startProcessBindAssignee")
    PorcessResponse startProcessBindAssignee(@RequestBody ProcessRequest processRequest);

    /**
     * 删除流程
     * @param processRequest
     */
    @DeleteMapping("/deleteProcessInstanceByBusinessId")
    void deleteProcessInstanceByBusinessId(@RequestBody ProcessRequest processRequest);

    @DeleteMapping("/deleteProcessInstanceById")
    void deleteProcessInstanceById(@RequestBody ProcessRequest processRequest);

    /**
     * 获取用户代办列表
     * @param userId
     * @param pageSize
     * @param pageNum
     * @return
     */
    @GetMapping("/getTaskByUserId")
    PageEntityInfo<TaskResponse> getTaskListByUserId(@RequestParam("userId") String userId, @RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum);


    /**
     * 根据业务主键获取历史流程列表
     *
     * @param processRequest
     * @return 历史流程列表
     */
    @PostMapping("/getTaskListByBusinessId")
    public List<PorcessResponse> getTaskListByBusinessId(@RequestBody ProcessRequest processRequest);

    /**
     * 获取用户已办列表
     * @param userId
     * @param pageSize
     * @param pageNum
     * @return
     */
    @GetMapping("/getDoneTaskByUserId")
    PageEntityInfo<TaskResponse> getDoneTaskListByUserId(@RequestParam("userId") String userId, @RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum);

    @GetMapping("/getDoneAllTask")
    public List<TaskResponse> getDoneAllTask();

    @GetMapping("/getCurrentAllTask")
    public List<TaskResponse> getCurrentAllTask();

    /**
     * 根据角色获取代办列表
     * @param userRole
     * @return
     */
    @GetMapping("/getTaskByUserRoleId")
    List<TaskResponse> getTaskListByRoleId(@RequestParam("userRole") String userRole);

    /**
     * 多角色获取列表
     * @param userRoles
     * @return
     */
    @PostMapping("/getTaskByUserRoleId")
    List<TaskResponse> getTaskListByRoleIds(List<String> userRoles);

    /**
     * 执行审批
     * @param taskRequest
     * @return
     */
    @PostMapping("/completeTask")
    TaskResponse completeTask(TaskRequest taskRequest);

    /**
     * 生成缩略图
     * @param processInstanceId
     * @return
     */
    @GetMapping("/genProcessDiagram")
    String genProcessDiagram(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 判断流程是否存在
     * @param processInstanceId
     * @return
     */
    @GetMapping("/processInstanceISExist")
    Boolean processInstanceISExist(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 根据流程实例id查询流程实例信息，可用户查询历史数据
     * @param processInstanceId
     * @return
     */
    @GetMapping("/getProcessInstanceByProcessInstanceId")
    PorcessResponse getProcessInstanceByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 根据业务主键和流程定义可能会查到历史，所以仅限于查询未结束的流程
     * 结束流程请用
     * @see FlowableFeign#getProcessInstanceByProcessInstanceId(String)
     * @param businessId
     * @param processDefinitionKey
     * @return
     */
    @GetMapping("/getProcessInstanceByBusinessIdAndProcessDefinitionKey")
    PorcessResponse getProcessInstanceByBusinessIdAndProcessDefinitionKey(@RequestParam("businessId") String businessId,
                                                                          @RequestParam("processDefinitionKey") String processDefinitionKey);

    /**
     * 获取流程历史审批
     * @param processInstanceId
     * @return
     */
    @GetMapping("/getHistoryTaskByCurrentProcessInstanceId")
    List<HistoryTaskResponse> getHistoryTaskByCurrentProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 查询历史节点和当前审批节点信息
     * @param processInstanceId
     * @return
     */
    @GetMapping("/getCurrentTaskByCurrentProcessInstanceId")
    public List<TaskResponse> getCurrentTaskByCurrentProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 获取任务信息
     * @param taskId
     * @return
     */
    @GetMapping("/getTaskInfoByTaskId")
    TaskResponse getTaskInfoByTaskId(@RequestParam("taskId") String taskId);

    /**
     * 获取当前用户代代办数量
     * @param userId
     * @return
     */
    @GetMapping("/getTaskCountByUserId")
    long getTaskCountByUserId(@RequestParam("userId") String userId);

    /**
     * 获取当前任务任务定义
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    @GetMapping("/getCurrentTaskDefinitionInfo")
    TaskResponse getCurrentTaskDefinitionInfo(@RequestParam("businessKey") String businessKey, @RequestParam("processDefinitionKey") String processDefinitionKey);

    /**
     * 获取当前任务节点所有的代办人
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    @GetMapping("/getCurrentTaskAssignees")
    TaskResponse getCurrentTaskAssignees(@RequestParam("businessKey") String businessKey, @RequestParam("processDefinitionKey") String processDefinitionKey);

    /**
     * 获取当前人对用的流程业务主键查看当前唯一任务
     * @param businessKey
     * @param processDefinitionKey
     * @param userCode
     * @return
     */
    @GetMapping("/getCurrentUserTask")
    public TaskResponse getCurrentUserTask(@RequestParam String businessKey, @RequestParam String processDefinitionKey,
                                           @RequestParam String userCode);

    /**
     * 获取当前流程存活的任务
     * @param processInstanceId
     * @return
     */
    @GetMapping("/getActiveTaskByProcessInstanceId")
    public List<TaskResponse> getActiveTaskByProcessInstanceId(@RequestParam String processInstanceId);

    /**
     * 根据流程实例id终止流程实例
     * @param processInstanceId
     * @param TerminationReason
     * @return
     */
    @GetMapping("/terminateProcessInstanceById")
    public Boolean terminateProcessInstanceById(@RequestParam String processInstanceId,@RequestParam(required = false) String TerminationReason);

    /**
     * 获取方案拥有者对用的流程业务主键查看当前唯一任务
     * @param businessKey
     * @param userCode
     * @return
     */
    @GetMapping("/getCurrentOwnerTask")
    public TaskResponse getCurrentOwnerTask(@RequestParam String businessKey, @RequestParam String userCode);

    @GetMapping("/selectFlowStatus")
    public Boolean selectFlowStatus(@RequestParam String businessKey, @RequestParam String processDefinitionKey);

    @PostMapping("/getDoneTaskList")
    List<TaskResponse> getDoneTaskList(@RequestParam("userId") String userId, @RequestBody List<String> processInstanceIds);

    @GetMapping("/isExistByTaskId")
    Boolean isExistByTaskId(@RequestParam String taskId);
}
