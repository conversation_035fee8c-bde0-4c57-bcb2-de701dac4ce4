package com.uinnova.product.eam.feign.workable.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/3/4 15:49
 */
@Data
public class PageEntityInfo<T> implements Serializable {
    private int pageNum;
    private int pageSize;
    private int size;
    private long pages;
    private int prePage;
    private int nextPage;
    private boolean isFirstPage;
    private boolean isLastPage;
    private boolean hasPreviousPage;
    private boolean hasNextPage;
    private List<T> list;
    private long total;
}
