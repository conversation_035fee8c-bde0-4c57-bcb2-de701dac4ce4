package com.uinnova.product.eam.workable.service.impl;

import com.uinnova.product.eam.workable.model.FilterUser;
import com.uinnova.product.eam.workable.repository.FilterUsersRepository;
import com.uinnova.product.eam.workable.service.FilterUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.AlternativeJdkIdGenerator;
import org.springframework.util.IdGenerator;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FilterUserServiceImpl implements FilterUserService {

    @Autowired
    private FilterUsersRepository filterUsersRepository;

    @Override
    public String saveAgreeUser(FilterUser filterUser) {
        if (filterUser.getId() == null) {
            //这块以后需要生成32位uuid
            filterUser.setId(getUUID());
            filterUser.setCreateTime(System.currentTimeMillis());
            filterUser.setModifyTime(System.currentTimeMillis());
            filterUser.setDomainId(1);
        }else{
            filterUser.setModifyTime(System.currentTimeMillis());
        }
        FilterUser save = filterUsersRepository.save(filterUser);
        return save.getId();
    }

    @Override
    public List<FilterUser> getCurrentTaskAgreeUser(String processInstanceId, String taskKey) {
        List<FilterUser> result = filterUsersRepository
                .findFilterUserByFlowableInstanceIdAndFlowableTaskKey(processInstanceId, taskKey);
        return result;
    }

    @Override
    public void deleteFilterUserByProcessInstanceId(String processInstanceId) {
        filterUsersRepository.deleteFilterUserByFlowableInstanceId(processInstanceId);
    }

    public String getUUID() {
        IdGenerator idGenerator = new AlternativeJdkIdGenerator();
        return idGenerator.generateId().toString().replace("-", "");
    }
}
