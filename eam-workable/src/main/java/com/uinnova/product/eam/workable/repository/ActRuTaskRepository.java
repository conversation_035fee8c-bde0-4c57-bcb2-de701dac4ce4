package com.uinnova.product.eam.workable.repository;

import com.uinnova.product.eam.workable.model.ActRuTask;
import com.uinnova.product.eam.workable.model.FilterUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * flowable任务表的dao
 *
 * <AUTHOR>
 * @since 2022/3/20 18:18
 */
public interface ActRuTaskRepository extends JpaRepository<ActRuTask, String> {


    List<ActRuTask> findActRuTaskByAssigneeAndProcInstIdAndSuspensionState(String assignee, String procInstId, Integer suspensionState);

}
