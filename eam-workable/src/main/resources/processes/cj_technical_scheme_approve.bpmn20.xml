<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="cj_technical_scheme_approve" name="cj_technical_scheme_approve" isExecutable="true">
    <documentation>仓颉技术方案审批流程-添加取消流程</documentation>
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-E99DE732-055F-4B18-8C03-5E8803560F72" name="提交人（处理）" flowable:category="submit" flowable:formFieldValidation="true"></userTask>
    <exclusiveGateway id="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F"></exclusiveGateway>
    <userTask id="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8" name="一级审批组（审批）" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A"></exclusiveGateway>
    <userTask id="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4" name="提交人（处理）" flowable:assignee="$INITIATOR" flowable:category="submit" flowable:formFieldValidation="true">
      <documentation>rectification</documentation>
      <extensionElements>
        <flowable:executionListener event="start" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
        <modeler:activiti-idm-initiator xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-initiator>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-F2602296-C98B-4CCA-9203-0A19BB251975" sourceRef="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4" targetRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F"></sequenceFlow>
    <endEvent id="sid-540F7416-E10E-4A4E-A406-55BB2705BE7E">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <endEvent id="sid-F1A24DE1-F3FC-4F0E-B21A-25F0017EF6B8">
      <terminateEventDefinition></terminateEventDefinition>
    </endEvent>
    <sequenceFlow id="sid-83836B10-FD6A-4A00-ADF2-58249D5544DE" sourceRef="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8" targetRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A"></sequenceFlow>
    <sequenceFlow id="sid-206CA60A-2285-4144-9374-D7D9BA9429A1" name="驳回" sourceRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" targetRef="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${updateDiagramStatusListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-34395A7C-50A6-4319-B666-A4E80850BDEE" sourceRef="startEvent1" targetRef="sid-E99DE732-055F-4B18-8C03-5E8803560F72"></sequenceFlow>
    <sequenceFlow id="sid-FDA47F47-BB1C-4FFB-96F9-EC5F83A130AE" sourceRef="sid-E99DE732-055F-4B18-8C03-5E8803560F72" targetRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F"></sequenceFlow>
    <sequenceFlow id="sid-51360696-D796-4E34-89DA-9906A9637126" name="同意" sourceRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F" targetRef="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8">
      <documentation>一级审批角色</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${generateUserByRoleListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-EF7EFC62-9828-413A-8F2B-DD394649DC8B" name="同意" sourceRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" targetRef="sid-540F7416-E10E-4A4E-A406-55BB2705BE7E">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-4354F616-2FBB-4C47-BD97-45D08A2EC566" name="取消" sourceRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" targetRef="sid-F1A24DE1-F3FC-4F0E-B21A-25F0017EF6B8">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-ED45618A-ADF4-4452-9EF7-2A25FCA105F7" name="取消" sourceRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F" targetRef="sid-F1A24DE1-F3FC-4F0E-B21A-25F0017EF6B8">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_cj_technical_scheme_approve">
    <bpmndi:BPMNPlane bpmnElement="cj_technical_scheme_approve" id="BPMNPlane_cj_technical_scheme_approve">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="45.0" y="315.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E99DE732-055F-4B18-8C03-5E8803560F72" id="BPMNShape_sid-E99DE732-055F-4B18-8C03-5E8803560F72">
        <omgdc:Bounds height="80.0" width="100.0" x="120.0" y="290.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F" id="BPMNShape_sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F">
        <omgdc:Bounds height="40.0" width="40.0" x="265.0" y="310.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8" id="BPMNShape_sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="290.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" id="BPMNShape_sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A">
        <omgdc:Bounds height="40.0" width="40.0" x="495.0" y="310.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4" id="BPMNShape_sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4">
        <omgdc:Bounds height="80.0" width="100.0" x="235.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-540F7416-E10E-4A4E-A406-55BB2705BE7E" id="BPMNShape_sid-540F7416-E10E-4A4E-A406-55BB2705BE7E">
        <omgdc:Bounds height="28.0" width="28.0" x="674.9999597668666" y="315.9999905824664"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F1A24DE1-F3FC-4F0E-B21A-25F0017EF6B8" id="BPMNShape_sid-F1A24DE1-F3FC-4F0E-B21A-25F0017EF6B8">
        <omgdc:Bounds height="28.0" width="28.0" x="270.99999192357086" y="434.99998703599016"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-F2602296-C98B-4CCA-9203-0A19BB251975" id="BPMNEdge_sid-F2602296-C98B-4CCA-9203-0A19BB251975">
        <omgdi:waypoint x="285.00324232081914" y="184.95"></omgdi:waypoint>
        <omgdi:waypoint x="285.43150684931504" y="310.4315068493151"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-ED45618A-ADF4-4452-9EF7-2A25FCA105F7" id="BPMNEdge_sid-ED45618A-ADF4-4452-9EF7-2A25FCA105F7">
        <omgdi:waypoint x="285.5" y="349.44127532225"></omgdi:waypoint>
        <omgdi:waypoint x="285.5" y="434.99998703599016"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-206CA60A-2285-4144-9374-D7D9BA9429A1" id="BPMNEdge_sid-206CA60A-2285-4144-9374-D7D9BA9429A1">
        <omgdi:waypoint x="515.5" y="310.5"></omgdi:waypoint>
        <omgdi:waypoint x="515.5" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="334.95000000000005" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4354F616-2FBB-4C47-BD97-45D08A2EC566" id="BPMNEdge_sid-4354F616-2FBB-4C47-BD97-45D08A2EC566">
        <omgdi:waypoint x="515.5" y="349.4420109612142"></omgdi:waypoint>
        <omgdi:waypoint x="515.5" y="449.0"></omgdi:waypoint>
        <omgdi:waypoint x="298.9499138589091" y="448.9999878205799"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FDA47F47-BB1C-4FFB-96F9-EC5F83A130AE" id="BPMNEdge_sid-FDA47F47-BB1C-4FFB-96F9-EC5F83A130AE">
        <omgdi:waypoint x="219.94999999999771" y="330.21623376623376"></omgdi:waypoint>
        <omgdi:waypoint x="265.4130434782609" y="330.4130434782609"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-83836B10-FD6A-4A00-ADF2-58249D5544DE" id="BPMNEdge_sid-83836B10-FD6A-4A00-ADF2-58249D5544DE">
        <omgdi:waypoint x="444.9499999999953" y="330.20726141078836"></omgdi:waypoint>
        <omgdi:waypoint x="495.4166666666667" y="330.4166666666667"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-34395A7C-50A6-4319-B666-A4E80850BDEE" id="BPMNEdge_sid-34395A7C-50A6-4319-B666-A4E80850BDEE">
        <omgdi:waypoint x="74.94999848995758" y="330.0"></omgdi:waypoint>
        <omgdi:waypoint x="120.0" y="330.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-51360696-D796-4E34-89DA-9906A9637126" id="BPMNEdge_sid-51360696-D796-4E34-89DA-9906A9637126">
        <omgdi:waypoint x="304.5281622364675" y="330.41284403669727"></omgdi:waypoint>
        <omgdi:waypoint x="344.9999999999942" y="330.22808219178086"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EF7EFC62-9828-413A-8F2B-DD394649DC8B" id="BPMNEdge_sid-EF7EFC62-9828-413A-8F2B-DD394649DC8B">
        <omgdi:waypoint x="534.499264476651" y="330.4450856551464"></omgdi:waypoint>
        <omgdi:waypoint x="675.0000147342936" y="330.04019302112556"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
