<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:34 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_DIR_RELATION">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramDirRelation">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="dirId" column="DIR_ID" jdbcType="BIGINT"/>	<!-- 目录ID -->
		<result property="dirType" column="DIR_TYPE" jdbcType="INTEGER"/>	<!-- 目录类型 -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图ID -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dirId != null">and
			DIR_ID = #{cdt.dirId:BIGINT}
		</if>
		<if test="dirIds != null and dirIds != ''">and
			DIR_ID in (${dirIds})
		</if>
		<if test="cdt != null and cdt.startDirId != null">and
			 DIR_ID &gt;= #{cdt.startDirId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDirId != null">and
			 DIR_ID &lt;= #{cdt.endDirId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dirType != null">and
			DIR_TYPE = #{cdt.dirType:INTEGER}
		</if>
		<if test="dirTypes != null and dirTypes != ''">and
			DIR_TYPE in (${dirTypes})
		</if>
		<if test="cdt != null and cdt.startDirType != null">and
			 DIR_TYPE &gt;= #{cdt.startDirType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDirType != null">and
			 DIR_TYPE &lt;= #{cdt.endDirType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			 DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			 DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.dirId != null"> 
			DIR_ID = #{record.dirId:BIGINT}
		,</if>
		<if test="record != null and record.dirType != null"> 
			DIR_TYPE = #{record.dirType:INTEGER}
		,</if>
		<if test="record != null and record.diagramId != null"> 
			DIAGRAM_ID = #{record.diagramId:BIGINT}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, DIR_ID, DIR_TYPE, DIAGRAM_ID, DOMAIN_ID, CREATE_TIME, 
		MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_DIR_RELATION.sql_query_columns"/>
		from VC_DIAGRAM_DIR_RELATION 
			<where>
				<include refid="VC_DIAGRAM_DIR_RELATION.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_DIR_RELATION 
			<where>
				<include refid="VC_DIAGRAM_DIR_RELATION.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_DIR_RELATION.sql_query_columns"/>
		from VC_DIAGRAM_DIR_RELATION where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_DIR_RELATION(
			ID, DIR_ID, DIR_TYPE, DIAGRAM_ID, DOMAIN_ID, 
			CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.dirId:BIGINT}, #{record.dirType:INTEGER}, #{record.diagramId:BIGINT}, #{record.domainId:BIGINT}, 
			#{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_DIR_RELATION
			<set> 
				<include refid="VC_DIAGRAM_DIR_RELATION.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_DIR_RELATION
			<set> 
				<include refid="VC_DIAGRAM_DIR_RELATION.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_DIAGRAM_DIR_RELATION.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_DIR_RELATION where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_DIR_RELATION
			<where> 
				<include refid="VC_DIAGRAM_DIR_RELATION.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>