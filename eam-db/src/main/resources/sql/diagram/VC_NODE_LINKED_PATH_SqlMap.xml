<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:39 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_NODE_LINKED_PATH">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcNodeLinkedPath">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="linkedId" column="LINKED_ID" jdbcType="BIGINT"/>	<!-- 节点路径ID -->
		<result property="nodeCodes" column="NODE_CODES" jdbcType="VARCHAR"/>	<!-- 节点代码列表 -->
		<result property="pathOrder" column="PATH_ORDER" jdbcType="INTEGER"/>	<!-- 节点列表序号 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.linkedId != null">and
			LINKED_ID = #{cdt.linkedId:BIGINT}
		</if>
		<if test="linkedIds != null and linkedIds != ''">and
			LINKED_ID in (${linkedIds})
		</if>
		<if test="cdt != null and cdt.startLinkedId != null">and
			 LINKED_ID &gt;= #{cdt.startLinkedId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endLinkedId != null">and
			 LINKED_ID &lt;= #{cdt.endLinkedId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.nodeCodes != null and cdt.nodeCodes != ''">and
			NODE_CODES like #{cdt.nodeCodes,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.pathOrder != null">and
			PATH_ORDER = #{cdt.pathOrder:INTEGER}
		</if>
		<if test="pathOrders != null and pathOrders != ''">and
			PATH_ORDER in (${pathOrders})
		</if>
		<if test="cdt != null and cdt.startPathOrder != null">and
			 PATH_ORDER &gt;= #{cdt.startPathOrder:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endPathOrder != null">and
			 PATH_ORDER &lt;= #{cdt.endPathOrder:INTEGER} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.linkedId != null"> 
			LINKED_ID = #{record.linkedId:BIGINT}
		,</if>
		<if test="record != null and record.nodeCodes != null"> 
			NODE_CODES = #{record.nodeCodes,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.pathOrder != null"> 
			PATH_ORDER = #{record.pathOrder:INTEGER}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, LINKED_ID, NODE_CODES, PATH_ORDER, DOMAIN_ID, CREATE_TIME, 
		MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_NODE_LINKED_PATH.sql_query_columns"/>
		from VC_NODE_LINKED_PATH 
			<where>
				<include refid="VC_NODE_LINKED_PATH.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_NODE_LINKED_PATH 
			<where>
				<include refid="VC_NODE_LINKED_PATH.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_NODE_LINKED_PATH.sql_query_columns"/>
		from VC_NODE_LINKED_PATH where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_NODE_LINKED_PATH(
			ID, LINKED_ID, NODE_CODES, PATH_ORDER, DOMAIN_ID, 
			CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.linkedId:BIGINT}, #{record.nodeCodes,jdbcType=VARCHAR}, #{record.pathOrder:INTEGER}, #{record.domainId:BIGINT}, 
			#{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_NODE_LINKED_PATH
			<set> 
				<include refid="VC_NODE_LINKED_PATH.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_NODE_LINKED_PATH
			<set> 
				<include refid="VC_NODE_LINKED_PATH.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_NODE_LINKED_PATH.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_NODE_LINKED_PATH where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_NODE_LINKED_PATH
			<where> 
				<include refid="VC_NODE_LINKED_PATH.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>