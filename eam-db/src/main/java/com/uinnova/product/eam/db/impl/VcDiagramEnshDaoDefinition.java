package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramEnsh;
import com.uinnova.product.eam.comm.model.VcDiagramEnsh;


/**
 * 视图收藏表[VC_DIAGRAM_ENSH]数据访问对象定义实现
 */
public class VcDiagramEnshDaoDefinition implements DaoDefinition<VcDiagramEnsh, CVcDiagramEnsh> {


	@Override
	public Class<VcDiagramEnsh> getEntityClass() {
		return VcDiagramEnsh.class;
	}


	@Override
	public Class<CVcDiagramEnsh> getConditionClass() {
		return CVcDiagramEnsh.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_ENSH";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcDiagramEnsh record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcDiagramEnsh cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcDiagramEnsh record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramEnsh record, String modifier) {
	}


}


