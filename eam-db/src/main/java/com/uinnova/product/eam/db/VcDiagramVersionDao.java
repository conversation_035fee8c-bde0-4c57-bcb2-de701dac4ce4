package com.uinnova.product.eam.db;


import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.db.support.dao.ComMyBatisBinaryDao;
import com.uino.bean.permission.base.SysUser;

import java.util.List;

/**
 * 视图设计版本表[VC_DIAGRAM_VERSION]数据访问对象
 */
public interface VcDiagramVersionDao extends ComMyBatisBinaryDao<VcDiagramVersion, CVcDiagramVersion> {

	public void updateDiagramVersionDescAndVersionNo(Long domainId, Long id,String versionDesc, String versionNo);

	long insertAsync(VcDiagramVersion diagramVersion, SysUser sysUser);

	int updateByIdAsync(VcDiagramVersion temp, Long oldDiagramId, String loginCode);

	int deleteByIdAsync(Long versionId, String loginCode);

	long[] insertBatchAsync(List<VcDiagramVersion> diagramVersionList, SysUser sysUser);

	int deleteByIds(CVcDiagramVersion versionCdt);
}


