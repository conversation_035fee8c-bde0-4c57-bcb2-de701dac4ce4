package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcNodeLinked;
import com.uinnova.product.eam.comm.model.VcNodeLinked;


/**
 * 节点链路表[VC_NODE_LINKED]数据访问对象定义实现
 */
public class VcNodeLinkedDaoDefinition implements DaoDefinition<VcNodeLinked, CVcNodeLinked> {


	@Override
	public Class<VcNodeLinked> getEntityClass() {
		return VcNodeLinked.class;
	}


	@Override
	public Class<CVcNodeLinked> getConditionClass() {
		return CVcNodeLinked.class;
	}


	@Override
	public String getTableName() {
		return "VC_NODE_LINKED";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcNodeLinked record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcNodeLinked cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcNodeLinked record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcNodeLinked record, String modifier) {
		record.setModifier(modifier);
	}


}


