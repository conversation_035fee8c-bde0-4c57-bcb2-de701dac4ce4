package com.uinnova.product.eam.db;


import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.db.support.dao.ComMyBatisBinaryDao;
import org.apache.ibatis.annotations.Param;


import java.util.List;


/**
 * 视图目录表[VC_DIAGRAM_DIR]数据访问对象
 */
@Deprecated
public interface VcDiagramDirDao extends ComMyBatisBinaryDao<VcDiagramDir, CVcDiagramDir> {

    /**
     * 通过es系统id获取mysql文件夹路径
     *
     * @param esSysId
     * @return
     */
    VcDiagramDir getDiagramDirByEsSysId(Long esSysId);

    /**
     * 查找给定系统id对应的文件夹id
     *
     * @param systemIdList
     * @return
     */
    List<Long> findDirIdListByEsSysIdList(@Param("systemIdList") List<Long> systemIdList);

    /**
     * 查询文件夹主键列表
     * @param dirType
     * @param dirIds
     * @return
     */
    List<Long> findDirIdList(Integer dirType, List<Long> dirIds);
}


