package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramOperationRecord;
import com.uinnova.product.eam.comm.model.VcDiagramOperationRecord;
import com.uinnova.product.eam.db.VcDiagramOperationRecordDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图相关操作记录表[VC_DIAGRAM_OPERATION_RECORD]数据访问对象实现
 */
public class VcDiagramOperationRecordDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramOperationRecord, CVcDiagramOperationRecord> implements VcDiagramOperationRecordDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


