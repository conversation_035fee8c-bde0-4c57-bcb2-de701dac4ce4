<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="
		  http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		  http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
          http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
          http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">
	<context:property-placeholder location="classpath:project.properties" order="1" />
	
	<bean class="com.binary.framework.util.FrameworkProperties">
		<constructor-arg>
			<map>
				<entry key="Single_Web_Root" value="${binarys.primarykey.root}" />
				<entry key="Primary_Key_Batch" value="${binarys.primarykey.batch}" />
				<entry key="Project_Local_Space" value="" />
				
				<entry key="charset" value="UTF-8"/>
			</map>
		</constructor-arg>
	</bean>
	
	
	<!-- 引用数据源 -->
	<import resource="classpath:spring-ds.xml"/>
	<import resource="classpath:spring/spring-db-iams-tpl.xml" />
	
	
</beans>


