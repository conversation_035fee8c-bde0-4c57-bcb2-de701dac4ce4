<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="
		  http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		  http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
          http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
          http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd">
          
          
	<bean class="com.binary.jdbc.JdbcOperatorFactory">
		<constructor-arg>
			<bean class="com.binary.jdbc.JdbcOperatorConfig">
				<property name="printerType" value="${ds.jdbc.diagram.printtype}" />
				<property name="printerWriterType" value="${ds.jdbc.diagram.writertype}" />
				<property name="defaultDataSource" value="${ds.jdbc.diagram.dsname}" />
				<property name="dataSourceStore">
					<list>
						<bean class="com.binary.jdbc.ds.support.PoolDataSource">
							<constructor-arg value="${ds.jdbc.diagram.dsname}" />	<!-- name -->
							<constructor-arg value="${ds.jdbc.diagram.dstype}" />	<!-- jdbcType -->
							<constructor-arg value="org.apache.commons.dbcp.BasicDataSource" />	<!-- poolClass -->
							<constructor-arg>				<!-- properties -->
								<map>
									<entry key="driverClassName" value="${ds.jdbc.diagram.driver}" />
									<entry key="url" value="${ds.jdbc.diagram.url}" />
									<entry key="username" value="${ds.jdbc.diagram.user}" />
									<entry key="password" value="${ds.jdbc.diagram.passwd}" />
									
									<entry key="initialSize" value="${ds.conn.pool.initialSize}" />
									<entry key="maxActive" value="${ds.conn.pool.maxActive}" />
									<entry key="maxIdle" value="${ds.conn.pool.maxIdle}" />
									<entry key="minIdle" value="${ds.conn.pool.minIdle}" />
									<entry key="validationQuery" value="${ds.conn.pool.validationQuery}" />
									<entry key="maxOpenPreparedStatements" value="${ds.conn.pool.maxOpenPreparedStatements}" />
								</map>
							</constructor-arg>
						</bean>
						<bean class="com.binary.jdbc.ds.support.PoolDataSource">
							<constructor-arg value="${ds.jdbc.monitor.mysql.dsname}" />	<!-- name -->
							<constructor-arg value="${ds.jdbc.monitor.mysql.dstype}" />	<!-- jdbcType -->
							<constructor-arg value="org.apache.commons.dbcp.BasicDataSource" />	<!-- poolClass -->
							<constructor-arg>				<!-- properties -->
								<map>
									<entry key="driverClassName" value="${ds.jdbc.monitor.mysql.driver}" />
									<entry key="url" value="${ds.jdbc.monitor.mysql.url}" />
									<entry key="username" value="${ds.jdbc.monitor.mysql.user}" />
									<entry key="password" value="${ds.jdbc.monitor.mysql.passwd}" />
									
									<entry key="initialSize" value="${ds.conn.pool.initialSize}" />
									<entry key="maxActive" value="${ds.conn.pool.maxActive}" />
									<entry key="maxIdle" value="${ds.conn.pool.maxIdle}" />
									<entry key="minIdle" value="${ds.conn.pool.minIdle}" />
									<entry key="validationQuery" value="${ds.conn.pool.validationQuery}" />
									<entry key="maxOpenPreparedStatements" value="${ds.conn.pool.maxOpenPreparedStatements}" />
								</map>
							</constructor-arg>
						</bean>
					</list>
				</property>
			</bean>
		</constructor-arg>
	</bean>
	
</beans>
