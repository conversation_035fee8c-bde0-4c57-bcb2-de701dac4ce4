0000000000000000000000000000000000000000 a6f8f2b89f5648a4393e1af6f6166493e4e206f8 lichong <<EMAIL>> 1748229679 +0800	branch: Created from origin/develop-v6.5.0-jdk17-spring3.4.5
a6f8f2b89f5648a4393e1af6f6166493e4e206f8 dc36ac120af3530435b35556dcd4d746ceb8e404 lichong <<EMAIL>> 1748416228 +0800	commit: fix：修复选择矩阵和视图，搜索导致历史视图被搜索出来的bug。tapd：【143全局搜索跟我的空间数据不一致，部分视图点击提示视图已删除】
dc36ac120af3530435b35556dcd4d746ceb8e404 cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 lichong <<EMAIL>> 1748416238 +0800	merge origin/develop-v6.5.0-jdk17-spring3.4.5: Merge made by the 'ort' strategy.
cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 91c947dd78f6d5c4842e5d2499e521c114b54594 lichong <<EMAIL>> 1748416748 +0800	commit: fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】
91c947dd78f6d5c4842e5d2499e521c114b54594 cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 lichong <<EMAIL>> 1748416814 +0800	reset: moving to cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9
cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 91c947dd78f6d5c4842e5d2499e521c114b54594 lichong <<EMAIL>> 1748416856 +0800	merge origin/develop-v6.5.0-jdk17-spring3.4.5: Fast-forward
91c947dd78f6d5c4842e5d2499e521c114b54594 cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 lichong <<EMAIL>> 1748416870 +0800	reset: moving to cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9
cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 68bf88d0fa8cda54f073f1bc36c433d12977c975 lichong <<EMAIL>> 1748416881 +0800	commit: fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】
68bf88d0fa8cda54f073f1bc36c433d12977c975 cf63cbd9c6c349a4dd0de8be695e2040163110f5 lichong <<EMAIL>> 1748416901 +0800	merge origin/develop-v6.5.0-jdk17-spring3.4.5: Merge made by the 'ort' strategy.
cf63cbd9c6c349a4dd0de8be695e2040163110f5 cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 lichong <<EMAIL>> 1748416952 +0800	reset: moving to cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9
cdc1b09f46a1a81ec4cd69b28a15afa2a54ac7b9 a60a2260728d07c9bf25c8502800929f593a75b4 lichong <<EMAIL>> 1748416994 +0800	commit: fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】
a60a2260728d07c9bf25c8502800929f593a75b4 663afd36b16274f1dcda0058f7658260b7b69b19 lichong <<EMAIL>> 1748420880 +0800	commit: fix：修复修改流程名称导致权限查询接口报错问题。tapd:【143全局搜索跟我的空间数据不一致，部分视图点击提示视图已删除】
663afd36b16274f1dcda0058f7658260b7b69b19 80f4e0238c108632e3aba95c2ce6e05bd48da6b6 lichong <<EMAIL>> 1748485932 +0800	commit: fix：解决初始化索引报错bug
80f4e0238c108632e3aba95c2ce6e05bd48da6b6 285fb0daa0132dbf2c05e7943d089edefc87d5c0 lichong <<EMAIL>> 1748490293 +0800	pull: Merge made by the 'ort' strategy.
285fb0daa0132dbf2c05e7943d089edefc87d5c0 80f4e0238c108632e3aba95c2ce6e05bd48da6b6 lichong <<EMAIL>> 1748497041 +0800	reset: moving to 80f4e0238c108632e3aba95c2ce6e05bd48da6b6
80f4e0238c108632e3aba95c2ce6e05bd48da6b6 a22584a7ef03d12ccc103ee9389694c42a6ffe8a lichong <<EMAIL>> 1748497053 +0800	merge origin/develop-v6.5.0-jdk17-spring3.4.5: Merge made by the 'ort' strategy.
a22584a7ef03d12ccc103ee9389694c42a6ffe8a a813c8a35083e82cb9536643d0b1bced37781f7b lichong <<EMAIL>> 1748498499 +0800	commit: fix：解决初始化索引报错bug。tapd：【【中信建投证券问题同步】信息资产管理分类列表配置保存时报错，首次选择时需要将所有字段勾选上，保存成功一次后面就不会再有问题】
a813c8a35083e82cb9536643d0b1bced37781f7b 71eaca28288f6e376a6f1aad98958543de29907f lichong <<EMAIL>> 1748503219 +0800	commit: fix：修改数据集未配置提示描述。tapd：【数据集停用/删除，架构地图及专题分析提示】
71eaca28288f6e376a6f1aad98958543de29907f a01bb58ff409bf1eeb786dafbb11c4dd5dd861d9 lichong <<EMAIL>> 1748511735 +0800	commit: fix：修复er图转换报错bug。【ER图转换报错，跟端到端流程，批量保存报错类似】
a01bb58ff409bf1eeb786dafbb11c4dd5dd861d9 897fd4ef3462d1e1d6f7f36f099adfbc8efdf2bd lichong <<EMAIL>> 1748513246 +0800	commit: fix：视图关系查询添加查询条件，分享情况下可以查询到。【用户A绘制视图V1无关系，v2有关系，视图分享给用户B,视图历史版本还原,B先将视图还原VI,再还原V2，还原后的视图，没有关系数据】
897fd4ef3462d1e1d6f7f36f099adfbc8efdf2bd c9bdec2436e9339b0dbfd8ef8894c671eb521511 lichong <<EMAIL>> 1748517452 +0800	commit: fix：修复关系列表无数据bug。tapd：【【关系管理】所属用户列无数据】
c9bdec2436e9339b0dbfd8ef8894c671eb521511 a479263f803768b8d1d7613a09322183716c0048 lichong <<EMAIL>> 1748520109 +0800	commit: feat：新增关系数据检查接口，删除前调用，替换原来的searchRltByBean接口
a479263f803768b8d1d7613a09322183716c0048 9f76bf0db05d012a96eab1027b287f3e3ea48868 lichong <<EMAIL>> 1748571986 +0800	commit: fix：修改元模型初始化菜单。【【元模型】元模型重构】
9f76bf0db05d012a96eab1027b287f3e3ea48868 1b781354a311aea63819fd25832a3f657ca863d2 lichong <<EMAIL>> 1748573223 +0800	commit: fix：修改对象管理初始化菜单。
