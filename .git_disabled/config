[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = ****************:eam/eam-app.git
	fetch = +refs/heads/*:refs/remotes/origin/*
	puttykeyfile = C:\\Users\\<USER>\\.ssh\\id_rsa.ppk
[branch "develop-v6.5.0-jdk17-spring3.4.5"]
	remote = origin
	merge = refs/heads/develop-v6.5.0-jdk17-spring3.4.5
	vscode-merge-base = origin/develop-v6.5.0-jdk17-spring3.4.5
[branch "develop-v6.5.0-lichong"]
	remote = origin
	merge = refs/heads/develop-v6.5.0-lichong
	vscode-merge-base = origin/develop-v6.5.0
[branch "develop-v6.3.0"]
	remote = origin
	merge = refs/heads/develop-v6.3.0
[branch "develop-v6.3.0-lichong"]
	remote = origin
	merge = refs/heads/develop-v6.3.0-lichong
	vscode-merge-base = origin/develop-v6.3.0
