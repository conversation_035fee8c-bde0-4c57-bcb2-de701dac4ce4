[{"id": 100001, "dirName": "架构原则、愿景、规划", "dirLvl": 1, "orderNo": 1, "domainId": 1, "parentId": 0, "dirType": 1, "isLeaf": 0, "userId": 1, "dataStatus": 1, "modifier": "admin", "modifyTime": 20210901010000, "createTime": 20210901010000, "dirPath": "#100001#"}, {"id": 100002, "dirName": "业务架构", "dirLvl": 1, "orderNo": 2, "domainId": 1, "parentId": 0, "dirType": 1, "isLeaf": 0, "userId": 1, "dataStatus": 1, "modifier": "admin", "modifyTime": 20210901010000, "createTime": 20210901010000, "dirPath": "#100002#"}, {"id": 100003, "dirName": "数据架构", "dirLvl": 1, "orderNo": 3, "domainId": 1, "parentId": 0, "dirType": 1, "isLeaf": 0, "userId": 1, "dataStatus": 1, "modifier": "admin", "modifyTime": 20210901010000, "createTime": 20210901010000, "dirPath": "#100003#"}, {"id": 100004, "dirName": "应用架构", "dirLvl": 1, "orderNo": 4, "domainId": 1, "parentId": 0, "dirType": 1, "isLeaf": 0, "userId": 1, "dataStatus": 1, "modifier": "admin", "modifyTime": 20210901010000, "createTime": 20210901010000, "dirPath": "#100004#"}, {"id": 100005, "dirName": "技术架构", "dirLvl": 1, "orderNo": 5, "domainId": 1, "parentId": 0, "dirType": 1, "isLeaf": 0, "userId": 1, "dataStatus": 1, "modifier": "admin", "modifyTime": 20210901010000, "createTime": 20210901010000, "dirPath": "#100005#"}, {"id": 100006, "dirName": "架构实现", "dirLvl": 1, "orderNo": 6, "domainId": 1, "parentId": 0, "dirType": 1, "isLeaf": 0, "userId": 1, "dataStatus": 1, "modifier": "admin", "modifyTime": 20210901010000, "createTime": 20210901010000, "dirPath": "#100006#"}, {"modifier": "system", "dataStatus": 1, "dirType": -3, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -3, "dirName": "技术架构资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -2, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -2, "dirName": "数据架构资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -1, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": 1, "dirName": "应用架构资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -4, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -4, "dirName": "其他业务架构资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -1, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -1, "dirName": "资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -2, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -2, "dirName": "资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -3, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -3, "dirName": "资产仓库"}, {"modifier": "system", "dataStatus": 1, "dirType": -4, "isLeaf": 0, "userId": 1, "domainId": 1, "parentId": -1, "modifyTime": 20220226181440, "dirLvl": 1, "createTime": 20210901010000, "dirPath": "#-1#", "id": -4, "dirName": "资产仓库"}]