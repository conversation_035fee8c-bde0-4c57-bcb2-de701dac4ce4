[{"name": "tarsier-eam", "permitAllUrls": ["/eam/workbenchChargeDone/changeAction", "/eam/workbenchChargeDone/saveOrUpdate", "/sync/syncUserDataBatchToEa", "/permission/oauth/resource/cleanOnlineUser", "/wiki/getTokenByCode", "/login/getLoginMethod", "/cj/system/diagram/changeFlowByDiagramIds", "/planDesign/updatePlanDiagramIsFlow", "/redirectAuth", "/getTokenByCode", "/refreshToken", "/cmdb/dataSet/execute", "/eam/user/getUserByRoleName", "/cmdb/dataSet/realTimeExecute", "/websocket/*/*", "/eam/oauth/getTokenByLoginInfo", "/trial/saas/login/check", "/eam/notice/workflow/msg/save", "/planDesign/getPlanForFeign", "/planDesign/findRenewVersionPlanList", "/flowable/getApprovalUser", "/flowable/approval/task", "/flowable/batchModifyWorkbenchTask", "/rsm/**"], "urlSecureConfigs": [{"urls": ["/permission/user/getAuth", "/permission/user/getMenuTree", "/permission/user/getAuth"]}, {"urls": ["/**"], "scope": "tarsier"}]}, {"name": "tarsier-diagram", "permitAllUrls": ["/login/getLoginMethod", "/planDesign/updatePlanDiagramIsFlow", "/redirectAuth", "/getTokenByCode", "/refreshToken", "/cmdb/dataSet/execute", "/cmdb/dataSet/realTimeExecute", "/websocket/*/*"], "urlSecureConfigs": [{"urls": ["/permission/user/getAuth", "/permission/user/getMenuTree", "/permission/user/getAuth"]}, {"urls": ["/**"], "scope": "tarsier"}]}, {"name": "tarsier-cj", "permitAllUrls": ["/planDesign/updatePlanDiagramIsFlow"], "urlSecureConfigs": [{"urls": ["/permission/user/getAuth", "/permission/user/getMenuTree", "/permission/user/getAuth"]}, {"urls": ["/**"], "scope": "tarsier"}]}]